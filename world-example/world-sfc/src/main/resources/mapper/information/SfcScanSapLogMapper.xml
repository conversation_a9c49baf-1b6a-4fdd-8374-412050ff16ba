<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.sfc.mapper.SfcScanSapLogMapper">

    <resultMap id="BaseResultMap" type="org.dromara.sfc.domain.SfcScanSapLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="REFERORDER" column="REFERORDER" jdbcType="VARCHAR"/>
            <result property="DATA" column="DATA" jdbcType="VARCHAR"/>
            <result property="MESSAGE" column="MESSAGE" jdbcType="VARCHAR"/>
            <result property="tenant_id" column="tenant_id" jdbcType="INTEGER"/>
            <result property="created_at" column="created_at" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,REFERORDER,DATA,
        MESSAGE,tenant_id,created_at
    </sql>
</mapper>
