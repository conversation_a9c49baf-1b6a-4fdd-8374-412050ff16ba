<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.sfc.mapper.SfcBarcodeMapper">

    <select id="getAbnormalData" resultType="org.dromara.sfc.domain.SfcBarcode">
        SELECT sb.*
        FROM sfc_barcode sb
        WHERE LENGTH(sb.mo_no) = 12
          AND sb.end_date IS NULL
          AND NOT EXISTS (
            SELECT 1
            FROM sfc_info si
            WHERE si.mo_no = sb.mo_no
              AND si.mo_no != ''
              AND (si.act14 NOT IN ('删除', '关闭', '出货') OR si.act14 IS NULL)
        )
    </select>

</mapper>
