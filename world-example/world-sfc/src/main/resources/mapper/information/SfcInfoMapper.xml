<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.sfc.mapper.SfcInfoMapper">
    <update id="updateAct14">
        UPDATE sfc_info AS a
            JOIN sap_scan_master AS b
            ON a.mo_no = b.MO_NO
        SET a.act14 = b.mo_state
        WHERE COALESCE(a.act14, '') <![CDATA[<>]]> '出货';
    </update>

    <select id="getNoFinish" resultType="org.dromara.sfc.domain.dto.InfoNoFinishDTO">
        SELECT mo_no                      AS moNo,
               zp_type                    AS zpType,
               bar_code                   AS barCode,
               MAX(COALESCE(sum_prod, 0)) AS maxProd
        FROM sfc_info
        WHERE b_dtm IS NOT NULL
          AND e_dtm IS NOT NULL
          AND zp_type NOT IN ('IPQC检验', '调试')
          AND (act14 NOT IN ('删除', '关闭', '出货') OR act14 IS NULL)
        GROUP BY mo_no, zp_type, bar_code
        HAVING MAX(COALESCE(sum_prod, 0)) <![CDATA[<]]> 100
    </select>

    <select id="selectGsStatPage" resultType="java.util.Map">
        SELECT
        mo_no,
        zp_type,
        SUM(COALESCE(bas_gs, 0)) as bas_gs_all,
        SUM(COALESCE(sj_gs, 0)) as sj_gs_all
        FROM sfc_info
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY id
    </select>

    <select id="selectGs2StatPage" resultType="java.util.Map">
        SELECT
        mo_no,
        zp_type,
        p_name,
        SUM(COALESCE(bas_gs, 0)) as bas_gs_all,
        SUM(COALESCE(sj_gs, 0)) as sj_gs_all
        FROM sfc_info
        <where>
            ${ew.sqlSegment}
        </where>
        GROUP BY id
    </select>


    <select id="selectGs3StatPage" resultType="java.util.Map">
        SELECT
        mo_no,
        zp_type,
        p_name,
        b_dtm,
        e_dtm,
        sj_gs,
        emp_no,
        emp_name
        FROM sfc_info
        <where>
            ${ew.sqlSegment}
        </where>
        ORDER BY id DESC
    </select>
</mapper>
