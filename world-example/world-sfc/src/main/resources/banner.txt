Spring Boot Version: ${spring-boot.version}
Spring Application Name: ${spring.application.name}
                     _       _               ___
                    (_ )    ( )            /'___)
 _   _   _   _   _ __| |   _| |______  ___| (__  ___
( ) ( ) ( )/'_`\( '__| | /'_` (______/',__| ,__/'___)
| \_/ \_/ ( (_) | |  | |( (_| |      \__, | | ( (___
`\___x___/`\___/(_) (___`\__,_)      (____(_) `\____)


