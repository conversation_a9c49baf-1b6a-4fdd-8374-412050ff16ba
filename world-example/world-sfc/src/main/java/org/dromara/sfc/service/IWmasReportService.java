package org.dromara.sfc.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.WmasReport;
import org.dromara.sfc.domain.bo.WmasReportBo;
import org.dromara.sfc.domain.vo.WmasReportVo;
import org.springframework.data.domain.Pageable;

/**
 * 样品清单维护Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface IWmasReportService {

    /**
     * 查询样品清单维护
     *
     * @param id 主键
     * @return 样品清单维护
     */
    WmasReportVo queryById(Long id);

    /**
     * 分页查询样品清单维护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 样品清单维护分页列表
     */
    TableDataInfo<WmasReportVo> queryPageList(WmasReportBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的样品清单维护列表
     *
     * @param bo 查询条件
     * @return 样品清单维护列表
     */
    List<WmasReportVo> queryList(WmasReportBo bo);

    /**
     * 新增样品清单维护
     *
     * @param bo 样品清单维护
     * @return 是否新增成功
     */
    Boolean insertByBo(WmasReportBo bo);

    /**
     * 修改样品清单维护
     *
     * @param bo 样品清单维护
     * @return 是否修改成功
     */
    Boolean updateByBo(WmasReportBo bo);

    /**
     * 校验并批量删除样品清单维护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R search(Map<String, Object> params, PageQuery pageQuery);

    R saveWmasReports(List<WmasReport> wmasReportList);

    R getMoGx(String moNo, Pageable pageable);

    R getPicUrl(String moNo);
}
