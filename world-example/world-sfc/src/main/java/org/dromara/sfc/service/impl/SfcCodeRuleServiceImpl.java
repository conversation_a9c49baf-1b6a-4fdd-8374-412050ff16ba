package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcCodeRule;
import org.dromara.sfc.domain.bo.SfcCodeRuleBo;
import org.dromara.sfc.domain.vo.SfcCodeRuleVo;
import org.dromara.sfc.mapper.SfcCodeRuleMapper;
import org.dromara.sfc.service.ISfcCodeRuleService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 类别设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@RequiredArgsConstructor
@Service
public class SfcCodeRuleServiceImpl implements ISfcCodeRuleService {

    @Resource
    private final SfcCodeRuleMapper baseMapper;

    /**
     * 查询类别设置
     *
     * @param id 主键
     * @return 类别设置
     */
    @Override
    public SfcCodeRuleVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询类别设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 类别设置分页列表
     */
    @Override
    public TableDataInfo<SfcCodeRuleVo> queryPageList(SfcCodeRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcCodeRule> lqw = buildQueryWrapper(bo);
        Page<SfcCodeRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的类别设置列表
     *
     * @param bo 查询条件
     * @return 类别设置列表
     */
    @Override
    public List<SfcCodeRuleVo> queryList(SfcCodeRuleBo bo) {
        LambdaQueryWrapper<SfcCodeRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcCodeRule> buildQueryWrapper(SfcCodeRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcCodeRule> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCodeType()), SfcCodeRule::getCodeType, bo.getCodeType());
        lqw.like(StringUtils.isNotBlank(bo.getAcnCode()), SfcCodeRule::getAcnCode, bo.getAcnCode());
        lqw.like(StringUtils.isNotBlank(bo.getAcnDesc()), SfcCodeRule::getAcnDesc, bo.getAcnDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getEnab()), SfcCodeRule::getEnab, bo.getEnab());
        lqw.like(StringUtils.isNotBlank(bo.getRem()), SfcCodeRule::getRem, bo.getRem());
        return lqw;
    }

    /**
     * 新增类别设置
     *
     * @param bo 类别设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcCodeRuleBo bo) {
        SfcCodeRule add = MapstructUtils.convert(bo, SfcCodeRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改类别设置
     *
     * @param bo 类别设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcCodeRuleBo bo) {
        SfcCodeRule update = MapstructUtils.convert(bo, SfcCodeRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcCodeRule entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除类别设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 查询类别设置列表
     *
     * @param codeType
     * @return
     */
    @Override
    public List<SfcCodeRuleVo> selectList(String codeType) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<SfcCodeRule>()
            .eq(SfcCodeRule::getCodeType, codeType).eq(SfcCodeRule::getEnab, "1"));
    }

    /**
     * 获取条码类型
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getBarcodeTypes() {
        // 从数据库获取所有条码规则
        List<SfcCodeRule> codeRules = baseMapper.selectList();
        // 根据类型分组并提取条码类型
        return codeRules.stream()
            .filter(codeRule -> codeRule.getCodeType() != null) // 过滤掉 type 为 null 的对象
            .collect(Collectors.groupingBy(SfcCodeRule::getCodeType)) // 根据类型分组
            .entrySet()
            .stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("type", entry.getKey()); // 条码类型
                map.put("count", entry.getValue().size()); // 该类型的数量
                // 可以根据需要添加更多信息
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getBarcodeAncTypes() {
        // 从数据库获取所有条码规则
        List<SfcCodeRule> codeRules = baseMapper.selectList();
        // 根据类型分组并提取条码类型
        return codeRules.stream()
            .filter(codeRule -> codeRule.getAcnCode() != null) // 过滤掉 type 为 null 的对象
            .collect(Collectors.groupingBy(SfcCodeRule::getAcnCode)) // 根据类型分组
            .entrySet()
            .stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("type", entry.getKey()); // 条码类型
                map.put("count", entry.getValue().size()); // 该类型的数量
                // 可以根据需要添加更多信息
                return map;
            })
            .collect(Collectors.toList());
    }
}
