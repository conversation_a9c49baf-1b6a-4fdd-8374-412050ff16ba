package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcRygstjbBo;
import org.dromara.sfc.domain.vo.SfcRygstjbVo;
import org.dromara.sfc.service.ISfcRygstjbService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

/**
 * 装配工时统计表
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/rygstjb")
public class SfcRygstjbController extends BaseController {

    private final ISfcRygstjbService sfcRygstjbService;

    /**
     * 查询装配工时统计表列表
     */
    @SaCheckPermission("information:rygstjb:list")
    @GetMapping("/list")
    public TableDataInfo<SfcRygstjbVo> list(SfcRygstjbBo bo, PageQuery pageQuery) {
        return sfcRygstjbService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出装配工时统计表列表
     */
    @SaCheckPermission("information:rygstjb:export")
    @Log(title = "装配工时统计表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcRygstjbBo bo, HttpServletResponse response) {
        List<SfcRygstjbVo> list = sfcRygstjbService.queryList(bo);
        ExcelUtil.exportExcel(list, "装配工时统计表", SfcRygstjbVo.class, response);
    }

    /**
     * 获取装配工时统计表详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:rygstjb:query")
    @GetMapping("/{id}")
    public R<SfcRygstjbVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(sfcRygstjbService.queryById(id));
    }

    /**
     * 新增装配工时统计表
     */
    @SaCheckPermission("information:rygstjb:add")
    @Log(title = "装配工时统计表", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcRygstjbBo bo) {
        return toAjax(sfcRygstjbService.insertByBo(bo));
    }

    /**
     * 修改装配工时统计表
     */
    @SaCheckPermission("information:rygstjb:edit")
    @Log(title = "装配工时统计表", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcRygstjbBo bo) {
        return toAjax(sfcRygstjbService.updateByBo(bo));
    }

    /**
     * 删除装配工时统计表
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:rygstjb:remove")
    @Log(title = "装配工时统计表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcRygstjbService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 装配工时统计
     *
     * @param bo 参数
     */
    @Log(title = "装配工时统计", businessType = BusinessType.OTHER)
    @GetMapping("/statistic")
    public R searchReport(SfcRygstjbBo bo) throws SQLException {
        sfcRygstjbService.statistic(bo);
        return R.ok();
    }

}
