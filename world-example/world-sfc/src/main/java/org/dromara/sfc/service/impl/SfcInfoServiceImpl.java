package org.dromara.sfc.service.impl;

import java.sql.Connection;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.sap.util.SapUtils;
import org.dromara.sfc.domain.SapScanMasterSn;
import org.dromara.sfc.domain.SfcBarcode;
import org.dromara.sfc.domain.SfcCodeRule;
import org.dromara.sfc.domain.SfcInfo;
import org.dromara.sfc.domain.SfcIpqcEmp;
import org.dromara.sfc.domain.SfcScanSapLog;
import org.dromara.sfc.domain.bo.SfcInfoBo;
import org.dromara.sfc.domain.bo.SfcInfoQueryBo;
import org.dromara.sfc.domain.dto.InfoNoFinishDTO;
import org.dromara.sfc.domain.dto.InfoReportGs2DTO;
import org.dromara.sfc.domain.dto.InfoReportGs3DTO;
import org.dromara.sfc.domain.dto.InfoReportGsDTO;
import org.dromara.sfc.domain.vo.SfcInfoVo;
import org.dromara.sfc.mapper.SapScanMasterSnMapper;
import org.dromara.sfc.mapper.SfcBarcodeMapper;
import org.dromara.sfc.mapper.SfcCodeRuleMapper;
import org.dromara.sfc.mapper.SfcInfoMapper;
import org.dromara.sfc.mapper.SfcIpqcEmpMapper;
import org.dromara.sfc.mapper.SfcScanSapLogMapper;
import org.dromara.sfc.service.ISfcInfoService;
import org.dromara.sfc.utils.AsyncManager;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.microsoft.sqlserver.jdbc.SQLServerException;
import com.sap.conn.jco.JCoException;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 报工扫描记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Slf4j
@Service
public class SfcInfoServiceImpl extends ServiceImpl<SfcInfoMapper, SfcInfo> implements ISfcInfoService {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String CACHE_KEY = "sfc:info:nofinish:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(15);
    /**
     * <a-option>机构装配</a-option> <a-option>电气装配</a-option> <a-option>组件检验</a-option> <a-option>整机检验</a-option>
     */
    private static final List<String> ZP_TYPE_EQ_LIST = List.of("机构装配", "电气装配", "组件检验", "整机检验");
    @DubboReference
    private RemoteUserService remoteUserService;
    @Resource
    private SfcInfoMapper sfcInfoMapper;
    @Resource
    private SapScanMasterSnMapper sapScanMasterSnMapper;
    @Resource
    private SfcCodeRuleMapper sfcCodeRuleMapper;
    @Resource
    private SfcIpqcEmpMapper sfcIpqcEmpMapper;
    @Resource
    private SfcScanSapLogMapper sfcScanSapLogMapper;
    @Resource
    private SfcBarcodeMapper sfcBarcodeMapper;

    /**
     * 查询报工扫描记录
     *
     * @param id 主键
     * @return 报工扫描记录
     */
    @Override
    public SfcInfoVo queryById(Long id) {
        return sfcInfoMapper.selectVoById(id);
    }

    /**
     * 分页查询报工扫描记录列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 报工扫描记录分页列表
     */
    @Override
    public TableDataInfo<SfcInfoVo> queryPageList(SfcInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcInfo> lqw = buildQueryWrapper(bo);
        Page<SfcInfoVo> result = sfcInfoMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的报工扫描记录列表
     *
     * @param bo 查询条件
     * @return 报工扫描记录列表
     */
    @Override
    public List<SfcInfoVo> queryList(SfcInfoBo bo) {
        LambdaQueryWrapper<SfcInfo> lqw = buildQueryWrapper(bo);
        return sfcInfoMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcInfo> buildQueryWrapper(SfcInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SfcInfo::getId);
        lqw.between(params.get("beginBDtm") != null && params.get("endBDtm") != null, SfcInfo::getBDtm,
            params.get("beginBDtm"), params.get("endBDtm"));
        lqw.eq(StringUtils.isNotBlank(bo.getZpType()), SfcInfo::getZpType, bo.getZpType());

        // 基本信息查询
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), SfcInfo::getMoNo, bo.getMoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSoNo()), SfcInfo::getSoNo, bo.getSoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getSoItm()), SfcInfo::getSoItm, bo.getSoItm());
        lqw.eq(StringUtils.isNotBlank(bo.getHMoNo()), SfcInfo::getHMoNo, bo.getHMoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDrNo()), SfcInfo::getDrNo, bo.getDrNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDrItm()), SfcInfo::getDrItm, bo.getDrItm());

        // 产品相关查询
        lqw.eq(StringUtils.isNotBlank(bo.getPrdNo()), SfcInfo::getPrdNo, bo.getPrdNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdType()), SfcInfo::getPrdType, bo.getPrdType());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdName()), SfcInfo::getPrdName, bo.getPrdName());
        lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), SfcInfo::getCustNo, bo.getCustNo());

        // 工站和进度相关
        lqw.eq(StringUtils.isNotBlank(bo.getFloorNo()), SfcInfo::getFloorNo, bo.getFloorNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPType()), SfcInfo::getPType, bo.getPType());
        lqw.eq(StringUtils.isNotBlank(bo.getPName()), SfcInfo::getPName, bo.getPName());
        lqw.eq(StringUtils.isNotBlank(bo.getWksDwg()), SfcInfo::getWksDwg, bo.getWksDwg());

        // 人员相关
        lqw.eq(StringUtils.isNotBlank(bo.getEmpNo()), SfcInfo::getEmpNo, bo.getEmpNo());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpName()), SfcInfo::getEmpName, bo.getEmpName());
        lqw.eq(StringUtils.isNotBlank(bo.getDep()), SfcInfo::getDep, bo.getDep());
        lqw.eq(StringUtils.isNotBlank(bo.getSType()), SfcInfo::getSType, bo.getSType());
        lqw.eq(bo.getJjzjRy() != null, SfcInfo::getJjzjRy, bo.getJjzjRy());

        // 条码相关
        lqw.eq(StringUtils.isNotBlank(bo.getBarCode()), SfcInfo::getBarCode, bo.getBarCode());
        lqw.eq(StringUtils.isNotBlank(bo.getMzBarcode()), SfcInfo::getMzBarcode, bo.getMzBarcode());

        // 状态相关
        lqw.eq(StringUtils.isNotBlank(bo.getMSta()), SfcInfo::getMSta, bo.getMSta());
        lqw.eq(StringUtils.isNotBlank(bo.getAct14()), SfcInfo::getAct14, bo.getAct14());

        // 其他条件
        lqw.eq(bo.getFlag() != null, SfcInfo::getFlag, bo.getFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getArbpl()), SfcInfo::getArbpl, bo.getArbpl());

        return lqw;
    }

    /**
     * 新增报工扫描记录
     *
     * @param bo 报工扫描记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcInfoBo bo) {
        SfcInfo add = MapstructUtils.convert(bo, SfcInfo.class);
        validEntityBeforeSave(add);
        boolean flag = sfcInfoMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改报工扫描记录
     *
     * @param bo 报工扫描记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcInfoBo bo) {
        SfcInfo update = MapstructUtils.convert(bo, SfcInfo.class);
        validEntityBeforeSave(update);
        return sfcInfoMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcInfo entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除报工扫描记录信息
     *
     * @param ids 待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return sfcInfoMapper.deleteByIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SfcInfoVo> searchReport(SfcInfoQueryBo bo, PageQuery pageQuery) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(SfcInfo::getId);
        queryWrapper.between(params.get("beginBDtm") != null && params.get("endBDtm") != null, SfcInfo::getBDtm,
            params.get("beginBDtm"), params.get("endBDtm"));

        // 更新act14字段的状态
        sfcInfoMapper.updateAct14();

        String msta = bo.getMsta();
        if (StringUtils.isNotBlank(msta)) {
            switch (msta) {
                case "未出货":
                    queryWrapper.in(SfcInfo::getMSta, "执行中", "已完成", "已结束");
                    break;
                case "出货":
                    queryWrapper.in(SfcInfo::getAct14, "出货");
                    break;
                case "全部":
                    queryWrapper.and(
                        wrapper -> wrapper.in(SfcInfo::getMSta, "执行中", "已完成", "已结束").or().eq(SfcInfo::getAct14, "出货"));
                    break;
                default:
                    queryWrapper.eq(SfcInfo::getMSta, msta);
            }
        }

        String zpType = bo.getZpType();
        if (StringUtils.isNotBlank(zpType)) {
            queryWrapper.in(SfcInfo::getZpType, (Object[])zpType.split(","));
        }

        String[] selectTypes = {bo.getSelectType1(), bo.getSelectType2(), bo.getSelectType3()};
        Object[] selectValues = {bo.getSelectVal1(), bo.getSelectVal2(), bo.getSelectVal3()};
        for (int i = 0; i < selectTypes.length; i++) {
            dealSelectParam(queryWrapper, selectTypes[i], selectValues[i]);
        }

        Page<SfcInfoVo> result = sfcInfoMapper.selectVoPage(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(result);
    }

    @Override
    public List<SfcInfoVo> searchReport(SfcInfoQueryBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcInfo> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.orderByAsc(SfcInfo::getId);
        queryWrapper.between(params.get("beginBDtm") != null && params.get("endBDtm") != null, SfcInfo::getBDtm,
            params.get("beginBDtm"), params.get("endBDtm"));

        // 更新act14字段的状态
        sfcInfoMapper.updateAct14();

        String msta = bo.getMsta();
        if (StringUtils.isNotBlank(msta)) {
            switch (msta) {
                case "未出货":
                    queryWrapper.in(SfcInfo::getMSta, "执行中", "已完成", "已结束");
                    break;
                case "出货":
                    queryWrapper.in(SfcInfo::getAct14, "出货");
                    break;
                case "全部":
                    queryWrapper.and(
                        wrapper -> wrapper.in(SfcInfo::getMSta, "执行中", "已完成", "已结束").or().eq(SfcInfo::getAct14, "出货"));
                    break;
                default:
                    queryWrapper.eq(SfcInfo::getMSta, msta);
            }
        }

        String zpType = bo.getZpType();
        if (StringUtils.isNotBlank(zpType)) {
            queryWrapper.in(SfcInfo::getZpType, (Object[])zpType.split(","));
        }

        String[] selectTypes = {bo.getSelectType1(), bo.getSelectType2(), bo.getSelectType3()};
        Object[] selectValues = {bo.getSelectVal1(), bo.getSelectVal2(), bo.getSelectVal3()};
        for (int i = 0; i < selectTypes.length; i++) {
            dealSelectParam(queryWrapper, selectTypes[i], selectValues[i]);
        }
        params.forEach((k, v) -> dealSelectParam(queryWrapper, k, v));
        return sfcInfoMapper.selectVoList(queryWrapper);
    }

    private void dealSelectParam(LambdaQueryWrapper<SfcInfo> queryWrapper, String selectType, Object selectValue) {
        if (!ObjectUtils.isEmpty(selectType) && !ObjectUtils.isEmpty(selectValue)) {
            switch (selectType) {
                case "工号":
                    queryWrapper.eq(SfcInfo::getEmpNo, selectValue);
                    break;
                case "参与人员工号":
                    queryWrapper.like(SfcInfo::getParticipantNo, selectValue).eq(SfcInfo::getEmpNo, selectValue);
                    break;
                case "管理工号":
                    handleUserQuery(queryWrapper, selectValue);
                    break;
                case "产品名称":
                    queryWrapper.eq(SfcInfo::getCusName, selectValue);
                    break;
                case "图号":
                    queryWrapper.eq(SfcInfo::getWksDwg, selectValue);
                    break;
                case "MO号":
                    queryWrapper.eq(SfcInfo::getMoNo, selectValue);
                    break;
                case "设备项次":
                    queryWrapper.eq(SfcInfo::getSopBatno, selectValue);
                    break;
                case "条码":
                    queryWrapper.eq(SfcInfo::getBarCode, selectValue);
                    break;
                case "设备SN":
                case "WD_SN":
                    handleSnQuery(queryWrapper, selectType, selectValue);
                    break;
                case "工站名称":
                    queryWrapper.eq(SfcInfo::getPName, selectValue);
                    break;
                case "楼层":
                    queryWrapper.eq(SfcInfo::getFloorNo, selectValue);
                    break;
                default:
                    break;
            }
        }
    }

    private void handleUserQuery(LambdaQueryWrapper<SfcInfo> queryWrapper, Object selectValue) {
        List<RemoteUserVo> remoteUserVos = remoteUserService.selectUsersByUsername(selectValue.toString());
        if (!remoteUserVos.isEmpty()) {
            List<String> empNos = remoteUserVos.stream().map(RemoteUserVo::getUserName).collect(Collectors.toList());
            queryWrapper.in(SfcInfo::getEmpNo, empNos);
        } else {
            queryWrapper.eq(SfcInfo::getEmpNo, selectValue);
        }
    }

    private void handleSnQuery(LambdaQueryWrapper<SfcInfo> queryWrapper, String selectType, Object selectValue) {
        LambdaQueryWrapper<SapScanMasterSn> snQueryWrapper = new LambdaQueryWrapper<>();
        if ("设备SN".equals(selectType)) {
            snQueryWrapper.eq(SapScanMasterSn::getCusSn, selectValue);
        } else {
            snQueryWrapper.eq(SapScanMasterSn::getSn, selectValue);
        }
        List<SapScanMasterSn> snList = sapScanMasterSnMapper.selectList(snQueryWrapper);
        if (!ObjectUtils.isEmpty(snList)) {
            queryWrapper.eq(SfcInfo::getMoNo, snList.get(0).getMoNo());
        }
    }

    @Override
    public List<InfoNoFinishDTO> getNoFinish() {
        try {
            // 1. 尝试从缓存获取
            String cacheKey = CACHE_KEY + "list";
            List<InfoNoFinishDTO> cachedData = RedisUtils.getCacheObject(cacheKey);
            if (cachedData != null) {
                return cachedData;
            }
            // 2. 从数据库查询
            List<InfoNoFinishDTO> result = sfcInfoMapper.getNoFinish();
            // 3. 存入缓存
            if (CollUtil.isNotEmpty(result)) {
                RedisUtils.setCacheObject(cacheKey, result, CACHE_TTL);
            }
            return result;
        } catch (Exception e) {
            throw new ServiceException("获取未完成数据失败");
        }
    }

    @Override
    public TableDataInfo<InfoReportGsDTO> searchReportGs(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        // 构建查询条件
        QueryWrapper<SfcInfo> queryWrapper = new QueryWrapper<>();
        // 添加基础查询条件
        Map<String, Object> params = sfcInfoQueryBo.getParams();
        if (params.get("beginBDtm") != null && params.get("endBDtm") != null) {
            queryWrapper.between("b_dtm", params.get("beginBDtm"), params.get("endBDtm"));
        }
        // 其他查询条件
        if (StringUtils.isNotEmpty(sfcInfoQueryBo.getZpType())) {
            queryWrapper.in("zp_type", Arrays.asList(sfcInfoQueryBo.getZpType().split(",")));
        }
        // 执行分页查询
        IPage<Map<String, Object>> page = sfcInfoMapper.selectGsStatPage(pageQuery.build(), queryWrapper);
        // 转换为DTO对象
        List<InfoReportGsDTO> records = page.getRecords().stream().map(map -> {
            InfoReportGsDTO dto = new InfoReportGsDTO();
            dto.setMoNo((String)map.get("mo_no"));
            dto.setZpType((String)map.get("zp_type"));
            double basGs = ((Number)map.get("bas_gs_all")).doubleValue();
            double sjGs = ((Number)map.get("sj_gs_all")).doubleValue();
            dto.setBasGsAll(NumberUtil.round(basGs, 2).doubleValue());
            dto.setSmGsAll(NumberUtil.round(sjGs, 2).doubleValue());
            dto.setSyGsAll(NumberUtil.round(basGs - sjGs, 2).doubleValue());
            return dto;
        }).collect(Collectors.toList());

        TableDataInfo dataInfo = new TableDataInfo<>(records, page.getTotal());
        return dataInfo;
    }

    @Override
    public TableDataInfo<InfoReportGs2DTO> searchReportGs2(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        // 构建查询条件
        QueryWrapper<SfcInfo> queryWrapper = new QueryWrapper<>();
        // 只添加MO号查询条件
        if (StringUtils.isNotBlank(sfcInfoQueryBo.getSelectType1())
            && StringUtils.isNotBlank(sfcInfoQueryBo.getSelectVal1())
            && "MO号".equals(sfcInfoQueryBo.getSelectType1())) {
            queryWrapper.eq("mo_no", sfcInfoQueryBo.getSelectVal1());
        }
        // 执行分页查询
        IPage<Map<String, Object>> page = sfcInfoMapper.selectGs2StatPage(pageQuery.build(), queryWrapper);

        // 转换为DTO对象
        List<InfoReportGs2DTO> records = page.getRecords().stream().map(map -> {
            InfoReportGs2DTO dto = new InfoReportGs2DTO();
            dto.setMoNo((String)map.get("mo_no"));
            dto.setZpType((String)map.get("zp_type"));
            dto.setPName((String)map.get("p_name"));
            double basGs = ((Number)map.get("bas_gs_all")).doubleValue();
            double sjGs = ((Number)map.get("sj_gs_all")).doubleValue();
            dto.setBasGsAll(NumberUtil.round(basGs, 2).doubleValue());
            dto.setSmGsAll(NumberUtil.round(sjGs, 2).doubleValue());
            dto.setSyGsAll(NumberUtil.round(basGs - sjGs, 2).doubleValue());
            return dto;
        }).collect(Collectors.toList());

        return new TableDataInfo<>(records, page.getTotal());
    }

    @Override
    public TableDataInfo<InfoReportGs3DTO> searchReportGs3(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        // 构建查询条件
        QueryWrapper<SfcInfo> queryWrapper = new QueryWrapper<>();

        // 只添加MO号查询条件
        if (StringUtils.isNotBlank(sfcInfoQueryBo.getSelectType1())
            && StringUtils.isNotBlank(sfcInfoQueryBo.getSelectVal1())
            && "MO号".equals(sfcInfoQueryBo.getSelectType1())) {
            queryWrapper.eq("mo_no", sfcInfoQueryBo.getSelectVal1());
        }
        if (StringUtils.isNotBlank(sfcInfoQueryBo.getSelectType2())
            && StringUtils.isNotBlank(sfcInfoQueryBo.getSelectVal2())
            && "工站名称".equals(sfcInfoQueryBo.getSelectType2())) {
            queryWrapper.eq("p_name", sfcInfoQueryBo.getSelectVal2());
        }

        // 执行分页查询
        IPage<Map<String, Object>> page = sfcInfoMapper.selectGs3StatPage(pageQuery.build(), queryWrapper);

        // 转换为DTO对象
        List<InfoReportGs3DTO> records = page.getRecords().stream().map(map -> {
            InfoReportGs3DTO dto = new InfoReportGs3DTO();
            dto.setMoNo((String)map.get("mo_no"));
            dto.setZpType((String)map.get("zp_type"));
            dto.setPName((String)map.get("p_name"));
            dto.setBDtm((LocalDateTime)map.get("b_dtm"));
            dto.setEDtm((LocalDateTime)map.get("e_dtm"));
            dto.setSjGs(map.get("sj_gs") != null ? ((Number)map.get("sj_gs")).doubleValue() : null);
            dto.setEmpNo((String)map.get("emp_no"));
            dto.setEmpName((String)map.get("emp_name"));
            return dto;
        }).collect(Collectors.toList());
        return new TableDataInfo<>(records, page.getTotal());
    }

    @Override
    public List<SfcInfo> getInfosByBarcodeData(Map<String, String> params) {
        String sopMo = params.get("sopMo");
        LambdaQueryWrapper<SfcBarcode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfcBarcode::getSopMo, sopMo).isNull(SfcBarcode::getEndDate);
        List<SfcBarcode> sfcBarcodes = sfcBarcodeMapper.selectList(queryWrapper);
        if (sfcBarcodes.isEmpty()) {
            log.warn("sopMO条码为空");
            return List.of();
        }
        LambdaQueryWrapper<SfcInfo> infoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        infoLambdaQueryWrapper.in(SfcInfo::getBarCode,
            sfcBarcodes.stream().map(SfcBarcode::getBarCode).collect(Collectors.toSet()));
        return baseMapper.selectList(infoLambdaQueryWrapper);
    }

    @Override
    public R getInfoList(SfcInfo sfcInfo) {
        LambdaQueryWrapper<SfcInfo> queryWrapper = new LambdaQueryWrapper<>();
        if (!ObjectUtils.isEmpty(sfcInfo.getZpType())) {
            queryWrapper.eq(SfcInfo::getZpType, sfcInfo.getZpType());
        }
        if (!ObjectUtils.isEmpty(sfcInfo.getEmpNo())) {
            queryWrapper.eq(SfcInfo::getEmpNo, sfcInfo.getEmpNo());
        }
        if (!ObjectUtils.isEmpty(sfcInfo.getBarCode())) {
            queryWrapper.eq(SfcInfo::getBarCode, sfcInfo.getBarCode());
        }
        List<SfcInfo> sfcInfos = sfcInfoMapper.selectList(queryWrapper);
        if (!ObjectUtils.isEmpty(sfcInfos) && !ObjectUtils.isEmpty(sfcInfo.getBarCode())) {
            LambdaQueryWrapper<SfcCodeRule> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SfcCodeRule::getCodeType, "ZPTP").like(SfcCodeRule::getCodingRule, "BG=N;");
            List<SfcCodeRule> sfcCodeRules = sfcCodeRuleMapper.selectList(wrapper);
            if (!ObjectUtils.isEmpty(sfcCodeRules)) {
                List<String> list = sfcCodeRules.stream().map(SfcCodeRule::getAcnDesc).toList();
                if (list.contains(sfcInfo.getZpType())) {
                    return R.fail("已报工不能重复扫描");
                }
            }

        }
        return R.ok(sfcInfos);
    }

    @Override
    public void checkData(List<SfcInfo> sfcInfos) {
        for (SfcInfo sfcInfo : sfcInfos) {
            if (ObjectUtils.isEmpty(sfcInfo.getEmpNo()) || ObjectUtils.isEmpty(sfcInfo.getBarCode())) {
                throw new RuntimeException("工号或者条码不能为空");
            }
            if (!ObjectUtils.isEmpty(sfcInfo.getMSta())) {
                continue;
            }
            LambdaQueryWrapper<SfcInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SfcInfo::getZpType, sfcInfo.getZpType()).eq(SfcInfo::getBarCode, sfcInfo.getBarCode())
                .eq(SfcInfo::getEmpNo, sfcInfo.getEmpNo()).isNull(SfcInfo::getEDtm);
            List<SfcInfo> infoList = sfcInfoMapper.selectList(wrapper);
            if (!ObjectUtils.isEmpty(infoList)) {
                throw new RuntimeException("不能重复开单");
            }
        }
    }

    @Override
    public List<SfcInfo> filter(List<SfcInfo> sfcInfos) {
        LambdaQueryWrapper<SfcCodeRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SfcCodeRule::getCodeType, "ZPTP").like(SfcCodeRule::getCodingRule, "BG=N;");
        List<SfcCodeRule> sfcCodeRules = sfcCodeRuleMapper.selectList(wrapper);
        if (ObjectUtils.isEmpty(sfcCodeRules)) {
            return sfcInfos;
        }
        List<String> zpList = sfcCodeRules.stream().map(SfcCodeRule::getAcnDesc).toList();
        List<SfcInfo> filterInfos = sfcInfos.stream().filter(en -> zpList.contains(en.getZpType())).toList();
        if (ObjectUtils.isEmpty(filterInfos)) {
            return sfcInfos;
        }
        filterInfos.forEach(en -> {
            en.setBDtm(LocalDateTime.now());
            en.setEDtm(LocalDateTime.now());
            en.setMSta("已结束");
            en.setCurProd(0L);
            en.setSumProd(0L);
            en.setSjGs(0d);
            en.setWkGs(0d);
        });
        saveBatch(filterInfos);

        String sqlInsert = "insert into pp_scan"
            + "(PJ号,工序号,工序名称,操作员,姓名,机床,动作,开始时间,结束时间,通过数量,报废数量,实际时间,备注,加工厂址,图号,部门,ARBPL,dep,bar_code,总进度,当前进度)"
            + " values " + "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        String sqlMaster1 = "update MASTER set 状态=? where mo_no=?";
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        filterInfos.forEach(en -> {
            try {
                List<Object> params = new ArrayList<>();
                params.add(en.getMoNo());
                params.add(en.getPType());
                params.add(en.getPName());
                params.add(en.getEmpNo());
                params.add(en.getEmpName());
                params.add(ObjectUtils.isEmpty(en.getArbpl()) ? "F2ZZ11" : en.getArbpl());
                params.add(en.getZpType());
                params.add(en.getBDtm());
                params.add(en.getEDtm());
                params.add(en.getQtyOk());
                params.add(en.getQtyNg());
                params.add(en.getSjGs());
                params.add(en.getRem());
                params.add("SFC");
                params.add(en.getWksDwg());
                params.add(en.getDep());
                params.add(ObjectUtils.isEmpty(en.getArbpl()) ? "F2ZZ11" : en.getArbpl());
                params.add(en.getDep());
                params.add(en.getBarCode());
                params.add(en.getSumProd());
                params.add(en.getCurProd());
                SqlserverJdbcUtils.executeUpdate(sqlInsert, params);
                SqlserverJdbcUtils.executeUpdate(sqlMaster1,
                    Arrays.asList(en.getPType() + en.getPName() + en.getZpType(), en.getMoNo()));
            } catch (SQLException e) {
                throw new RuntimeException(e);
            }
        });
        return sfcInfos.stream().filter(en -> !zpList.contains(en.getZpType())).toList();
    }

    @Override
    public List<SfcInfo> trans(List<SfcInfo> sfcInfos) {
        boolean matchTs = sfcInfos.stream().allMatch(in -> in.getZpType().equals("调试"));
        LocalDateTime dateTime = LocalDateTime.now();
        sfcInfos.forEach(en -> {
            if (ObjectUtils.isEmpty(en.getMSta())) {
                en.setMSta("执行中");
                en.setBDtm(dateTime);
            } else {
                en.setMSta(matchTs ? "已结束" : "已完成");
                en.setEDtm(dateTime);
            }
            if (en.getCurProd() == null) {
                en.setCurProd(0L);
            }
            // 获取最大进度
            Long maxProgress = getMaxProgress(en);
            en.setSumProd(Objects.requireNonNullElse(maxProgress, 0L));
            // 然后进行累加操作
            en.setSumProd(en.getSumProd() + en.getCurProd());
            if (en.getSumProd() > 100d) {
                throw new RuntimeException("总进度不能大于100");
            }
            // 更新后的总进度为100，则状态设置成已结束
            if (en.getSumProd().equals(100L)) {
                en.setMSta("已结束");
            }
            // 状态已结束设置总进度为100
            if (en.getMSta().equals("已结束")) {
                en.setSumProd(100L);
            }
        });
        sfcInfos.forEach(en -> {
            if (en.getMSta().equals("已结束") || en.getMSta().equals("已完成")) {
                double sjGs = calculateWorkingHours(en.getBDtm(), en.getEDtm());
                en.setSjGs(sjGs < 0 ? 0 : sjGs);
                // 确保开始时间和结束时间在同一日期内
                LocalDate startDate = en.getBDtm().toLocalDate();
                LocalDate endDate = en.getEDtm().toLocalDate();

                // 如果结束时间不在同一天内，将其调整为当天的23:30
                if (!startDate.isEqual(endDate)) {
                    en.setEDtm(startDate.atTime(23, 30));
                }
            }
            // 在设备装配进度在100%时，系统自动随机插入一条IPQC检验记录
            if (ZP_TYPE_EQ_LIST.contains(en.getZpType()) && en.getSumProd().equals(100L)) {
                Random random = new Random();
                int ran = random.nextInt(5, 15);
                AsyncManager.getInstance().execute(ipqcRunnable(en), ran, TimeUnit.MINUTES);
            }
        });

        Map<String, Set<String>> groupToEmpNos = new HashMap<>();
        Map<String, Set<String>> groupToEmpNames = new HashMap<>();
        for (SfcInfo sfcInfo : sfcInfos) {
            String key = sfcInfo.getBarCode() + sfcInfo.getBDtm() + sfcInfo.getEDtm();
            groupToEmpNos.computeIfAbsent(key, k -> new HashSet<>()).add(sfcInfo.getEmpNo());
            groupToEmpNames.computeIfAbsent(key, k -> new HashSet<>()).add(sfcInfo.getEmpName());
        }
        // 为每个记录添加参与人员信息
        sfcInfos.forEach(sfcInfo -> {
            sfcInfo.setParticipantNo(
                String.join(",", groupToEmpNos.get(sfcInfo.getBarCode() + sfcInfo.getBDtm() + sfcInfo.getEDtm())));
            sfcInfo.setParticipantName(
                String.join(",", groupToEmpNames.get(sfcInfo.getBarCode() + sfcInfo.getBDtm() + sfcInfo.getEDtm())));
        });

        return sfcInfos;
    }

    @Override
    public void uploadSap(List<SfcInfo> transInfos) {
        // 异步执行
        AsyncManager.getInstance().execute(() -> runnableUploadSap(transInfos));
    }

    private void runnableUploadSap(List<SfcInfo> transInfos) {
        try {
            // 1. 更新SQL Server数据库
            // updateSqlServerData(transInfos);
            updateSqlServerData1(transInfos);
            // 2. 过滤需要上传到SAP的数据
            List<SfcInfo> sfcInfos = filterSapUploadData(transInfos);
            if (sfcInfos.isEmpty()) {
                log.info("开单或者IPQC检验无需SAP报工");
                return;
            }

            // 3. 准备SAP上传数据
            Map<String, SfcInfo> referOrderMap = new HashMap<>();
            List<Map<String, Object>> confTableData = prepareSapUploadData(sfcInfos, referOrderMap);

            // 4. 调用SAP函数并处理结果
            processSapUpload(confTableData, referOrderMap);

            log.info("报工SAP结束");
        } catch (Exception e) {
            log.error("上传sap错误", e);
            throw new ServiceException("上传SAP错误: " + e.getMessage());
        }
    }

    /**
     * 更新SQL Server数据库
     */
    private void updateSqlServerData1(List<SfcInfo> transInfos) {
        // 批量处理的大小
        final int BATCH_SIZE = 100;
        // 最大重试次数
        final int MAX_RETRIES = 3;

        // SQL语句
        final String SQL_QUERY =
            "select count(*) from pp_scan with(nolock) where bar_code=? and PJ号=? and 动作=? and 操作员=? and 结束时间 is null";
        final String SQL_UPDATE =
            "update pp_scan set 结束时间=?,实际时间=?,总进度=?,当前进度=?,通过数量=?,报废数量=? where bar_code=? and PJ号=? and 动作=? and 操作员=? and 结束时间 is null";
        final String SQL_INSERT =
            "insert into pp_scan(PJ号,工序号,工序名称,操作员,姓名,机床,动作,开始时间,结束时间,通过数量,报废数量,实际时间,备注,加工厂址,图号,部门,ARBPL,dep,bar_code,总进度,当前进度) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        final String SQL_MASTER1 = "update MASTER set 状态=?,动作=?,末工序时间=?,sys_dt=?,当前工序=? where mo_no=?";
        final String SQL_MASTER2 = "update MASTER set 上层状态=? where mo_no<>'' and 上层订单号=?";

        try {
            // 切换到正确的数据源
            SqlserverJdbcUtils.switchDataSource("sqlserver1");

            // 分批处理数据
            for (int i = 0; i < transInfos.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, transInfos.size());
                List<SfcInfo> batch = transInfos.subList(i, endIndex);

                // 使用重试机制执行批量更新
                executeBatchWithRetry(batch, SQL_QUERY, SQL_UPDATE, SQL_INSERT, SQL_MASTER1, SQL_MASTER2, MAX_RETRIES);

                // 添加适当的延迟，避免数据库压力过大
                if (endIndex < transInfos.size()) {
                    Thread.sleep(100);
                }
            }
        } catch (Exception e) {
            log.error("批量更新SQL Server数据失败", e);
            throw new ServiceException("更新SQL Server数据失败: " + e.getMessage());
        }
    }

    /**
     * 使用重试机制执行批量更新
     */
    private void executeBatchWithRetry(List<SfcInfo> batch, String sqlQuery, String sqlUpdate, String sqlInsert,
        String sqlMaster1, String sqlMaster2, int maxRetries) throws SQLException {
        int retryCount = 0;
        while (retryCount < maxRetries) {
            try {
                executeBatchUpdate(batch, sqlQuery, sqlUpdate, sqlInsert, sqlMaster1, sqlMaster2);
                return;
            } catch (SQLServerException e) {
                if (e.getMessage().contains("死锁") && retryCount < maxRetries - 1) {
                    retryCount++;
                    log.warn("发生死锁，正在进行第{}次重试", retryCount);
                    try {
                        Thread.sleep(1000 * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new SQLException("重试被中断", ie);
                    }
                } else {
                    throw e;
                }
            }
        }
    }

    /**
     * 执行批量更新
     */
    private void executeBatchUpdate(List<SfcInfo> batch, String sqlQuery, String sqlUpdate, String sqlInsert,
        String sqlMaster1, String sqlMaster2) throws SQLException {
        Connection conn = null;
        try {
            conn = SqlserverJdbcUtils.getConnection();
            conn.setAutoCommit(false);
            // 准备批量更新参数
            List<Object[]> updateParams = new ArrayList<>();
            List<Object[]> insertParams = new ArrayList<>();
            List<Object[]> master1Params = new ArrayList<>();
            List<Object[]> master2Params = new ArrayList<>();

            for (SfcInfo info : batch) {
                // 检查记录是否存在
                int count = SqlserverJdbcUtils.getRecordCount(sqlQuery,
                    Arrays.asList(info.getBarCode(), info.getMoNo(), info.getZpType(), info.getEmpNo()));
                if (count > 0) {
                    // 准备更新参数
                    updateParams.add(new Object[] {info.getEDtm(), info.getSjGs(), info.getSumProd(), info.getCurProd(),
                        info.getQtyOk(), info.getQtyNg(), info.getBarCode(), info.getMoNo(), info.getZpType(),
                        info.getEmpNo()});
                } else {
                    // 准备插入参数
                    insertParams.add(new Object[] {info.getMoNo(), info.getPType(), info.getPName(), info.getEmpNo(),
                        info.getEmpName(), ObjectUtils.isEmpty(info.getArbpl()) ? "F2ZZ11" : info.getArbpl(),
                        info.getZpType(), info.getBDtm(), info.getEDtm(), info.getQtyOk(), info.getQtyNg(),
                        info.getSjGs(), info.getRem(), "SFC", info.getWksDwg(), info.getDep(),
                        ObjectUtils.isEmpty(info.getArbpl()) ? "F2ZZ11" : info.getArbpl(), info.getDep(),
                        info.getBarCode(), info.getSumProd(), info.getCurProd()});
                }
                // 准备主表更新参数
                master1Params.add(new Object[] {"0010" + info.getPName() + info.getZpType(), info.getZpType(),
                    info.getEDtm(), LocalDateTime.now(), "0010" + info.getZpType(), info.getMoNo()});
                master2Params.add(new Object[] {"0010" + info.getPName() + info.getZpType(), info.getMoNo()});
            }

            // 执行批量更新
            if (!updateParams.isEmpty()) {
                SqlserverJdbcUtils.executeBatchUpdate(sqlUpdate, updateParams);
            }
            if (!insertParams.isEmpty()) {
                SqlserverJdbcUtils.executeBatchInsert(sqlInsert, insertParams);
            }
            if (!master1Params.isEmpty()) {
                SqlserverJdbcUtils.executeBatchUpdate(sqlMaster1, master1Params);
            }
            if (!master2Params.isEmpty()) {
                SqlserverJdbcUtils.executeBatchUpdate(sqlMaster2, master2Params);
            }
            conn.commit();
        } catch (SQLException e) {
            if (conn != null) {
                try {
                    conn.rollback();
                } catch (SQLException ex) {
                    log.error("回滚事务失败", ex);
                }
            }
            throw e;
        } finally {
            SqlserverJdbcUtils.closeConnection(conn);
        }
    }

    /**
     * 更新SQL Server数据库
     */
    private void updateSqlServerData(List<SfcInfo> transInfos) {
        String sqlQuery =
            "select count(*) from pp_scan with(nolock) where bar_code=? and PJ号=? and 动作=? and 操作员=? and 结束时间 is null";
        String sqlUpdate =
            "update pp_scan set 结束时间=?,实际时间=?,总进度=?,当前进度=?,通过数量=?,报废数量=? where bar_code=? and PJ号=? and 动作=? and 操作员=? and 结束时间 is null";
        String sqlInsert = "insert into pp_scan"
            + "(PJ号,工序号,工序名称,操作员,姓名,机床,动作,开始时间,结束时间,通过数量,报废数量,实际时间,备注,加工厂址,图号,部门,ARBPL,dep,bar_code,总进度,当前进度)"
            + " values " + "(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        String sqlMaster1 = "update MASTER set 状态=?,动作=?,末工序时间=?,sys_dt=?,当前工序=? where mo_no=?";
        String sqlMaster2 = "update MASTER set 上层状态=? where mo_no<>'' and 上层订单号=?";

        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        for (SfcInfo en : transInfos) {
            try {
                Thread.sleep(1000);
                updateSqlServerRecord(en, sqlQuery, sqlUpdate, sqlInsert, sqlMaster1, sqlMaster2);
            } catch (Exception e) {
                log.error("写入sqlserver错误", e);
            }
        }
    }

    /**
     * 更新单条SQL Server记录
     */
    private void updateSqlServerRecord(SfcInfo en, String sqlQuery, String sqlUpdate, String sqlInsert,
        String sqlMaster1, String sqlMaster2) throws SQLException {
        int count = SqlserverJdbcUtils.getRecordCount(sqlQuery,
            Arrays.asList(en.getBarCode(), en.getMoNo(), en.getZpType(), en.getEmpNo()));

        if (count > 0) {
            updateExistingRecord(en, sqlUpdate);
        } else {
            insertNewRecord(en, sqlInsert);
        }

        updateMasterRecords(en, sqlMaster1, sqlMaster2);
    }

    /**
     * 更新已存在的记录
     */
    private void updateExistingRecord(SfcInfo en, String sqlUpdate) throws SQLException {
        List<Object> params = new ArrayList<>();
        params.add(en.getEDtm());
        params.add(en.getSjGs());
        params.add(en.getSumProd());
        params.add(en.getCurProd());
        params.add(en.getQtyOk());
        params.add(en.getQtyNg());
        params.add(en.getBarCode());
        params.add(en.getMoNo());
        params.add(en.getZpType());
        params.add(en.getEmpNo());
        SqlserverJdbcUtils.executeUpdate(sqlUpdate, params);
    }

    /**
     * 插入新记录
     */
    private void insertNewRecord(SfcInfo en, String sqlInsert) throws SQLException {
        List<Object> params = new ArrayList<>();
        params.add(en.getMoNo());
        params.add(en.getPType());
        params.add(en.getPName());
        params.add(en.getEmpNo());
        params.add(en.getEmpName());
        params.add(ObjectUtils.isEmpty(en.getArbpl()) ? "F2ZZ11" : en.getArbpl());
        params.add(en.getZpType());
        params.add(en.getBDtm());
        params.add(en.getEDtm());
        params.add(en.getQtyOk());
        params.add(en.getQtyNg());
        params.add(en.getSjGs());
        params.add(en.getRem());
        params.add("SFC");
        params.add(en.getWksDwg());
        params.add(en.getDep());
        params.add(ObjectUtils.isEmpty(en.getArbpl()) ? "F2ZZ11" : en.getArbpl());
        params.add(en.getDep());
        params.add(en.getBarCode());
        params.add(en.getSumProd());
        params.add(en.getCurProd());
        SqlserverJdbcUtils.executeUpdate(sqlInsert, params);
    }

    /**
     * 更新主表记录
     */
    private void updateMasterRecords(SfcInfo en, String sqlMaster1, String sqlMaster2) throws SQLException {
        SqlserverJdbcUtils.executeUpdate(sqlMaster1, Arrays.asList("0010" + en.getPName() + en.getZpType(),
            en.getZpType(), en.getEDtm(), LocalDateTime.now(), "0010" + en.getZpType(), en.getMoNo()));
        SqlserverJdbcUtils.executeUpdate(sqlMaster2,
            Arrays.asList("0010" + en.getPName() + en.getZpType(), en.getMoNo()));
    }

    /**
     * 过滤需要上传到SAP的数据
     */
    private List<SfcInfo> filterSapUploadData(List<SfcInfo> transInfos) {
        return transInfos.stream()
            .filter(en -> !ObjectUtils.isEmpty(en.getEDtm()) && !Objects.equals(en.getZpType(), "IPQC检验")).toList();
    }

    /**
     * 准备SAP上传数据
     */
    private List<Map<String, Object>> prepareSapUploadData(List<SfcInfo> sfcInfos, Map<String, SfcInfo> referOrderMap) {
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HHmmss");
        List<Map<String, Object>> confTableData = new ArrayList<>();

        for (SfcInfo info : sfcInfos) {
            Map<String, Object> row = new HashMap<>();
            String barCode = info.getBarCode();
            referOrderMap.put(String.valueOf(info.getId()), info);
            // 基本信息
            row.put("REFERORDER", String.valueOf(info.getId()));
            row.put("REFERITEM", "000001");
            row.put("STEMFROM", "SFC");
            row.put("AUFNR", info.getMoNo().trim().toUpperCase(Locale.ROOT));
            row.put("VORNR", barCode.length() == 16 ? barCode.substring(barCode.length() - 4) : "0010");

            // 工时信息
            double laborTimeInMinutes = info.getSjGs() * 60;
            row.put("LABORTIME", laborTimeInMinutes);
            row.put("LABORUNIT", "MIN");
            row.put("MACHTIME", laborTimeInMinutes);
            row.put("MACHUNIT", "MIN");
            row.put("SETUPTIME", laborTimeInMinutes);
            row.put("SETUPUNIT", "MIN");

            // 日期时间信息
            row.put("BUDAT", LocalDateTime.now().format(dateFormatter));
            row.put("SDATE", info.getBDtm().format(dateFormatter));
            row.put("STIME", info.getBDtm().format(timeFormatter));
            row.put("EDATE", info.getEDtm().format(dateFormatter));
            row.put("ETIME", info.getEDtm().format(timeFormatter));

            // 人员信息
            row.put("CARDNO", info.getEmpNo());
            row.put("CARDNAME", info.getEmpName());
            row.put("ARBPL", ObjectUtils.isEmpty(info.getArbpl()) ? "F2ZZ11" : info.getArbpl());

            confTableData.add(row);
        }

        return confTableData;
    }

    /**
     * 创建参考订单映射
     */
    private Map<String, SfcInfo> createReferOrderMap(List<SfcInfo> sfcInfos) {
        Map<String, SfcInfo> referOrderMap = new HashMap<>();
        for (SfcInfo info : sfcInfos) {
            String mark = info.getBarCode() + System.currentTimeMillis();
            referOrderMap.put(mark, info);
        }
        return referOrderMap;
    }

    /**
     * 处理SAP上传
     */
    private void processSapUpload(List<Map<String, Object>> confTableData, Map<String, SfcInfo> referOrderMap)
        throws JCoException {
        log.info("开始调用SAP函数ZPPM_CONF_SYNCH_MODIFY，表格数据行数: {}", confTableData.size());

        Object[] tableData = confTableData.toArray();
        Map<String, Object> result =
            SapUtils.callFunctionWithTable("ZPPM_CONF_SYNCH_MODIFY", null, "TXI_CONF", tableData);

        log.info("SAP函数调用完成，返回结果键: {}", result != null ? result.keySet() : "null");

        processSapResponse(result, referOrderMap);
    }

    /**
     * 处理SAP响应
     */
    private void processSapResponse(Map<String, Object> result, Map<String, SfcInfo> referOrderMap) {
        Object[] returnTable = SapUtils.getTableData(result, "TXI_CONF");
        if (returnTable != null) {
            List<String> responseMessages = new ArrayList<>();
            for (Object rowObj : returnTable) {
                Map<String, Object> row = (Map<String, Object>)rowObj;
                String referOrder = (String)row.get("REFERORDER");
                String type = (String)row.get("TYPE");
                String message = (String)row.get("MESSAGE");

                // 记录SAP调用日志
                saveSapLog(referOrder, message, referOrderMap.get(referOrder));

                // 构建返回消息
                String responseMessage =
                    "S".equals(type) ? "报工单号:" + referOrder + ",报工成功" : "报工单号:" + referOrder + "," + message;
                responseMessages.add(responseMessage);
            }
            log.info(responseMessages.toString());
        } else {
            log.warn("未获取到返回表格数据TXI_CONF");
        }
    }

    /**
     * 保存SAP日志
     */
    private void saveSapLog(String referOrder, String message, SfcInfo sfcInfo) {
        SfcScanSapLog log = new SfcScanSapLog();
        log.setReferorder(referOrder);
        log.setMessage(message);
        log.setData(sfcInfo.toString());
        sfcScanSapLogMapper.insert(log);
    }

    private Runnable ipqcRunnable(SfcInfo info) {
        return () -> {
            LambdaQueryWrapper<SfcIpqcEmp> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SfcIpqcEmp::getDept, "设备组").isNull(SfcIpqcEmp::getGroupLeader).or()
                .eq(SfcIpqcEmp::getGroupLeader, "");
            List<SfcIpqcEmp> empList = sfcIpqcEmpMapper.selectList(queryWrapper);
            Random random = new Random();
            int r1 = random.nextInt(empList.size()); // 生成随机整数
            SfcIpqcEmp emp = empList.get(r1);
            SfcInfo sfcInfo = new SfcInfo();
            sfcInfo.setEmpNo(emp.getEmpNo());
            sfcInfo.setEmpName(emp.getEmpName());
            sfcInfo.setDep(emp.getDep());
            sfcInfo.setBDtm(LocalDateTime.now());
            int r2 = random.nextInt(10, 20);
            sfcInfo.setEDtm(LocalDateTime.now().plusMinutes(r2));

            sfcInfo.setBarCode(info.getBarCode());
            sfcInfo.setMoNo(info.getMoNo());
            sfcInfo.setFloorNo(info.getFloorNo());
            sfcInfo.setCusName(info.getCusName());
            sfcInfo.setOQty(info.getOQty());
            sfcInfo.setWksDwg(info.getWksDwg());
            sfcInfo.setZpType("IPQC检验");
            sfcInfo.setPName("IPQC检验");
            sfcInfo.setPType("0010");
            sfcInfo.setCurProd(100L);
            sfcInfo.setSumProd(100L);
            sfcInfo.setMSta("已结束");
            sfcInfo.setRem("SFC");
            sfcInfoMapper.insert(sfcInfo);
            log.info("完成IPQC插入：{}", info.getBarCode());
        };
    }

    private double calculateWorkingHours(LocalDateTime start, LocalDateTime end) {
        // 确保开始时间和结束时间在同一日期内
        LocalDate startDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();

        // 如果结束时间不在同一天内，将其调整为当天的23:30
        if (!startDate.isEqual(endDate)) {
            end = startDate.atTime(23, 30);
        }

        // 定义用餐时间段
        LocalTime lunchStart = LocalTime.of(11, 30);
        LocalTime lunchEnd = LocalTime.of(13, 0);
        LocalTime dinnerStart = LocalTime.of(17, 30);
        LocalTime dinnerEnd = LocalTime.of(18, 0);

        // 调整当天的开始和结束时间，使其不会跨越用餐时间
        LocalDateTime adjustedStart = start;
        boolean isLunch = false;
        if (start.toLocalTime().isAfter(lunchStart) && start.toLocalTime().isBefore(lunchEnd)) {
            // 如果开始时间在午餐时间段内，将其调整为当天的13
            adjustedStart = start.with(lunchEnd);
        }
        if (start.toLocalTime().isBefore(lunchStart) && end.toLocalTime().isAfter(lunchEnd)) {
            // 如果开始时间在午餐时间前，结束时间在午餐时间后，减掉午餐时间
            isLunch = true;
        }

        LocalDateTime adjustedEnd = end;
        boolean isDinner = false;
        if (end.toLocalTime().isAfter(dinnerStart) && end.toLocalTime().isBefore(dinnerEnd)) {
            // 如果结束时间在晚餐时间段内，将其调整为当天的17:30
            adjustedEnd = end.with(dinnerStart);
        }
        if (start.toLocalTime().isBefore(dinnerStart) && end.toLocalTime().isAfter(dinnerEnd)) {
            // 如果开始时间在晚餐时间前，结束时间在晚餐时间后，减掉晚餐时间
            isDinner = true;
        }
        // 如果开始时间在晚餐时间段内
        if (start.toLocalTime().isAfter(dinnerStart) && start.toLocalTime().isBefore(dinnerEnd)) {
            adjustedStart = start.with(dinnerEnd);
        }

        // 计算实际工作时间
        Duration workDuration = Duration.between(adjustedStart, adjustedEnd);
        double totalHours = (double)workDuration.getSeconds() / 3600;

        // 计算用餐时间
        double lunchDuration = isLunch ? Duration.between(lunchStart, lunchEnd).getSeconds() / 3600.0 : 0d;
        double dinnerDuration = isDinner ? Duration.between(dinnerStart, dinnerEnd).getSeconds() / 3600.0 : 0d;

        // 总工作时间减去用餐时间
        double actualHours = totalHours - (lunchDuration + dinnerDuration);

        return NumberUtil.round(actualHours, 2).doubleValue();
    }

    private Long getMaxProgress(SfcInfo sfcInfo) {
        if (ObjectUtils.isEmpty(sfcInfo)) {
            return null;
        }
        if (ObjectUtils.isEmpty(sfcInfo.getZpType()) || ObjectUtils.isEmpty(sfcInfo.getBarCode())) {
            return null;
        }
        LambdaQueryWrapper<SfcInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SfcInfo::getZpType, sfcInfo.getZpType());
        queryWrapper.eq(SfcInfo::getBarCode, sfcInfo.getBarCode());
        return sfcInfoMapper.selectList(queryWrapper).stream().max(Comparator.comparing(SfcInfo::getSumProd))
            .map(SfcInfo::getSumProd).orElse(null);
    }

}
