package org.dromara.sfc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SapScanMasterSn;
import org.dromara.sfc.domain.bo.SapScanMasterSnBo;
import org.dromara.sfc.domain.vo.SapScanMasterSnVo;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;

/**
 * sap_scan_master_snService接口
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface ISapScanMasterSnService extends IService<SapScanMasterSn> {

    /**
     * 查询sap_scan_master_sn
     *
     * @param myRowId 主键
     * @return sap_scan_master_sn
     */
    SapScanMasterSnVo queryById(Long myRowId);

    /**
     * 分页查询sap_scan_master_sn列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return sap_scan_master_sn分页列表
     */
    TableDataInfo<SapScanMasterSnVo> queryPageList(SapScanMasterSnBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的sap_scan_master_sn列表
     *
     * @param bo 查询条件
     * @return sap_scan_master_sn列表
     */
    List<SapScanMasterSnVo> queryList(SapScanMasterSnBo bo);

    /**
     * 新增sap_scan_master_sn
     *
     * @param bo sap_scan_master_sn
     * @return 是否新增成功
     */
    Boolean insertByBo(SapScanMasterSnBo bo);

    /**
     * 修改sap_scan_master_sn
     *
     * @param bo sap_scan_master_sn
     * @return 是否修改成功
     */
    Boolean updateByBo(SapScanMasterSnBo bo);

    /**
     * 校验并批量删除sap_scan_master_sn信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据MO编号和所需数量获取SAP扫描主件SN列表。
     *
     * @param mo  物料订单（Manufacturing Order）编号，用于指定需要查询的MO。
     * @param num 需要返回的SAP扫描主件SN的数量。
     * @return 包含指定数量SAP扫描主件SN的列表。
     */

    List<SapScanMasterSn> getSapScanMasterSns(String mo, Integer num) throws SQLException;

    void syncRemote(SapScanMasterSn sapScanMasterSn) throws SQLException;
}
