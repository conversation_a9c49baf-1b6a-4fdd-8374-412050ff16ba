package org.dromara.sfc.controller;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.NoSuchAlgorithmException;
import java.util.List;

import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.DigestUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.web.core.BaseController;
import org.dromara.resource.api.RemoteFileService;
import org.dromara.sfc.domain.bo.ZpPicBo;
import org.dromara.sfc.domain.bo.ZpPicDataBo;
import org.dromara.sfc.domain.vo.ZpPicDataVo;
import org.dromara.sfc.domain.vo.ZpPicVo;
import org.dromara.sfc.service.IZpPicDataService;
import org.dromara.sfc.service.IZpPicService;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.io.resource.ClassPathResource;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 装配现场图像 前端访问路由地址为:/sfc/zp
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/zp")
public class ZpController extends BaseController {

    private final IZpPicService zpPicService;

    @Resource
    IZpPicDataService zpPicDataService;

    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 查询装配现场图像列表
     */
    @SaCheckPermission("sfc:zp:list")
    @GetMapping("/list")
    public TableDataInfo<ZpPicVo> list(ZpPicBo bo, PageQuery pageQuery) {
        return zpPicService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出装配现场图像列表
     */
    @SaCheckPermission("sfc:zp:export")
    @Log(title = "装配现场图像", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ZpPicBo bo, HttpServletResponse response) {
        List<ZpPicVo> list = zpPicService.queryList(bo);
        ExcelUtil.exportExcel(list, "装配现场图像", ZpPicVo.class, response);
    }

    /**
     * 获取装配现场图像详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("sfc:zp:query")
    @GetMapping("/{id}")
    public R<ZpPicVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(zpPicService.queryById(id));
    }

    /**
     * 新增装配现场图像
     */
    @SaCheckPermission("sfc:zp:add")
    @Log(title = "装配现场图像", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ZpPicBo bo) {
        return toAjax(zpPicService.insertByBo(bo));
    }

    /**
     * 修改装配现场图像
     */
    @SaCheckPermission("sfc:zp:edit")
    @Log(title = "装配现场图像", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ZpPicBo bo) {
        return toAjax(zpPicService.updateByBo(bo));
    }

    /**
     * 删除装配现场图像
     *
     * @param ids 主键串
     */
    @SaCheckPermission("sfc:zp:remove")
    @Log(title = "装配现场图像", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(zpPicService.deleteWithValidByIds(List.of(ids), true));
    }

    @GetMapping("/pic/data/list")
    public TableDataInfo<ZpPicDataVo> getZpPicData(ZpPicDataBo bo, PageQuery pageQuery) {
        return TableDataInfo.build(zpPicDataService.getZpPicData(bo, pageQuery));
    }

    @PostMapping("/export/zpTemplate")
    public void export(HttpServletResponse response) {
        // // 对文件名进行URL编码并替换 "+" 为 "%20"
        // String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
        // 配置响应
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment; filename=zp_template.xlsx");
        // 将PDF文件内容写入响应输出流
        try (InputStream inputStream = new ClassPathResource("/excel/zp_template.xlsx").getStream();
            OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/pic/mo/upload")
    @Log(title = "装配现场图像模板导入", businessType = BusinessType.IMPORT)
    public R upload(@RequestParam("file") MultipartFile file) {
        return zpPicService.dealMoFile(file);
    }

    @PostMapping("/pic/upload")
    @Log(title = "装配现场图像上传", businessType = BusinessType.IMPORT)
    public R uploadZp(@RequestParam("file") MultipartFile file, @RequestParam("moNo") String moNo,
        @RequestParam("picType") String picType) throws IOException {
        if (file.isEmpty() || ObjectUtils.isEmpty(moNo) || ObjectUtils.isEmpty(picType)) {
            return R.fail("param is empty.");
        }
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(file.getBytes());
        String md5Digest;
        try {
            md5Digest = DigestUtils.md5Hex(byteArrayInputStream);
        } catch (NoSuchAlgorithmException e) {
            return R.fail("MD5计算失败");
        }
        if (remoteFileService.checkFileExist(md5Digest)) {
            return R.fail("系统已存在该文件，不能重复上传");
        }
        String originalFilename = file.getOriginalFilename();
        String suffix =
            StringUtils.substring(originalFilename, originalFilename.lastIndexOf("."), originalFilename.length());
        // 构造存储路径
        String path = moNo + "/" + picType + "/" + originalFilename;
        OssClient storage = OssFactory.instance("minio");
        UploadResult uploadResult =
            storage.uploadSuffix(file.getBytes(), suffix, path, "bucket-zp", file.getContentType());
        Boolean flag = remoteFileService.saveUploadResult(originalFilename, uploadResult.getFilename(), suffix,
            uploadResult.getUrl(), uploadResult.getETag().replace("\"", ""), storage.getConfigKey());
        return zpPicService.dealZpFile(uploadResult, moNo, picType,flag);
    }

    @GetMapping("/pic/{mo}")
    public R getZpPic(@PathVariable String mo) {
        return R.ok(zpPicService.getZpMoData(mo));
    }
}
