package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcCodeRuleBo;
import org.dromara.sfc.domain.vo.SfcCodeRuleVo;
import org.dromara.sfc.service.ISfcCodeRuleService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 类别设置
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/codeRule")
public class SfcCodeRuleController extends BaseController {

    private final ISfcCodeRuleService sfcCodeRuleService;

    /**
     * 查询类别设置列表
     */
    @SaCheckPermission("information:codeRule:list")
    @GetMapping("/list")
    public TableDataInfo<SfcCodeRuleVo> list(SfcCodeRuleBo bo, PageQuery pageQuery) {
        return sfcCodeRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出类别设置列表
     */
    @SaCheckPermission("information:codeRule:export")
    @Log(title = "类别设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcCodeRuleBo bo, HttpServletResponse response) {
        List<SfcCodeRuleVo> list = sfcCodeRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "类别设置", SfcCodeRuleVo.class, response);
    }

    /**
     * 获取类别设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:codeRule:query")
    @GetMapping("/{id}")
    public R<SfcCodeRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                    @PathVariable Long id) {
        return R.ok(sfcCodeRuleService.queryById(id));
    }

    /**
     * 新增类别设置
     */
    @SaCheckPermission("information:codeRule:add")
    @Log(title = "类别设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @CacheEvict(value = "barcodeTypesCache", allEntries = true)
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcCodeRuleBo bo) {
        return toAjax(sfcCodeRuleService.insertByBo(bo));
    }

    /**
     * 修改类别设置
     */
    @SaCheckPermission("information:codeRule:edit")
    @Log(title = "类别设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @CacheEvict(value = "barcodeTypesCache", allEntries = true)
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcCodeRuleBo bo) {
        return toAjax(sfcCodeRuleService.updateByBo(bo));
    }

    /**
     * 删除类别设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:codeRule:remove")
    @Log(title = "类别设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcCodeRuleService.deleteWithValidByIds(List.of(ids), true));
    }

        /**
     * 根据类型获取楼层数据
     *
     * @param codeType 类型
     * @return R<List<SfcCodeRuleVo>> 条码规则列表
     */
    @GetMapping("/type/{codeType}")
    public R<List<SfcCodeRuleVo>> getCodeRulelist(@NotEmpty(message = "类型不能为空") @PathVariable("codeType") String codeType) {
        try {
            List<SfcCodeRuleVo> sfcCodeRuleVos = sfcCodeRuleService.selectList(codeType);
            return R.ok(sfcCodeRuleVos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 查询条码类型组的数据
     *
     * @return List<String> 条码类型组
     */
    @GetMapping("/barcode/types")
    public R<List<Map<String, Object>>> getBarcodeTypes() {
        List<Map<String, Object>> barcodeTypes = sfcCodeRuleService.getBarcodeTypes(); // 假设您在服务中有这个方法
        return R.ok(barcodeTypes);
    }

    /**
     * 查询条码类型组的数据
     *
     * @return List<String> 条码类型组
     */
    @GetMapping("/barcode/ancTypes")
    public R<List<Map<String, Object>>> getBarcodeAncTypes() {
        List<Map<String, Object>> barcodeTypes = sfcCodeRuleService.getBarcodeAncTypes(); // 假设您在服务中有这个方法
        return R.ok(barcodeTypes);
    }
}
