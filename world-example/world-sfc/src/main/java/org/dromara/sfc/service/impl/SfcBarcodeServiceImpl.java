package org.dromara.sfc.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.convert.Convert;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.service.DictService;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.SfcBarcode;
import org.dromara.sfc.domain.SfcCodeRule;
import org.dromara.sfc.domain.SfcInfo;
import org.dromara.sfc.domain.bo.SfcBarcodeBo;
import org.dromara.sfc.domain.dto.InfoBarcodeDTO;
import org.dromara.sfc.domain.dto.ProcessDTO;
import org.dromara.sfc.domain.vo.SfcBarcodeVo;
import org.dromara.sfc.mapper.SapScanMasterMapper;
import org.dromara.sfc.mapper.SfcBarcodeMapper;
import org.dromara.sfc.mapper.SfcCodeRuleMapper;
import org.dromara.sfc.mapper.SfcInfoMapper;
import org.dromara.sfc.query.BarcodeQuery;
import org.dromara.sfc.service.ISfcBarcodeService;
import org.dromara.sfc.utils.DateUtil;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 非生产条码Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SfcBarcodeServiceImpl extends ServiceImpl<SfcBarcodeMapper, SfcBarcode> implements ISfcBarcodeService {

    private static final String CACHE_KEY = "sfc:abnormal:data:";
    private static final Duration CACHE_TTL = Duration.ofMinutes(30);
    private static final String TEMPLATE_PATH = "/excel/SFC_MB_template.xlsx";

    // 业务常量
    private static final String BAR_TYPE_ELECTRIC = "电气";
    private static final String BAR_TYPE_MECHANISM = "机构";
    private static final String BAR_TYPE_OTHER = "其他";

    @Resource
    private SfcBarcodeMapper baseMapper;
    @Resource
    private DictService dictService;
    @Resource
    private SfcInfoMapper sfcInfoMapper;
    @Resource
    private SfcCodeRuleMapper sfcCodeRuleMapper;
    @Resource
    private SapScanMasterMapper sapScanMasterMapper;

    /**
     * 查询非生产条码
     *
     * @param id 主键
     * @return 非生产条码
     */
    @Override
    public SfcBarcodeVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询非生产条码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 非生产条码分页列表
     */
    @Override
    public TableDataInfo<SfcBarcodeVo> queryPageList(SfcBarcodeBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcBarcode> lqw = buildQueryWrapper(bo);
        Page<SfcBarcodeVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的非生产条码列表
     *
     * @param bo 查询条件
     * @return 非生产条码列表
     */
    @Override
    public List<SfcBarcodeVo> queryList(SfcBarcodeBo bo) {
        LambdaQueryWrapper<SfcBarcode> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcBarcode> buildQueryWrapper(SfcBarcodeBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcBarcode> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(SfcBarcode::getId);
        lqw.like(StringUtils.isNotBlank(bo.getActType()), SfcBarcode::getActType, bo.getActType());
        lqw.like(StringUtils.isNotBlank(bo.getMoNo()), SfcBarcode::getMoNo, bo.getMoNo());
        lqw.like(StringUtils.isNotBlank(bo.getBarCode()), SfcBarcode::getBarCode, bo.getBarCode());
        lqw.like(StringUtils.isNotBlank(bo.getBarType()), SfcBarcode::getBarType, bo.getBarType());
        lqw.like(StringUtils.isNotBlank(bo.getSoNo()), SfcBarcode::getSoNo, bo.getSoNo());
        lqw.like(StringUtils.isNotBlank(bo.getSoItm()), SfcBarcode::getSoItm, bo.getSoItm());
        lqw.like(StringUtils.isNotBlank(bo.getHmoNo()), SfcBarcode::getHmoNo, bo.getHmoNo());
        lqw.like(StringUtils.isNotBlank(bo.getPrdName()), SfcBarcode::getPrdName, bo.getPrdName());
        lqw.like(StringUtils.isNotBlank(bo.getPrdType()), SfcBarcode::getPrdType, bo.getPrdType());
        lqw.like(StringUtils.isNotBlank(bo.getDwgNo()), SfcBarcode::getDwgNo, bo.getDwgNo());
        lqw.like(StringUtils.isNotBlank(bo.getProType()), SfcBarcode::getProType, bo.getProType());
        lqw.like(StringUtils.isNotBlank(bo.getSfileName()), SfcBarcode::getSfileName, bo.getSfileName());

        // 数值类型属性
        if (bo.getQty() != null) {
            lqw.eq(SfcBarcode::getQty, bo.getQty());
        }
        if (bo.getBpRy() != null) {
            lqw.eq(SfcBarcode::getBpRy, bo.getBpRy());
        }
        if (bo.getBasGs() != null) {
            lqw.eq(SfcBarcode::getBasGs, bo.getBasGs());
        }

        // 日期类型属性
        if (bo.getSysdt() != null) {
            lqw.eq(SfcBarcode::getSysdt, bo.getSysdt());
        }
        if (bo.getEndDate() != null) {
            lqw.isNotNull(SfcBarcode::getEndDate);
        }

        return lqw;
    }

    /**
     * 新增非生产条码
     *
     * @param bo 非生产条码
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcBarcodeBo bo) {
        SfcBarcode add = MapstructUtils.convert(bo, SfcBarcode.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改非生产条码
     *
     * @param bo 非生产条码
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcBarcodeBo bo) {
        SfcBarcode update = MapstructUtils.convert(bo, SfcBarcode.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcBarcode entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除非生产条码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void generateAnotherBarcode(HttpServletResponse response, String prefix) throws IOException {
        /*
         * String prefix = "QT";
         * // 类型是培训，5S整理，人力外调，则赋予相应开头
         * if ("培训".equals(type)) {
         * prefix = "PX";
         * } else if ("5S整理".equals(type)) {
         * prefix = "5S";
         * } else if ("人力外调".equals(type)) {
         * prefix = "RL";
         * }
         */
        // 时间戳作为虚拟MO号
        String moNo = DateUtils.dateTimeNowYyMmDdHhMmSs();
        String type = dictService.getDictLabel("barcode_type", prefix);
        // 条码加上前缀
        String barCode = prefix + moNo;
        SfcBarcode sfcBarcode = new SfcBarcode();
        sfcBarcode.setBarCode(barCode);
        sfcBarcode.setMoNo(moNo);
        sfcBarcode.setProType("01");
        sfcBarcode.setProName(type);
        sfcBarcode.setActType(type);
        sfcBarcode.setSysdt(new Date());
        sfcBarcode.setUsr(LoginHelper.getUsername());

        baseMapper.insert(sfcBarcode);

        // 对文件名进行URL编码并替换 "+" 为 "%20"
        String fileName = type + "条码.xlsx";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
        // 配置响应
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
        ClassPathResource templateResource = new ClassPathResource(TEMPLATE_PATH);
        try (InputStream inputStream = templateResource.getStream();
             Workbook workbook = new XSSFWorkbook(inputStream);
             OutputStream os = response.getOutputStream()) {
            // 复制第一个sheet作为模板并插入新工作表
            Sheet copiedSheet = workbook.cloneSheet(0);
            // 准备写入的数据
            String[] dataToWrite = {"", "*" + sfcBarcode.getBarCode() + "*", sfcBarcode.getProName(),
                sfcBarcode.getMoNo()};
            // 在第一个sheet的第2列写入数据
            for (int j = 0; j < dataToWrite.length; j++) {
                Row row = copiedSheet.getRow(j);
                if (row == null) {
                    row = copiedSheet.createRow(j);
                }
                Cell cell = row.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                cell.setCellValue(dataToWrite[j]);
            }
            // 删除原始模板表
            workbook.removeSheetAt(0);
            // 保存更改
            workbook.write(os);
        }
    }

    /**
     * 处理上传的SOP文件
     *
     * @param file 文件
     * @return Response
     */
    @Override
    public R dealSopFile(MultipartFile file) {
        String originalFilename = file.getOriginalFilename();
        if (file.isEmpty() || ObjectUtils.isEmpty(originalFilename)) {
            return R.fail("file is empty.");
        }
        if (!originalFilename.endsWith(".xls") && !originalFilename.endsWith(".xlsx")) {
            return R.fail("must upload xls or xlsx file.");
        }

        String barType = BAR_TYPE_OTHER;
        if (originalFilename.contains(BAR_TYPE_ELECTRIC)) {
            barType = BAR_TYPE_ELECTRIC;
        } else if (originalFilename.contains(BAR_TYPE_MECHANISM)) {
            barType = BAR_TYPE_MECHANISM;
        }
        List<SfcBarcode> sfcBarcodeList = new ArrayList<>();
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            // 这里处理workbook，例如读取数据等
            Sheet sheet = workbook.getSheetAt(0);
            int prdNameIndex = 0;
            int dwgNoIndex = 0;
            Row row1 = sheet.getRow(3);
            DataFormatter dataFormatter = new DataFormatter();
            for (int i = row1.getFirstCellNum(); i < row1.getLastCellNum(); i++) {
                Cell cell = row1.getCell(i);
                String value = dataFormatter.formatCellValue(cell);
                if (value.contains("产品名称")) {
                    prdNameIndex = i;
                }
                if (value.contains("产品图号及版本")) {
                    dwgNoIndex = i;
                }
            }
            String prdName = row1.getCell(prdNameIndex + 3).getStringCellValue();
            String dwgNo = row1.getCell(dwgNoIndex + 4).getStringCellValue();

            // 获取工序信息
            int proTypeIndex = 0;
            int proNameIndex = 0;
            int basGsIndex = 0;
            int bpRyIndex = 0;
            Row row2 = sheet.getRow(5);
            for (int i = row2.getFirstCellNum(); i < row2.getLastCellNum(); i++) {
                Cell cell = row2.getCell(i);
                String value = dataFormatter.formatCellValue(cell);
                if (value.contains("站别")) {
                    proTypeIndex = i;
                }
                if (value.contains("工站内容")) {
                    proNameIndex = i;
                }
                if (value.contains("标准工时")) {
                    basGsIndex = i;
                }
                if (value.contains("标配人力")) {
                    bpRyIndex = i;
                }
            }
            String yyMmDdHhMmSs = DateUtil.getYyMmDdHhMmSs();
            int serialNumber = 1;
            for (int i = 6; i < sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                String proType = dataFormatter.formatCellValue(row.getCell(proTypeIndex));
                if (ObjectUtils.isEmpty(proType)) {
                    // 没有站别数据不用继续解析
                    break;
                }
                SfcBarcode sfcBarcode = new SfcBarcode();
                sfcBarcode.setActType("SOP");
                sfcBarcode.setBarType(barType);
                sfcBarcode.setPrdName(prdName);
                sfcBarcode.setDwgNo(dwgNo);
                sfcBarcode.setProType(proType);
                sfcBarcode.setProName(dataFormatter.formatCellValue(row.getCell(proNameIndex)));
                sfcBarcode.setQty(1f);
                sfcBarcode.setUt("set");
                sfcBarcode.setBpRy((float) row.getCell(bpRyIndex).getNumericCellValue());
                sfcBarcode.setBasGs((float) row.getCell(basGsIndex).getNumericCellValue());
                // 条码时间戳
                sfcBarcode.setSopBatno(yyMmDdHhMmSs);
                sfcBarcode.setBarCode(yyMmDdHhMmSs + String.format("%02d", serialNumber++));
                sfcBarcode.setUsr(LoginHelper.getUsername());
                sfcBarcode.setUsrName(LoginHelper.getUsername());
                sfcBarcode.setSfileName(originalFilename);
                sfcBarcode.setSysdt(new Date());

                sfcBarcodeList.add(sfcBarcode);
            }
            baseMapper.insertBatch(sfcBarcodeList);
            // this.saveBatch(sfcBarcodeList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return R.ok(sfcBarcodeList);
    }

    @Override
    public R getAbnormalData() {
        String cacheKey = CACHE_KEY + "list";
        List<SfcBarcode> sfcBarcodeList = RedisUtils.getCacheObject(cacheKey);
        if (sfcBarcodeList == null) {
            sfcBarcodeList = baseMapper.getAbnormalData();
            // 存入缓存
            if (CollUtil.isNotEmpty(sfcBarcodeList)) {
                RedisUtils.setCacheObject(cacheKey, sfcBarcodeList, CACHE_TTL);
            }
        }
        Set<Map<String, String>> moNoList = sfcBarcodeList.stream().map(SfcBarcode::getMoNo).distinct().map(mo -> {
            Map<String, String> map = new HashMap<>();
            map.put("moNo", mo);
            return map;
        }).collect(Collectors.toSet());
        Map<String, Object> result = new HashMap<>();
        result.put("sfcBarcodeList", sfcBarcodeList);
        result.put("moList", moNoList);
        return R.ok("success", result);
    }

    @Override
    public R queryBarcodeInfo(BarcodeQuery barcodeQuery) throws SQLException {
        String barCode = barcodeQuery.getBarCode();
        if (ObjectUtils.isEmpty(barCode)) {
            return R.fail("barcode is empty");
        }

        // 1. 检查条码类型
        LambdaQueryWrapper<SfcCodeRule> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SfcCodeRule::getRem, "治具", "模组", "IPQC");
        List<String> zpList = sfcCodeRuleMapper.selectList(wrapper).stream()
            .map(SfcCodeRule::getAcnDesc)
            .toList();

        InfoBarcodeDTO infoBarcodeDTO = new InfoBarcodeDTO();

        // 2. 处理治具、模组、IPQC类型的条码
        if (zpList.contains(barcodeQuery.getActType())) {
            String mo = barCode.substring(0, 12);
            String pType = barCode.length() >= 16 ? barCode.substring(barCode.length() - 4) : "0010";

            // 2.1 查询工单信息
            LambdaQueryWrapper<SapScanMaster> masterWrapper = new LambdaQueryWrapper<>();
            masterWrapper.eq(SapScanMaster::getMoNo, mo);
            SapScanMaster sapScanMaster = sapScanMasterMapper.selectOne(masterWrapper);
            if (ObjectUtils.isEmpty(sapScanMaster)) {
                return R.fail("mo is not exist in master");
            }
            if (sapScanMaster.getMoState1() != 0
                || sapScanMaster.getOrderQty() <= Objects.requireNonNullElse(sapScanMaster.getInStorageQty(), 0L)) {
                return R.fail("mo_state1非0或已入库数大于订单数量，不能再操作报工");
            }

            // 2.2 设置基本信息
            infoBarcodeDTO.setMoNo(mo);
            infoBarcodeDTO.setMoQty(Convert.toStr(sapScanMaster.getOrderQty()));
            infoBarcodeDTO.setPrdName(sapScanMaster.getDesc());
            infoBarcodeDTO.setDwgNo(sapScanMaster.getDrawNumber());
            infoBarcodeDTO.setProType(pType);

            // 2.3 查询工序信息
            SqlserverJdbcUtils.switchDataSource("sqlserver2");
            List<ProcessDTO> dtoList = SqlserverJdbcUtils.getProcessDTO(mo, pType);
            if (!ObjectUtils.isEmpty(dtoList)) {
                infoBarcodeDTO.setProName(dtoList.get(0).getProName());
                infoBarcodeDTO.setGzvs(dtoList.get(0).getGzvs());
                infoBarcodeDTO.setBasGs(dtoList.get(0).getBasGs() / 60);
            }
        } else {
            // 3. 处理其他类型的条码
            // 3.1 查询条码信息
            LambdaQueryWrapper<SfcBarcode> barcodeWrapper = new LambdaQueryWrapper<>();
            barcodeWrapper.eq(SfcBarcode::getBarCode, barCode)
                .isNull(SfcBarcode::getEndDate);
            SfcBarcode sfcBarcode = baseMapper.selectOne(barcodeWrapper);
            if (ObjectUtils.isEmpty(sfcBarcode)) {
                return R.fail("barcode不存在");
            }
            BeanUtils.copyProperties(sfcBarcode, infoBarcodeDTO);

            // 3.2 查询工单信息
            LambdaQueryWrapper<SapScanMaster> masterWrapper = new LambdaQueryWrapper<>();
            masterWrapper.eq(SapScanMaster::getMoNo, sfcBarcode.getMoNo());
            SapScanMaster sapScanMaster = sapScanMasterMapper.selectOne(masterWrapper);
            if (ObjectUtils.isEmpty(sapScanMaster)) {
                return R.fail("未找到相关工单信息");
            }

            if (sapScanMaster.getMoState1() != 0
                || sapScanMaster.getOrderQty() <= Objects.requireNonNullElse(sapScanMaster.getInStorageQty(), 0L)) {
                return R.fail("mo_state1非0或已入库数大于订单数量，不能再操作报工");
            }

            infoBarcodeDTO.setMoNo(sfcBarcode.getMoNo());
            infoBarcodeDTO.setMoQty(Convert.toStr(sapScanMaster.getOrderQty()));
            infoBarcodeDTO.setProductionQty(Convert.toStr(sapScanMaster.getProductionQty()));
            infoBarcodeDTO.setPartType(sapScanMaster.getPartType());
            infoBarcodeDTO.setMoState(sapScanMaster.getMoState());
            infoBarcodeDTO.setMoState1(sapScanMaster.getMoState1().intValue());

            LambdaQueryWrapper<SfcInfo> infoWrapper = new LambdaQueryWrapper<>();
            infoWrapper.eq(SfcInfo::getBarCode, barCode);
            List<SfcInfo> sfcInfos = sfcInfoMapper.selectList(infoWrapper);
            if (ObjectUtils.isEmpty(sfcInfos)) {
                infoBarcodeDTO.setNotScan(true);
            } else {
                Map<String, List<SfcInfo>> infoMap = sfcInfos.stream().collect(Collectors.groupingBy(SfcInfo::getZpType));
                for (List<SfcInfo> infoList : infoMap.values()) {
                    if (infoList.stream().mapToDouble(SfcInfo::getSumProd).max().orElse(0) != 100d) {
                        infoBarcodeDTO.setNotScan(true);
                    }
                }
            }

            SqlserverJdbcUtils.switchDataSource("sqlserver2");
            List<ProcessDTO> dtoList = SqlserverJdbcUtils.getProcessDTO(infoBarcodeDTO.getMoNo(), "0010");
            if (!ObjectUtils.isEmpty(dtoList)) {
                infoBarcodeDTO.setGzvs(dtoList.get(0).getGzvs());
            }
        }

        // 4. 检查是否需要工序信息
        LambdaQueryWrapper<SfcCodeRule> ruleWrapper = new LambdaQueryWrapper<>();
        ruleWrapper.eq(SfcCodeRule::getCodeType, "ZPTP")
            .eq(SfcCodeRule::getAcnDesc, barcodeQuery.getActType())
            .like(SfcCodeRule::getCodingRule, "GX=N");
        List<SfcCodeRule> sfcCodeRules = sfcCodeRuleMapper.selectList(ruleWrapper);
        if (!ObjectUtils.isEmpty(sfcCodeRules)) {
            infoBarcodeDTO.setProType("");
            infoBarcodeDTO.setProName("");
            infoBarcodeDTO.setGzvs("");
        }
        // 检查是否需要数量信息
        LambdaQueryWrapper<SfcCodeRule> ruleWrapper2 = new LambdaQueryWrapper<>();
        ruleWrapper2.eq(SfcCodeRule::getCodeType, "ZPTP")
            .eq(SfcCodeRule::getAcnDesc, barcodeQuery.getActType())
            .like(SfcCodeRule::getCodingRule, "QTY=N");
        List<SfcCodeRule> sfcCodeRules2 = sfcCodeRuleMapper.selectList(ruleWrapper2);
        if (!ObjectUtils.isEmpty(sfcCodeRules2)) {
            infoBarcodeDTO.setMoQty("");
        }

        return R.ok("success", infoBarcodeDTO);
    }

    @Override
    public R createBarcode(Map<String, String> map) throws Exception {
        String fileName = map.get("fileName");
        String mo = map.get("mo");
        String qty = map.get("qty");
        String dwgno = map.get("dwgno");
        String sopBatno = map.get("sopBatno");
        if (ObjectUtils.isEmpty(fileName) || ObjectUtils.isEmpty(mo) || ObjectUtils.isEmpty(qty)
            || ObjectUtils.isEmpty(dwgno)) {
            return R.fail("param cannot be empty");
        }
        // 相同数据添加失效时间
        UpdateWrapper<SfcBarcode> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("sfile_name", fileName).eq("mo_no", mo).set("end_date", LocalDateTime.now());
        baseMapper.update(updateWrapper);

        // 更新mo_no和图号
        UpdateWrapper<SfcBarcode> updateWrapper2 = new UpdateWrapper<>();
        updateWrapper2.eq("sfile_name", fileName).eq("sop_batno", sopBatno).isNull("end_date").set("mo_no", mo)
            .set("mo_dwgno", dwgno).set("sop_mo", mo + "-" + 10);
        baseMapper.update(updateWrapper2);

        // 获取原始条码信息
        QueryWrapper<SfcBarcode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sfile_name", fileName).eq("mo_no", mo).isNull("end_date");
        List<SfcBarcode> sfcBarcodeList = baseMapper.selectList(queryWrapper);

        // 生成每套条码里除了条码编码不同之外，其余内容均完全相同
        List<SfcBarcode> barcodes = new ArrayList<>();
        int cnt = Integer.parseInt(qty);
        // 其他套条码复制
        for (int i = 1; i < cnt; i++) {
            // 时间戳
            String yyMmDdHhMmSs = DateUtil.getYyMmDdHhMmSs();
            AtomicInteger serialNumber = new AtomicInteger(1);
            int number = i + 1;
            String sopMo = mo + "-" + number + "0";
            List<SfcBarcode> sfcBarcodes = sfcBarcodeList.stream().map(ob -> {
                SfcBarcode sfcBarcode = new SfcBarcode();
                BeanUtil.copyProperties(ob, sfcBarcode);
                sfcBarcode.setId(null);
                sfcBarcode.setSopMo(sopMo);
                sfcBarcode.setSopBatno(yyMmDdHhMmSs);
                sfcBarcode.setBarCode(yyMmDdHhMmSs + String.format("%02d", serialNumber.getAndIncrement()));
                return sfcBarcode;
            }).toList();
            barcodes.addAll(sfcBarcodes);
            Thread.sleep(1000);
        }
        // 其他套条码入库
        if (!ObjectUtils.isEmpty(barcodes)) {
            saveBatch(barcodes);
        }

        // 生成excel
        String name = mo + "_" + fileName;
        ClassPathResource templateResource = new ClassPathResource(TEMPLATE_PATH);
        try (InputStream inputStream = templateResource.getStream();
             Workbook workbook = new XSSFWorkbook(inputStream);
             ByteArrayOutputStream os = new ByteArrayOutputStream();) {
            // 加上已有的数据
            barcodes.addAll(sfcBarcodeList);
            Map<String, List<SfcBarcode>> listMap =
                barcodes.stream().collect(Collectors.groupingBy(SfcBarcode::getSopBatno));
            AtomicInteger number = new AtomicInteger(1);
            listMap.forEach((key, value) -> {
                // 复制第一个sheet作为模板并插入新工作表
                Sheet copiedSheet = workbook.cloneSheet(0);
                workbook.setSheetName(workbook.getSheetIndex(copiedSheet), key);// 重命名复制的表
                String tab = "-" + number.getAndIncrement() + "0";
                for (int i = 0; i < value.size(); i++) {
                    SfcBarcode barcode = value.get(i);
                    // 准备写入的数据
                    String[] dataToWrite =
                        {"", "*" + barcode.getBarCode() + "*", barcode.getProName(), barcode.getSopMo()};
                    // 在第一个sheet的第2列写入数据
                    for (int j = 0; j < dataToWrite.length; j++) {
                        int index = j + 4 * i;
                        Row row = copiedSheet.getRow(index);
                        if (row == null) {
                            row = copiedSheet.createRow(index);
                        }
                        Cell cell = row.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        cell.setCellValue(dataToWrite[j]);
                    }
                }
            });

            // 删除原始模板表
            workbook.removeSheetAt(0);

            // 保存更改
            workbook.write(os);

            // // 将 ByteArrayOutputStream 的内容写入本地文件
            // String outputFilePath = "D:" + File.separator + name; // 指定输出文件路径
            // try (FileOutputStream fos = new FileOutputStream(outputFilePath)) {
            //     fos.write(os.toByteArray());
            // }
            OssClient storage = OssFactory.instance("minio");
            storage.createBucket(Constants.BUCKET_BARCODE);
            byte[] bytes = os.toByteArray();
            storage.uploadSuffix(bytes, ".xlsx", name, Constants.BUCKET_BARCODE,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        return R.ok("success", name);
    }

    @Override
    public R queryAnotherBarcodeInfo(BarcodeQuery barcodeQuery) {
        String barCode = barcodeQuery.getBarCode();
        if (ObjectUtils.isEmpty(barCode)) {
            return R.fail("barcode is empty");
        }

        InfoBarcodeDTO infoBarcodeDTO = new InfoBarcodeDTO();

        LambdaQueryWrapper<SfcBarcode> barcodeWrapper = new LambdaQueryWrapper<>();
        barcodeWrapper.eq(SfcBarcode::getBarCode, barCode).isNull(SfcBarcode::getEndDate);
        SfcBarcode sfcBarcode = baseMapper.selectOne(barcodeWrapper);
        if (ObjectUtils.isEmpty(sfcBarcode)) {
            return R.fail("barcode不存在");
        }
        infoBarcodeDTO.setActType(sfcBarcode.getActType());
        infoBarcodeDTO.setMoNo(sfcBarcode.getMoNo());
        infoBarcodeDTO.setProType(sfcBarcode.getProType());
        infoBarcodeDTO.setProName(sfcBarcode.getProName());
        return R.ok("success", infoBarcodeDTO);
    }
}
