package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcSalm;
import org.dromara.sfc.domain.bo.SfcSalmBo;
import org.dromara.sfc.domain.vo.SfcSalmVo;
import org.dromara.sfc.mapper.SfcSalmMapper;
import org.dromara.sfc.service.ISfcSalmService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 人员设定记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@RequiredArgsConstructor
@Service
public class SfcSalmServiceImpl implements ISfcSalmService {

    private final SfcSalmMapper baseMapper;

    /**
     * 查询人员设定记录
     *
     * @param id 主键
     * @return 人员设定记录
     */
    @Override
    public SfcSalmVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询人员设定记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员设定记录分页列表
     */
    @Override
    public TableDataInfo<SfcSalmVo> queryPageList(SfcSalmBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcSalm> lqw = buildQueryWrapper(bo);
        Page<SfcSalmVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的人员设定记录列表
     *
     * @param bo 查询条件
     * @return 人员设定记录列表
     */
    @Override
    public List<SfcSalmVo> queryList(SfcSalmBo bo) {
        LambdaQueryWrapper<SfcSalm> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcSalm> buildQueryWrapper(SfcSalmBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcSalm> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SfcSalm::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getSalNo()), SfcSalm::getSalNo, bo.getSalNo());
        lqw.like(StringUtils.isNotBlank(bo.getName()), SfcSalm::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getSType()), SfcSalm::getSType, bo.getSType());
        lqw.eq(StringUtils.isNotBlank(bo.getSGroup()), SfcSalm::getSGroup, bo.getSGroup());
        lqw.eq(StringUtils.isNotBlank(bo.getDep()), SfcSalm::getDep, bo.getDep());
        lqw.eq(bo.getDutOtD() != null, SfcSalm::getDutOtD, bo.getDutOtD());
        lqw.like(StringUtils.isNotBlank(bo.getUsrName()), SfcSalm::getUsrName, bo.getUsrName());
        lqw.eq(StringUtils.isNotBlank(bo.getHost()), SfcSalm::getHost, bo.getHost());
        lqw.eq(bo.getSysdt() != null, SfcSalm::getSysdt, bo.getSysdt());
        lqw.eq(bo.getFlag() != null, SfcSalm::getFlag, bo.getFlag());
        return lqw;
    }

    /**
     * 新增人员设定记录
     *
     * @param bo 人员设定记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcSalmBo bo) {
        SfcSalm add = MapstructUtils.convert(bo, SfcSalm.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改人员设定记录
     *
     * @param bo 人员设定记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcSalmBo bo) {
        SfcSalm update = MapstructUtils.convert(bo, SfcSalm.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcSalm entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除人员设定记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
