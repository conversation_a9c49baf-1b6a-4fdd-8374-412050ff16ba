package org.dromara.sfc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcRygstjb;
import org.dromara.sfc.domain.bo.SfcRygstjbBo;
import org.dromara.sfc.domain.vo.SfcRygstjbVo;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;

/**
 * 装配工时统计表Service接口
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
public interface ISfcRygstjbService extends IService<SfcRygstjb> {

    /**
     * 查询装配工时统计表
     *
     * @param id 主键
     * @return 装配工时统计表
     */
    SfcRygstjbVo queryById(Long id);

    /**
     * 分页查询装配工时统计表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配工时统计表分页列表
     */
    TableDataInfo<SfcRygstjbVo> queryPageList(SfcRygstjbBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的装配工时统计表列表
     *
     * @param bo 查询条件
     * @return 装配工时统计表列表
     */
    List<SfcRygstjbVo> queryList(SfcRygstjbBo bo);

    /**
     * 新增装配工时统计表
     *
     * @param bo 装配工时统计表
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcRygstjbBo bo);

    /**
     * 修改装配工时统计表
     *
     * @param bo 装配工时统计表
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcRygstjbBo bo);

    /**
     * 校验并批量删除装配工时统计表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 装配工时统计表统计
     * @param bo
     */
    void statistic(SfcRygstjbBo bo) throws SQLException;
}
