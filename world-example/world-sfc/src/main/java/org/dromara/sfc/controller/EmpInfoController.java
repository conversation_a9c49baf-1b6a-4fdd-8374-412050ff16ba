package org.dromara.sfc.controller;

import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.EmpInfoBo;
import org.dromara.sfc.domain.vo.EmpInfoVo;
import org.dromara.sfc.service.IEmpInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 人员信息配置
 * 前端访问路由地址为:/sfc/empInfo
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/empInfo")
public class EmpInfoController extends BaseController {

    private final IEmpInfoService empInfoService;

    /**
     * 查询人员信息配置列表
     */
    @SaCheckPermission("sfc:empInfo:list")
    @GetMapping("/list")
    public TableDataInfo<EmpInfoVo> list(EmpInfoBo bo, PageQuery pageQuery) {
        return empInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人员信息配置列表
     */
    @SaCheckPermission("sfc:empInfo:export")
    @Log(title = "人员信息配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(EmpInfoBo bo, HttpServletResponse response) {
        List<EmpInfoVo> list = empInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员信息配置", EmpInfoVo.class, response);
    }

    /**
     * 获取人员信息配置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("sfc:empInfo:query")
    @GetMapping("/{id}")
    public R<EmpInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(empInfoService.queryById(id));
    }

    /**
     * 新增人员信息配置
     */
    @SaCheckPermission("sfc:empInfo:add")
    @Log(title = "人员信息配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody EmpInfoBo bo) {
        return toAjax(empInfoService.insertByBo(bo));
    }

    /**
     * 修改人员信息配置
     */
    @SaCheckPermission("sfc:empInfo:edit")
    @Log(title = "人员信息配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody EmpInfoBo bo) {
        return toAjax(empInfoService.updateByBo(bo));
    }

    /**
     * 删除人员信息配置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("sfc:empInfo:remove")
    @Log(title = "人员信息配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(empInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
