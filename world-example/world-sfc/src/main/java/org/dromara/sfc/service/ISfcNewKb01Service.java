package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcNewKb01;
import org.dromara.sfc.domain.bo.SfcNewKb01Bo;
import org.dromara.sfc.domain.dto.WkXlDataDTO;
import org.dromara.sfc.domain.vo.SfcNewKb01Vo;

import java.util.Collection;
import java.util.List;

/**
 * 装配看板记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
public interface ISfcNewKb01Service {

    /**
     * 查询装配看板记录
     *
     * @param id 主键
     * @return 装配看板记录
     */
    SfcNewKb01Vo queryById(Long id);

    /**
     * 分页查询装配看板记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配看板记录分页列表
     */
    TableDataInfo<SfcNewKb01Vo> queryPageList(SfcNewKb01Bo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的装配看板记录列表
     *
     * @param bo 查询条件
     * @return 装配看板记录列表
     */
    List<SfcNewKb01Vo> queryList(SfcNewKb01Bo bo);

    /**
     * 新增装配看板记录
     *
     * @param bo 装配看板记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcNewKb01Bo bo);

    /**
     * 修改装配看板记录
     *
     * @param bo 装配看板记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcNewKb01Bo bo);

    /**
     * 校验并批量删除装配看板记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取装配看板前一天记录列表
     */
    List<SfcNewKb01Vo> fetchSfcNewKb01FromSysdtTodayOrderedById();

    List<WkXlDataDTO> getWkXlData();

    List<SfcNewKb01> getSfcNewKbList1(PageQuery pageQuery);

    Long getSfcNewKbCount();
}
