package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.ipwhite.annotation.IpWhitelist;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.dto.WeightDataResponse;
import org.dromara.sfc.domain.dto.WmsWeightDataDTO;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 第三方接口定义
 * VB对外WMS对接接口
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Slf4j
@RestController
@SaIgnore
@RequestMapping("/v1/api")
public class VbWmsMasterController extends BaseController {


    private static final String SELECT_SQL = "SELECT id, mo_no, q_weight FROM mo_weight WHERE mo_no = ?";
    private static final String INSERT_SQL = "INSERT INTO mo_weight(MO_NO, Q_WEIGHT) VALUES(?, ?)";
    private static final String UPDATE_SQL = "UPDATE mo_weight SET Q_WEIGHT = ? WHERE mo_no = ?";

    /**
     * 保存扫码数据到VB数据库
     */
    @PostMapping("/savaWms")
    @Transactional(rollbackFor = Exception.class)
    @IpWhitelist
    public WeightDataResponse handleWeightData(@RequestBody List<WmsWeightDataDTO> listRequest) {
        // 1. 参数验证
        if (listRequest == null || listRequest.isEmpty()) {
            log.error("请求参数为空");
            return WeightDataResponse.fail("请求参数不能为空");
        }

        // 2. 切换数据源
        SqlserverJdbcUtils.switchDataSource("vb_sqlserver");

        // 3. 处理每条数据
        for (WmsWeightDataDTO request : listRequest) {
            try {
                // 3.1 验证数据
                if (request.getTicketsNum() == null || request.getTicketsNum().trim().isEmpty()) {
                    log.error("单号为空");
                    return WeightDataResponse.fail("单号不能为空");
                }

                if (request.getWeight() == null || request.getWeight() <= 0) {
                    log.error("重量无效，单号：{}，重量：{}", request.getTicketsNum(), request.getWeight());
                    return WeightDataResponse.fail("重量必须大于0");
                }
                //获取12位单号
                String ticketsNum = request.getTicketsNum().substring(0, 12);
                // 3.2 检查记录是否存在
                int recordCount = SqlserverJdbcUtils.getRecordCount(SELECT_SQL,
                    Collections.singletonList(ticketsNum));

                // 3.3 执行插入或更新操作
                if (recordCount == 0) {
                    log.info("新增称重记录，单号：{}", ticketsNum);
                    List<Object[]> params = Collections.singletonList(
                        new Object[]{ticketsNum, request.getWeight()});
                    SqlserverJdbcUtils.executeBatchInsert(INSERT_SQL, params);
                } else {
                    log.info("更新称重记录，单号：{}", ticketsNum);
                    SqlserverJdbcUtils.executeUpdate(UPDATE_SQL,
                        Arrays.asList(request.getWeight(), ticketsNum));
                }

            } catch (SQLException e) {
                log.error("数据库操作异常，单号：{}，错误信息：{}", request.getTicketsNum(), e.getMessage(), e);
                return WeightDataResponse.fail("数据库操作异常：" + e.getMessage());
            } catch (Exception e) {
                log.error("系统异常，单号：{}，错误信息：{}", request.getTicketsNum(), e.getMessage(), e);
                return WeightDataResponse.fail("系统异常：" + e.getMessage());
            }
        }

        log.info("称重数据处理成功，共处理{}条数据", listRequest.size());
        return WeightDataResponse.ok();
    }
}
