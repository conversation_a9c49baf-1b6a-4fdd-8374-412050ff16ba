package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcKbWkXl;
import org.dromara.sfc.domain.bo.SfcKbWkXlBo;
import org.dromara.sfc.domain.vo.SfcKbWkXlVo;
import org.dromara.sfc.mapper.SfcKbWkXlMapper;
import org.dromara.sfc.service.ISfcKbWkXlService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 装配看板隐藏数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@RequiredArgsConstructor
@Service
public class SfcKbWkXlServiceImpl implements ISfcKbWkXlService {

    private final SfcKbWkXlMapper baseMapper;

    /**
     * 查询装配看板隐藏数据
     *
     * @param id 主键
     * @return 装配看板隐藏数据
     */
    @Override
    public SfcKbWkXlVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询装配看板隐藏数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配看板隐藏数据分页列表
     */
    @Override
    public TableDataInfo<SfcKbWkXlVo> queryPageList(SfcKbWkXlBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcKbWkXl> lqw = buildQueryWrapper(bo);
        Page<SfcKbWkXlVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的装配看板隐藏数据列表
     *
     * @param bo 查询条件
     * @return 装配看板隐藏数据列表
     */
    @Override
    public List<SfcKbWkXlVo> queryList(SfcKbWkXlBo bo) {
        LambdaQueryWrapper<SfcKbWkXl> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcKbWkXl> buildQueryWrapper(SfcKbWkXlBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcKbWkXl> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SfcKbWkXl::getId);
        lqw.between(params.get("beginSysDt") != null && params.get("endSysDt") != null,
            SfcKbWkXl::getSysDt ,params.get("beginSysDt"), params.get("endSysDt"));
        lqw.eq(StringUtils.isNotBlank(bo.getWkNo()), SfcKbWkXl::getWkNo, bo.getWkNo());
        lqw.like(StringUtils.isNotBlank(bo.getWkName()), SfcKbWkXl::getWkName, bo.getWkName());
        return lqw;
    }

    /**
     * 新增装配看板隐藏数据
     *
     * @param bo 装配看板隐藏数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcKbWkXlBo bo) {
        SfcKbWkXl add = MapstructUtils.convert(bo, SfcKbWkXl.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装配看板隐藏数据
     *
     * @param bo 装配看板隐藏数据
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcKbWkXlBo bo) {
        SfcKbWkXl update = MapstructUtils.convert(bo, SfcKbWkXl.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcKbWkXl entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除装配看板隐藏数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
