package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.util.IOUtils;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcBarcodeBo;
import org.dromara.sfc.domain.vo.SfcBarcodeVo;
import org.dromara.sfc.query.BarcodeQuery;
import org.dromara.sfc.service.ISfcBarcodeService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * 非生产条码
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/barcode")
public class SfcBarcodeController extends BaseController {

    private final ISfcBarcodeService sfcBarcodeService;

    /**
     * 查询非生产条码列表
     */
    @SaCheckPermission("barcode:barcode:list")
    @GetMapping("/list")
    public TableDataInfo<SfcBarcodeVo> list(SfcBarcodeBo bo, PageQuery pageQuery) {
        return sfcBarcodeService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出非生产条码列表
     */
    @SaCheckPermission("barcode:barcode:export")
    @Log(title = "非生产条码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcBarcodeBo bo, HttpServletResponse response) {
        List<SfcBarcodeVo> list = sfcBarcodeService.queryList(bo);
        ExcelUtil.exportExcel(list, "非生产条码", SfcBarcodeVo.class, response);
    }

    /**
     * 获取非生产条码详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("barcode:barcode:query")
    @GetMapping("/{id}")
    public R<SfcBarcodeVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(sfcBarcodeService.queryById(id));
    }

    /**
     * 新增非生产条码
     */
    @SaCheckPermission("barcode:barcode:add")
    @Log(title = "非生产条码", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcBarcodeBo bo) {
        return toAjax(sfcBarcodeService.insertByBo(bo));
    }

    /**
     * 修改非生产条码
     */
    @SaCheckPermission("barcode:barcode:edit")
    @Log(title = "非生产条码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcBarcodeBo bo) {
        return toAjax(sfcBarcodeService.updateByBo(bo));
    }

    /**
     * 删除非生产条码
     *
     * @param ids 主键串
     */
    @SaCheckPermission("barcode:barcode:remove")
    @Log(title = "非生产条码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcBarcodeService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 非生产条码生成
     *
     * @param response 响应
     * @param type     类型
     */
    @PostMapping("/generate")
    @Log(title = "非生产条码生成", businessType = BusinessType.EXPORT)
    public void generaterAnotherBarcode(HttpServletResponse response, String type) {
        try {
            sfcBarcodeService.generateAnotherBarcode(response, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 处理上传SOP文件
     *
     * @param file 文件
     * @return
     */
    @PostMapping("/upload")
    @Log(title = "装配SOP条码导入", businessType = BusinessType.IMPORT)
    public R upload(@RequestParam("file") MultipartFile file) {
        System.out.println("upload");
        return sfcBarcodeService.dealSopFile(file);
    }

    /**
     * 生成条码
     *
     * @param map 参数
     */
    @PostMapping("createBarcode")
    @Log(title = "装配SOP条码生成", businessType = BusinessType.INSERT)
    public R createBarcode(@RequestBody Map<String, String> map) {
        try {
            return sfcBarcodeService.createBarcode(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 装配SOP条码导出
     *
     * @param response 响应
     * @param fileName 文件名
     */
    @PostMapping("/export/{fileName}")
    public void export(HttpServletResponse response, @PathVariable String fileName) {
        // 对文件名进行URL编码并替换 "+" 为 "%20"
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
        // 配置响应
        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
        OssClient storage = OssFactory.instance("minio");
        // 将PDF文件内容写入响应输出流
        try (InputStream inputStream = storage.getObjectContent(fileName, Constants.BUCKET_BARCODE);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            IOUtils.closeQuietly(outputStream);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取装配未扫描的条码
     */
    @GetMapping("/notScan")
    public R notScan() {
        return sfcBarcodeService.getAbnormalData();
    }

    /**
     * 联表查询
     *
     * @param barcodeQuery 条件
     * @return Response
     */
    @GetMapping("/query")
    public R query(BarcodeQuery barcodeQuery) {
        try {
            return sfcBarcodeService.queryBarcodeInfo(barcodeQuery);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @GetMapping("/query/another")
    public R queryAntoher(BarcodeQuery barcodeQuery) {
            return sfcBarcodeService.queryAnotherBarcodeInfo(barcodeQuery);
    }
}
