package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.bo.SapScanMasterBo;
import org.dromara.sfc.domain.vo.SapScanMasterVo;

import java.sql.SQLException;
import java.util.Collection;
import java.util.List;

/**
 * sapMaster信息Service接口
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
public interface ISapScanMasterService {

    /**
     * 查询sapMaster信息
     *
     * @param id 主键
     * @return sapMaster信息
     */
    SapScanMasterVo queryById(Long id);

    /**
     * 分页查询sapMaster信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return sapMaster信息分页列表
     */
    TableDataInfo<SapScanMasterVo> queryPageList(SapScanMasterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的sapMaster信息列表
     *
     * @param bo 查询条件
     * @return sapMaster信息列表
     */
    List<SapScanMasterVo> queryList(SapScanMasterBo bo);

    /**
     * 新增sapMaster信息
     *
     * @param bo sapMaster信息
     * @return 是否新增成功
     */
    Boolean insertByBo(SapScanMasterBo bo);

    /**
     * 修改sapMaster信息
     *
     * @param bo sapMaster信息
     * @return 是否修改成功
     */
    Boolean updateByBo(SapScanMasterBo bo);

    /**
     * 校验并批量删除sapMaster信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据MO查询Master信息
     * @param mo
     * @return
     */
    SapScanMaster getMasterByMo(String mo) throws SQLException;




}
