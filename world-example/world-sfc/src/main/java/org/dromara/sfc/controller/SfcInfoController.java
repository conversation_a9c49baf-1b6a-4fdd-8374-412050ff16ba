package org.dromara.sfc.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.sfc.domain.SfcInfo;
import org.dromara.sfc.service.ISfcInfoService;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

/**
 * sfc_info SFC扫描记录明细表控制层
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/information/sfcInfo")
public class SfcInfoController {

    @Resource
    private ISfcInfoService sfcInfoService;

    /**
     * 添加记录
     *
     * @param sfcInfos 记录
     * @return Response
     */
    @PostMapping("/add")
    @Log(title = "SFC报工记录", businessType = BusinessType.INSERT)
    public R add(@RequestBody List<SfcInfo> sfcInfos) {
        if (ObjectUtils.isEmpty(sfcInfos)) {
            return R.fail("保存数据不能为空");
        }
        sfcInfoService.checkData(sfcInfos);

        List<SfcInfo> filterInfos = sfcInfoService.filter(sfcInfos);
        if (!ObjectUtils.isEmpty(filterInfos)) {
            List<SfcInfo> transInfos = sfcInfoService.trans(filterInfos);

            sfcInfoService.saveOrUpdateBatch(transInfos);

            // 报工SAP
            sfcInfoService.uploadSap(transInfos);
        }

        return R.ok();
    }

    /**
     * 添加记录
     *
     * @param sfcInfos 记录
     * @return Response
     */
    @PostMapping("/add/another")
    @Log(title = "SFC非生产报工记录", businessType = BusinessType.INSERT)
    public R addAnother(@RequestBody List<SfcInfo> sfcInfos) {
        if (ObjectUtils.isEmpty(sfcInfos)) {
            return R.fail("保存数据不能为空");
        }
        sfcInfoService.checkData(sfcInfos);

        List<SfcInfo> transInfos = sfcInfoService.trans(sfcInfos);

        sfcInfoService.saveOrUpdateBatch(transInfos);

        return R.ok();
    }

    /**
     * 查询记录
     *
     * @param sfcInfo 参数
     * @return List<SfcInfo>
     */
    @GetMapping("/data")
    public R getInfos(SfcInfo sfcInfo) {
        return sfcInfoService.getInfoList(sfcInfo);
    }

    /**
     * 查询记录
     *
     * @param params 参数
     * @return List<SfcInfo>
     */
    @GetMapping("/barcode")
    public R getInfosByBarcodeData(@RequestParam Map<String, String> params) {
        List<SfcInfo> sfcInfos = sfcInfoService.getInfosByBarcodeData(params);
        return R.ok(sfcInfos);
    }

}
