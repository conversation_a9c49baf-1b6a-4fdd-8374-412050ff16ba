package org.dromara.sfc.service;

import java.util.Collection;
import java.util.List;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.EmpInfoBo;
import org.dromara.sfc.domain.vo.EmpInfoVo;

/**
 * 人员信息配置Service接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IEmpInfoService {

    /**
     * 查询人员信息配置
     *
     * @param id 主键
     * @return 人员信息配置
     */
    EmpInfoVo queryById(Long id);

    /**
     * 分页查询人员信息配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员信息配置分页列表
     */
    TableDataInfo<EmpInfoVo> queryPageList(EmpInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的人员信息配置列表
     *
     * @param bo 查询条件
     * @return 人员信息配置列表
     */
    List<EmpInfoVo> queryList(EmpInfoBo bo);

    /**
     * 新增人员信息配置
     *
     * @param bo 人员信息配置
     * @return 是否新增成功
     */
    Boolean insertByBo(EmpInfoBo bo);

    /**
     * 修改人员信息配置
     *
     * @param bo 人员信息配置
     * @return 是否修改成功
     */
    Boolean updateByBo(EmpInfoBo bo);

    /**
     * 校验并批量删除人员信息配置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
