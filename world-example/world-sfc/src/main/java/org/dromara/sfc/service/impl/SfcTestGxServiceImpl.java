package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcTestGx;
import org.dromara.sfc.domain.bo.SfcTestGxBo;
import org.dromara.sfc.domain.vo.SfcTestGxVo;
import org.dromara.sfc.mapper.SfcTestGxMapper;
import org.dromara.sfc.service.ISfcTestGxService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 调试SOP设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@RequiredArgsConstructor
@Service
public class SfcTestGxServiceImpl implements ISfcTestGxService {

    private final SfcTestGxMapper baseMapper;

    /**
     * 查询调试SOP设置
     *
     * @param id 主键
     * @return 调试SOP设置
     */
    @Override
    public SfcTestGxVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询调试SOP设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 调试SOP设置分页列表
     */
    @Override
    public TableDataInfo<SfcTestGxVo> queryPageList(SfcTestGxBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcTestGx> lqw = buildQueryWrapper(bo);
        Page<SfcTestGxVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的调试SOP设置列表
     *
     * @param bo 查询条件
     * @return 调试SOP设置列表
     */
    @Override
    public List<SfcTestGxVo> queryList(SfcTestGxBo bo) {
        LambdaQueryWrapper<SfcTestGx> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcTestGx> buildQueryWrapper(SfcTestGxBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcTestGx> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getTsGxNo()), SfcTestGx::getTsGxNo, bo.getTsGxNo());
        lqw.like(StringUtils.isNotBlank(bo.getTsGxName()), SfcTestGx::getTsGxName, bo.getTsGxName());
        lqw.eq(bo.getBasGs() != null, SfcTestGx::getBasGs, bo.getBasGs());
        lqw.eq(StringUtils.isNotBlank(bo.getRem()), SfcTestGx::getRem, bo.getRem());
        lqw.eq(bo.getFlag()!=null, SfcTestGx::getFlag, bo.getFlag());
        lqw.like(StringUtils.isNotBlank(bo.getSfileName()), SfcTestGx::getSfileName, bo.getSfileName());
        lqw.like(StringUtils.isNotBlank(bo.getUsrId()), SfcTestGx::getUsrId, bo.getUsrId());
        return lqw;
    }

    /**
     * 新增调试SOP设置
     *
     * @param bo 调试SOP设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcTestGxBo bo) {
        SfcTestGx add = MapstructUtils.convert(bo, SfcTestGx.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改调试SOP设置
     *
     * @param bo 调试SOP设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcTestGxBo bo) {
        SfcTestGx update = MapstructUtils.convert(bo, SfcTestGx.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcTestGx entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除调试SOP设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
