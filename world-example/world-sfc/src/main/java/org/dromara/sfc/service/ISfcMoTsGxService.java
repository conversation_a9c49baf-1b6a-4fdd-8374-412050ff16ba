package org.dromara.sfc.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcMoTsGx;
import org.dromara.sfc.domain.bo.SfcMoTsGxBo;
import org.dromara.sfc.domain.dto.TestGxDTO;
import org.dromara.sfc.domain.vo.SfcMoTsGxVo;
import org.dromara.sfc.domain.vo.TestGxInformation;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 调试SOP条码Service接口
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
public interface ISfcMoTsGxService {

    /**
     * 查询调试SOP条码
     *
     * @param id 主键
     * @return 调试SOP条码
     */
    SfcMoTsGxVo queryById(Long id);

    /**
     * 分页查询调试SOP条码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 调试SOP条码分页列表
     */
    TableDataInfo<SfcMoTsGxVo> queryPageList(SfcMoTsGxBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的调试SOP条码列表
     *
     * @param bo 查询条件
     * @return 调试SOP条码列表
     */
    List<SfcMoTsGxVo> queryList(SfcMoTsGxBo bo);

    /**
     * 新增调试SOP条码
     *
     * @param bo 调试SOP条码
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcMoTsGxBo bo);

    /**
     * 修改调试SOP条码
     *
     * @param bo 调试SOP条码
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcMoTsGxBo bo);

    /**
     * 校验并批量删除调试SOP条码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 生成条码
     *
     * @param testGxInformation 工序信息
     * @return Response
     */
    R generateTsBarcode(TestGxInformation testGxInformation);


    /**
     * 处理SOP文件
     *
     * @param file
     * @return
     */
    R<List<TestGxDTO>> dealSopFile(MultipartFile file);

    R queryMoTsGxInfo(SfcMoTsGx moTsGxQuery);
}
