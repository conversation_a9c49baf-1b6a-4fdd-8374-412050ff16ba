package org.dromara.sfc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.sfc.domain.SfcLdmzLink;
import org.dromara.sfc.query.LdmzLinkQuery;
import org.dromara.sfc.service.ISfcLdmzLinkService;
import org.dromara.sfc.utils.QueryWrapperUtil;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/information/ldmzlink")
public class SfcLdmzLinkController {
    private final ISfcLdmzLinkService sfcLdmzLinkService;

    public SfcLdmzLinkController(ISfcLdmzLinkService sfcLdmzLinkService) {
        this.sfcLdmzLinkService = sfcLdmzLinkService;
    }

    /**
     * 分页查询
     *
     * @param ldmzLinkQuery
     * @param pageable
     * @return
     */
    @GetMapping("/list")
    public R<IPage<SfcLdmzLink>> list(LdmzLinkQuery ldmzLinkQuery, Pageable pageable) {
        Page<SfcLdmzLink> page = QueryWrapperUtil.getPage(pageable);
        QueryWrapper<SfcLdmzLink> queryWrapper = QueryWrapperUtil.buildQueryWrapper(ldmzLinkQuery, SfcLdmzLink.class);
        sfcLdmzLinkService.page(page, queryWrapper);
        return R.ok(page);
    }


    @Log(title = "流道模组绑定", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    public R add(@RequestBody List<SfcLdmzLink> sfcLdmzLinkList) {
        return sfcLdmzLinkService.addList(sfcLdmzLinkList);
    }
}
