package org.dromara.sfc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcLdmzLink;
import org.dromara.sfc.domain.bo.SfcKbWkXlBo;
import org.dromara.sfc.domain.vo.SfcKbWkXlVo;

import java.util.List;

public interface ISfcLdmzLinkService extends IService<SfcLdmzLink> {
    /**
     * 保存模组绑定信息
     *
     * @param sfcLdmzLinkList 绑定信息
     * @return 保存结果
     */
    R addList(List<SfcLdmzLink> sfcLdmzLinkList);

}
