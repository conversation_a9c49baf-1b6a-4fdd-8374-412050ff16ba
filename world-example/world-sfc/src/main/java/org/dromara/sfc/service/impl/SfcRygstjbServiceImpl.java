package org.dromara.sfc.service.impl;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcInfo;
import org.dromara.sfc.domain.SfcRygstjb;
import org.dromara.sfc.domain.SfcSalm;
import org.dromara.sfc.domain.bo.SfcRygstjbBo;
import org.dromara.sfc.domain.dto.EmpGsDTO;
import org.dromara.sfc.domain.vo.SfcRygstjbVo;
import org.dromara.sfc.mapper.SfcInfoMapper;
import org.dromara.sfc.mapper.SfcRygstjbMapper;
import org.dromara.sfc.mapper.SfcSalmMapper;
import org.dromara.sfc.service.ISfcRygstjbService;
import org.dromara.sfc.utils.SafeMathUtils;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;

/**
 * 装配工时统计表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@RequiredArgsConstructor
@Service
public class SfcRygstjbServiceImpl extends ServiceImpl<SfcRygstjbMapper, SfcRygstjb> implements ISfcRygstjbService {

    private final SfcRygstjbMapper baseMapper;
    private final SfcInfoMapper sfcInfoMapper;
    private final SfcSalmMapper sfcSalmMapper;

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_TIME_FORMATTER2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final List<String> DEV_ZP_TYPE_LIST = List.of("机构装配", "电气装配", "组件检验", "整机检验");
    private static final List<String> JG_ZP_TYPE_LIST = List.of("治具装配", "治具电气装配", "治具机构装配");

    private static final List<String> DEP_LIST = List.of("装配一组", "装配二组", "0101320201", "0101320202");
    private static final List<String> ZP_TYPE_LIST = new ArrayList<>(DEV_ZP_TYPE_LIST) {
        {
            addAll(JG_ZP_TYPE_LIST);
        }
    };

    /**
     * 查询装配工时统计表
     *
     * @param id 主键
     * @return 装配工时统计表
     */
    @Override
    public SfcRygstjbVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询装配工时统计表列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配工时统计表分页列表
     */
    @Override
    public TableDataInfo<SfcRygstjbVo> queryPageList(SfcRygstjbBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcRygstjb> lqw = buildQueryWrapper(bo);
        Page<SfcRygstjbVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的装配工时统计表列表
     *
     * @param bo 查询条件
     * @return 装配工时统计表列表
     */
    @Override
    public List<SfcRygstjbVo> queryList(SfcRygstjbBo bo) {
        LambdaQueryWrapper<SfcRygstjb> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcRygstjb> buildQueryWrapper(SfcRygstjbBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcRygstjb> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SfcRygstjb::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getCntType()), SfcRygstjb::getCntType, bo.getCntType());
        lqw.between(params.get("beginSysDt") != null && params.get("endSysDt") != null,
            SfcRygstjb::getSysDt, params.get("beginSysDt"), params.get("endSysDt"));
        return lqw;
    }

    /**
     * 新增装配工时统计表
     *
     * @param bo 装配工时统计表
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcRygstjbBo bo) {
        SfcRygstjb add = MapstructUtils.convert(bo, SfcRygstjb.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装配工时统计表
     *
     * @param bo 装配工时统计表
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcRygstjbBo bo) {
        SfcRygstjb update = MapstructUtils.convert(bo, SfcRygstjb.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcRygstjb entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除装配工时统计表信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public void statistic(SfcRygstjbBo bo) throws SQLException {
        Map<String, Object> params = bo.getParams();
        if (ObjectUtils.isEmpty(params) || ObjectUtils.isEmpty(params.get("beginSysDt")) || ObjectUtils.isEmpty(params.get("endSysDt"))) {
            throw new IllegalArgumentException("统计日期格式错误");
        }
        // // 获取 Salm 数据并分组
        // List<SfcSalm> salmList = sfcSalmMapper.selectList(new LambdaQueryWrapper<SfcSalm>()
        //     .isNull(SfcSalm::getDutOtD)
        //     .isNotNull(SfcSalm::getSGroup));
        // Map<String, List<SfcSalm>> salmGroupMap = salmList.stream()
        //     .collect(Collectors.groupingBy(SfcSalm::getSGroup, Collectors.mapping(Function.identity(), Collectors.toList())));

        // // 获取 SfcInfo 数据
        // List<SfcInfo> sfcInfos = sfcInfoMapper.selectList(buildSfcInfoQueryWrapper(params));

        // // 处理统计数据
        // List<SfcRygstjb> sfcRygstjbList = new ArrayList<>();
        //
        // // 统计设备数据
        // processEquipmentStatistics(sfcInfos, salmGroupMap.get("设备装配"), sfcRygstjbList);
        // // 统计治具数据
        // processFixtureStatistics(sfcInfos, salmGroupMap.get("治具装配"), sfcRygstjbList);
        //
        // // 批量操作数据库
        // if (!sfcRygstjbList.isEmpty()) {
        //     Set<Date> dateSet = sfcRygstjbList.stream()
        //         .map(SfcRygstjb::getSysDt)
        //         .collect(Collectors.toSet());
        //     baseMapper.delete(new LambdaUpdateWrapper<SfcRygstjb>().in(SfcRygstjb::getSysDt, dateSet));
        //     baseMapper.insertBatch(sfcRygstjbList);
        // }

        List<SfcRygstjb> sfcRygstjbList = new ArrayList<>();
        // // salm人员表
        List<SfcSalm> salmList = sfcSalmMapper
            .selectList(new LambdaQueryWrapper<SfcSalm>().isNull(SfcSalm::getDutOtD).isNotNull(SfcSalm::getSGroup));
        // s_group分组
        Map<String, List<SfcSalm>> salmGroupMap = salmList.stream().collect(Collectors.groupingBy(SfcSalm::getSGroup));
        List<SfcSalm> salmList1 = salmGroupMap.get("设备装配");
        List<SfcSalm> zjList = salmList1.stream().filter(en -> Objects.equals(en.getSType(), "直接")).toList();
        List<SfcSalm> jjList = salmList1.stream().filter(en -> Objects.equals(en.getSType(), "间接")).toList();
        List<SfcSalm> salmList2 = salmGroupMap.get("治具装配");
        List<SfcSalm> zjList2 = salmList2.stream().filter(en -> Objects.equals(en.getSType(), "直接")).toList();
        List<SfcSalm> jjList2 = salmList2.stream().filter(en -> Objects.equals(en.getSType(), "间接")).toList();

        LocalDate beginDate;
        LocalDate endDate;
        // String[] split = rangeValue.toString().split(",");
        beginDate = LocalDate.parse(params.get("beginSysDt").toString(), DATE_TIME_FORMATTER);
        endDate = LocalDate.parse(params.get("endSysDt").toString(), DATE_TIME_FORMATTER);
        LambdaQueryWrapper<SfcInfo> queryWrapper = new LambdaQueryWrapper<>();
        // 统计设备装配和治具装配已关单数据
        queryWrapper.in(SfcInfo::getZpType, ZP_TYPE_LIST).between(SfcInfo::getBDtm, beginDate, endDate.plusDays(1))
            .isNotNull(SfcInfo::getEDtm);
        List<SfcInfo> sfcInfos = sfcInfoMapper.selectList(queryWrapper);

        // 筛选出设备装配
        List<SfcInfo> devSfcInfos = sfcInfos.stream().filter(en -> DEV_ZP_TYPE_LIST.contains(en.getZpType())).toList();
        // 按bDtm每天分组
        Map<LocalDate, List<SfcInfo>> sfcInfoDevMap =
            devSfcInfos.stream().collect(Collectors.groupingBy((sfcInfo -> sfcInfo.getBDtm().toLocalDate())));
        for (Map.Entry<LocalDate, List<SfcInfo>> entry : sfcInfoDevMap.entrySet()) {
            LocalDate key = entry.getKey();
            List<SfcInfo> value = entry.getValue();
            SfcRygstjb sfcRygstjb = new SfcRygstjb();
            sfcRygstjb.setCntType("设备");
            ZonedDateTime today = key.atStartOfDay(ZoneId.systemDefault());
            sfcRygstjb.setSysDt(Date.from(today.toInstant()));
            sfcRygstjb.setZjStandRs((long) zjList.size());
            sfcRygstjb.setJjStandRs((long) jjList.size());
            sfcRygstjb.setSumStandRs(sfcRygstjb.getZjStandRs() + sfcRygstjb.getJjStandRs());

            // 筛选出直接人员工号
            List<String> zjEmps = zjList.stream().map(SfcSalm::getSalNo).toList();
            // 获取直接人员出勤信息
            List<EmpGsDTO> zjEmpGsList = SqlserverJdbcUtils.getEmpGsList(zjEmps, today.format(DATE_TIME_FORMATTER2));
            // 筛选出间接人员工号
            List<String> jjEmps = jjList.stream().map(SfcSalm::getSalNo).toList();
            // 获取间接人员出勤信息
            List<EmpGsDTO> jjEmpGsList = SqlserverJdbcUtils.getEmpGsList(jjEmps, today.format(DATE_TIME_FORMATTER2));
            // 当天装配外援
            Set<String> wyEmps = value.stream().filter(en -> en.getDep() != null && !DEP_LIST.contains(en.getDep()))
                .map(SfcInfo::getEmpNo).collect(Collectors.toSet());
            List<EmpGsDTO> wyEmpGsList = Collections.emptyList();
            if (!wyEmps.isEmpty()) {
                // 获取外援人员出勤信息
                wyEmpGsList =
                    SqlserverJdbcUtils.getEmpGsList(new ArrayList<>(wyEmps), today.format(DATE_TIME_FORMATTER2));
            }

            sfcRygstjb.setZjSjcqRs((long) zjEmpGsList.size());
            sfcRygstjb.setJjSjcqRs((long) jjEmpGsList.size());
            sfcRygstjb.setWyRs((long) wyEmps.size());
            sfcRygstjb.setSumSjcqRs(sfcRygstjb.getZjSjcqRs() + sfcRygstjb.getJjSjcqRs() + sfcRygstjb.getWyRs());

            double zjSum = zjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            sfcRygstjb.setZjSjcqGs((float) zjSum);
            double jjSum = jjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            double wySum = wyEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            sfcRygstjb.setSumSjcqGs((float) (zjSum + jjSum + wySum));

            // 获取直接扫描人员
            List<SfcInfo> zjInfoList = value.stream().filter(en -> zjEmps.contains(en.getEmpNo())).toList();
            sfcRygstjb.setZjSmRs(zjInfoList.stream().map(SfcInfo::getEmpNo).distinct().count());
            sfcRygstjb.setZjSmgs((float) zjInfoList.stream().mapToDouble(SfcInfo::getSjGs).sum());
            sfcRygstjb.setZjZb(SafeMathUtils.safeDivide(sfcRygstjb.getZjSmgs(), sfcRygstjb.getZjSjcqGs()));
            sfcRygstjb.setSsGs(sfcRygstjb.getZjSjcqGs() - sfcRygstjb.getZjSmgs());
            sfcRygstjb.setWsmRs(sfcRygstjb.getZjSjcqRs() - sfcRygstjb.getZjSmRs());
            sfcRygstjbList.add(sfcRygstjb);
        }

        // 筛选出治具装配
        List<SfcInfo> jgSfcInfos = sfcInfos.stream().filter(en -> JG_ZP_TYPE_LIST.contains(en.getZpType())).toList();
        // 按bDtm每天分组
        Map<LocalDate, List<SfcInfo>> sfcInfoJgMap =
            jgSfcInfos.stream().collect(Collectors.groupingBy((sfcInfo -> sfcInfo.getBDtm().toLocalDate())));
        for (Map.Entry<LocalDate, List<SfcInfo>> entry : sfcInfoJgMap.entrySet()) {
            LocalDate key = entry.getKey();
            List<SfcInfo> value = entry.getValue();
            SfcRygstjb sfcRygstjb = new SfcRygstjb();
            sfcRygstjb.setCntType("治具");
            ZonedDateTime today = key.atStartOfDay(ZoneId.systemDefault());
            sfcRygstjb.setSysDt(Date.from(today.toInstant()));
            sfcRygstjb.setZjStandRs((long) zjList2.size());
            sfcRygstjb.setJjStandRs((long) jjList2.size());
            sfcRygstjb.setSumStandRs(sfcRygstjb.getZjStandRs() + sfcRygstjb.getJjStandRs());

            // 筛选出直接人员工号
            List<String> zjEmps = zjList2.stream().map(SfcSalm::getSalNo).toList();
            // 获取直接人员出勤信息
            List<EmpGsDTO> zjEmpGsList = SqlserverJdbcUtils.getEmpGsList(zjEmps, today.format(DATE_TIME_FORMATTER2));
            // 筛选出间接人员工号
            List<String> jjEmps = jjList2.stream().map(SfcSalm::getSalNo).toList();
            // 获取间接人员出勤信息
            List<EmpGsDTO> jjEmpGsList = SqlserverJdbcUtils.getEmpGsList(jjEmps, today.format(DATE_TIME_FORMATTER2));
            // 当天装配外援
            Set<String> wyEmps = value.stream().filter(en -> en.getDep() != null && !DEP_LIST.contains(en.getDep()))
                .map(SfcInfo::getEmpNo).collect(Collectors.toSet());
            List<EmpGsDTO> wyEmpGsList = Collections.emptyList();
            if (!wyEmps.isEmpty()) {
                // 获取外援人员出勤信息
                wyEmpGsList =
                    SqlserverJdbcUtils.getEmpGsList(new ArrayList<>(wyEmps), today.format(DATE_TIME_FORMATTER2));
            }

            sfcRygstjb.setZjSjcqRs((long) zjEmpGsList.size());
            sfcRygstjb.setJjSjcqRs((long) jjEmpGsList.size());
            sfcRygstjb.setWyRs((long) wyEmps.size());
            sfcRygstjb.setSumSjcqRs(sfcRygstjb.getZjSjcqRs() + sfcRygstjb.getJjSjcqRs() + sfcRygstjb.getWyRs());

            double zjSum = zjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            sfcRygstjb.setZjSjcqGs((float) zjSum);
            double jjSum = jjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            double wySum = wyEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            sfcRygstjb.setSumSjcqGs((float) (zjSum + jjSum + wySum));

            // 获取直接扫描人员
            List<SfcInfo> zjInfoList = value.stream().filter(en -> zjEmps.contains(en.getEmpNo())).toList();
            sfcRygstjb.setZjSmRs(zjInfoList.stream().map(SfcInfo::getEmpNo).distinct().count());
            sfcRygstjb.setZjSmgs((float) zjInfoList.stream().mapToDouble(SfcInfo::getSjGs).sum());
            sfcRygstjb.setZjZb(SafeMathUtils.safeDivide(sfcRygstjb.getZjSmgs(), sfcRygstjb.getZjSjcqGs()));
            sfcRygstjb.setSsGs(sfcRygstjb.getZjSjcqGs() - sfcRygstjb.getZjSmgs());
            sfcRygstjb.setWsmRs(sfcRygstjb.getZjSjcqRs() - sfcRygstjb.getZjSmRs());
            sfcRygstjbList.add(sfcRygstjb);
        }
        if (!ObjectUtils.isEmpty(sfcRygstjbList)) {
            Set<Date> dateSet = sfcRygstjbList.stream().map(SfcRygstjb::getSysDt).collect(Collectors.toSet());
            LambdaUpdateWrapper<SfcRygstjb> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(SfcRygstjb::getSysDt, dateSet);
            baseMapper.delete(wrapper);
            saveBatch(sfcRygstjbList);
        }

    }

    private LambdaQueryWrapper<SfcInfo> buildSfcInfoQueryWrapper(Map<String, Object> params) {
        LambdaQueryWrapper<SfcInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SfcInfo::getZpType, ZP_TYPE_LIST);
        if (params.get("beginSysDt") != null && params.get("endSysDt") != null) {
            queryWrapper.between(SfcInfo::getEDtm, params.get("beginSysDt"), params.get("endSysDt"));
        }
        return queryWrapper;
    }

    // 设备统计逻辑
    private void processEquipmentStatistics(
        List<SfcInfo> sfcInfos,
        List<SfcSalm> salmList,
        List<SfcRygstjb> sfcRygstjbList) throws SQLException {
        if (salmList == null || salmList.isEmpty()) {
            return;
        }

        // 分组和分页查询员工工时
        processAndAddStatistics(sfcInfos, salmList, "设备", sfcRygstjbList);
    }

    // 治具统计逻辑
    private void processFixtureStatistics(
        List<SfcInfo> sfcInfos,
        List<SfcSalm> salmList,
        List<SfcRygstjb> sfcRygstjbList) throws SQLException {
        if (salmList == null || salmList.isEmpty()) {
            return;
        }

        // 分组和分页查询员工工时
        processAndAddStatistics(sfcInfos, salmList, "治具", sfcRygstjbList);
    }

    /**
     * 统计公共逻辑
     *
     * @param sfcInfos
     * @param salmList
     * @param cntType
     * @param sfcRygstjbList
     * @throws SQLException
     */
    private void processAndAddStatistics(
        List<SfcInfo> sfcInfos,
        List<SfcSalm> salmList,
        String cntType,
        List<SfcRygstjb> sfcRygstjbList) throws SQLException {

        // 分离直接和间接人员
        List<SfcSalm> zjList = salmList.stream().filter(en -> "直接".equals(en.getSType())).toList();
        List<SfcSalm> jjList = salmList.stream().filter(en -> "间接".equals(en.getSType())).toList();

        // 过滤 SfcInfo
        List<SfcInfo> filteredInfos = sfcInfos.stream()
            .filter(en -> cntType.equals("设备") ? DEV_ZP_TYPE_LIST.contains(en.getZpType()) : JG_ZP_TYPE_LIST.contains(en.getZpType()))
            .toList();

        // 按日期分组
        Map<LocalDate, List<SfcInfo>> sfcInfoMap = filteredInfos.stream()
            .collect(Collectors.groupingBy(sfcInfo -> sfcInfo.getBDtm().toLocalDate()));

        // 批量查询员工工时
        Map<LocalDate, Map<String, Object>> empGsMap = queryEmpGsByDateRange(
            zjList.stream().map(SfcSalm::getSalNo).toList(),
            jjList.stream().map(SfcSalm::getSalNo).toList(),
            sfcInfoMap.keySet());

        // 处理统计数据
        for (Map.Entry<LocalDate, List<SfcInfo>> entry : sfcInfoMap.entrySet()) {
            LocalDate key = entry.getKey();
            List<SfcInfo> value = entry.getValue();
            SfcRygstjb sfcRygstjb = new SfcRygstjb();
            sfcRygstjb.setCntType(cntType);
            sfcRygstjb.setSysDt(Date.from(key.atStartOfDay(ZoneId.systemDefault()).toInstant()));

            // 标准人数
            sfcRygstjb.setZjStandRs((long) zjList.size());
            sfcRygstjb.setJjStandRs((long) jjList.size());
            sfcRygstjb.setSumStandRs(sfcRygstjb.getZjStandRs() + sfcRygstjb.getJjStandRs());

            // 查询工时数据
            Map<String, Object> dailyData = empGsMap.get(key);
            List<EmpGsDTO> zjEmpGsList = (List<EmpGsDTO>) dailyData.get("zj");
            List<EmpGsDTO> jjEmpGsList = (List<EmpGsDTO>) dailyData.get("jj");
            Set<String> wyEmps = value.stream()
                .filter(en -> en.getDep() != null && !DEP_LIST.contains(en.getDep()))
                .map(SfcInfo::getEmpNo)
                .collect(Collectors.toSet());

            // 统计工时和人员
            sfcRygstjb.setZjSjcqRs((long) zjEmpGsList.size());
            sfcRygstjb.setJjSjcqRs((long) jjEmpGsList.size());
            sfcRygstjb.setWyRs((long) wyEmps.size());
            sfcRygstjb.setSumSjcqRs(sfcRygstjb.getZjSjcqRs() + sfcRygstjb.getJjSjcqRs() + sfcRygstjb.getWyRs());

            double zjSum = zjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            sfcRygstjb.setZjSjcqGs((float) zjSum);
            double jjSum = jjEmpGsList.stream().mapToDouble(EmpGsDTO::getGs).sum();
            double wySum;
            if (!wyEmps.isEmpty()) {
                SqlserverJdbcUtils.switchDataSource("sqlserver3");
                wySum = SqlserverJdbcUtils.getEmpGsList(new ArrayList<>(wyEmps), key.format(DATE_TIME_FORMATTER2))
                    .stream().mapToDouble(EmpGsDTO::getGs).sum();
            } else {
                wySum = 0;
            }
            sfcRygstjb.setSumSjcqGs((float) (zjSum + jjSum + wySum));

            // 进一步统计和计算
            List<SfcInfo> zjInfoList = value.stream().filter(en -> zjList.stream().anyMatch(zj -> zj.getSalNo().equals(en.getEmpNo()))).toList();
            sfcRygstjb.setZjSmRs((long) zjInfoList.stream().map(SfcInfo::getEmpNo).distinct().count());
            sfcRygstjb.setZjSmgs((float) zjInfoList.stream().mapToDouble(SfcInfo::getSjGs).sum());
            sfcRygstjb.setZjZb(zjInfoList.isEmpty() ? 0 : (sfcRygstjb.getZjSmgs() / sfcRygstjb.getZjSjcqGs()));
            sfcRygstjb.setSsGs(sfcRygstjb.getZjSjcqGs() - sfcRygstjb.getZjSmgs());
            sfcRygstjb.setWsmRs(sfcRygstjb.getZjSjcqRs() - sfcRygstjb.getZjSmRs());

            sfcRygstjbList.add(sfcRygstjb);
        }
    }

    /**
     * 批量查询员工工时
     *
     * @param zjEmps
     * @param jjEmps
     * @param dates
     * @return
     * @throws SQLException
     */
    private Map<LocalDate, Map<String, Object>> queryEmpGsByDateRange(
        List<String> zjEmps,
        List<String> jjEmps,
        Set<LocalDate> dates) throws SQLException {

        Map<LocalDate, Map<String, Object>> empGsMap = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 批量查询直接人员工时
        List<String> formattedZjDates = dates.stream()
            .map(date -> date.format(formatter))
            .collect(Collectors.toList());
        SqlserverJdbcUtils.switchDataSource("sqlserver3");
        //        Map<String, EmpGsDTO> zjEmpGsBatch = SqlserverJdbcHrUtils.getEmpGsBatch(zjEmps, formattedZjDates);
        Map<String, EmpGsDTO> zjEmpGsBatch = SqlserverJdbcUtils.getEmpGsBatch(zjEmps, formattedZjDates);
        // 批量查询间接人员工时
        Map<String, EmpGsDTO> jjEmpGsBatch = SqlserverJdbcUtils.getEmpGsBatch(jjEmps, formattedZjDates);

        // 按日期分组
        dates.forEach(date -> {
            String dateStr = date.format(formatter);
            Map<String, Object> dailyData = new HashMap<>();
            dailyData.put("zj", zjEmpGsBatch.get(dateStr));
            dailyData.put("jj", jjEmpGsBatch.get(dateStr));
            empGsMap.put(date, dailyData);
        });

        return empGsMap;
    }

}
