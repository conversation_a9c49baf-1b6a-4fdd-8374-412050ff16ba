package org.dromara.sfc.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcIpqcEmp;
import org.dromara.sfc.domain.bo.SfcIpqcEmpBo;
import org.dromara.sfc.domain.vo.SfcIpqcEmpVo;

import java.util.Collection;
import java.util.List;

/**
 * IPQC人员设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
public interface ISfcIpqcEmpService {

    /**
     * 查询IPQC人员设置
     *
     * @param id 主键
     * @return IPQC人员设置
     */
    SfcIpqcEmpVo queryById(Long id);

    /**
     * 分页查询IPQC人员设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IPQC人员设置分页列表
     */
    TableDataInfo<SfcIpqcEmpVo> queryPageList(SfcIpqcEmpBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的IPQC人员设置列表
     *
     * @param bo 查询条件
     * @return IPQC人员设置列表
     */
    List<SfcIpqcEmpVo> queryList(SfcIpqcEmpBo bo);

    /**
     * 新增IPQC人员设置
     *
     * @param bo IPQC人员设置
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcIpqcEmpBo bo);

    /**
     * 修改IPQC人员设置
     *
     * @param bo IPQC人员设置
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcIpqcEmpBo bo);

    /**
     * 校验并批量删除IPQC人员设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R getIpqcEmpInfo(SfcIpqcEmp ipqcEmpQuery);
}
