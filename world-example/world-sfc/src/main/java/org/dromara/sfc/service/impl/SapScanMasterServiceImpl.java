package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.bo.SapScanMasterBo;
import org.dromara.sfc.domain.vo.SapScanMasterVo;
import org.dromara.sfc.mapper.SapScanMasterMapper;
import org.dromara.sfc.service.ISapScanMasterService;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.sql.SQLException;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.dromara.sfc.utils.RowMapperUtil.getSapScanMasterRowMapper;

/**
 * sapMaster信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@RequiredArgsConstructor
@Service
public class SapScanMasterServiceImpl implements ISapScanMasterService {

    private final SapScanMasterMapper baseMapper;


    /**
     * 查询sapMaster信息
     *
     * @param id 主键
     * @return sapMaster信息
     */
    @Override
    public SapScanMasterVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询sapMaster信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return sapMaster信息分页列表
     */
    @Override
    public TableDataInfo<SapScanMasterVo> queryPageList(SapScanMasterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SapScanMaster> lqw = buildQueryWrapper(bo);
        Page<SapScanMasterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的sapMaster信息列表
     *
     * @param bo 查询条件
     * @return sapMaster信息列表
     */
    @Override
    public List<SapScanMasterVo> queryList(SapScanMasterBo bo) {
        LambdaQueryWrapper<SapScanMaster> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SapScanMaster> buildQueryWrapper(SapScanMasterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SapScanMaster> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(SapScanMaster::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMoState()), SapScanMaster::getMoState, bo.getMoState());
        lqw.eq(StringUtils.isNotBlank(bo.getMoRem()), SapScanMaster::getMoRem, bo.getMoRem());
        lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), SapScanMaster::getCustNo, bo.getCustNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPmNo()), SapScanMaster::getPmNo, bo.getPmNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPoNo()), SapScanMaster::getPoNo, bo.getPoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDrNo()), SapScanMaster::getDrNo, bo.getDrNo());
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), SapScanMaster::getMoNo, bo.getMoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPartId()), SapScanMaster::getPartId, bo.getPartId());
        return lqw;
    }

    /**
     * 新增sapMaster信息
     *
     * @param bo sapMaster信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SapScanMasterBo bo) {
        SapScanMaster add = MapstructUtils.convert(bo, SapScanMaster.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改sapMaster信息
     *
     * @param bo sapMaster信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SapScanMasterBo bo) {
        SapScanMaster update = MapstructUtils.convert(bo, SapScanMaster.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SapScanMaster entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除sapMaster信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    public SapScanMaster getMasterByMo(String mo) throws SQLException {
        LambdaQueryWrapper<SapScanMaster> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SapScanMaster::getMoNo, mo);
        List<SapScanMaster> sapScanMasters = baseMapper.selectList(queryWrapper);
        if (ObjectUtils.isEmpty(sapScanMasters)) {
            String sql = "select * from master where mo_no = ?";
            sapScanMasters = SqlserverJdbcUtils.executeQuery(sql, getSapScanMasterRowMapper(), Collections.singletonList(mo));
        }
        return ObjectUtils.isEmpty(sapScanMasters) ? null : sapScanMasters.get(0);
    }




    // public static List<SapScanMaster> getSapScanMasterRowMapper(List<Map<String, Object>> mapList) {
    //     List<SapScanMaster> list = new ArrayList<>();
    //     mapList.forEach(rs -> {
    //         SapScanMaster sapScanMaster = new SapScanMaster();
    //         Object id = rs.get("ID");
    //         //转换成Long类型，如果id是null则返回0L
    //         sapScanMaster.setId(Long.parseLong(id.toString()));
    //         sapScanMaster.setMoState(rs.get("状态").toString());
    //         sapScanMaster.setMoRem(rs.get("备注").toString());
    //         sapScanMaster.setBargainDate(DateUtils.parseDate(rs.get("接单日期")));
    //         sapScanMaster.setCustNo(rs.get("客户代码").toString());
    //         sapScanMaster.setPmNo(rs.get("pm_no").toString());
    //         sapScanMaster.setPoNo(rs.get("客户PO号").toString());
    //         sapScanMaster.setDrNo(rs.get("dr_no").toString());
    //         sapScanMaster.setDrItm(rs.get("dr_itm").toString());
    //         sapScanMaster.setDeliveryDate(DateUtils.parseDate(rs.get("客户要求交期")));
    //         sapScanMaster.setOrderDescription(rs.get("类别描述").toString());
    //         sapScanMaster.setSoNo(rs.get("so_no").toString());
    //         sapScanMaster.setSoItm(rs.get("so_itm").toString());
    //         sapScanMaster.setMoNo(rs.get("mo_no").toString());
    //         sapScanMaster.setPartId(rs.get("品号").toString());
    //         sapScanMaster.setPartType(rs.get("零件分类").toString());
    //         sapScanMaster.setDrawNumber(rs.get("图号").toString());
    //         sapScanMaster.setPartVer(rs.get("版本").toString());
    //         sapScanMaster.setDesc(rs.get("描述").toString());
    //         sapScanMaster.setMerchandiserCharge(rs.get("跟单负责人").toString());
    //         sapScanMaster.setMerchandiser(rs.get("跟单人").toString());
    //         sapScanMaster.setProfitCenter(rs.get("利润中心").toString());
    //         sapScanMaster.setOrderQty(Long.parseLong(rs.get("订单数量").toString()));
    //         sapScanMaster.setProductionQty(Long.parseLong(rs.get("生产数量").toString()));
    //         sapScanMaster.setMfgDept(rs.get("制造部门").toString());
    //         sapScanMaster.setWorkshop(rs.get("生产车间").toString());
    //         sapScanMaster.setMergeMo(rs.get("合单标识").toString());
    //         sapScanMaster.setProofMo(rs.get("打样标识").toString());
    //         sapScanMaster.setOrderName(rs.get("下单员姓名").toString());
    //         sapScanMaster.setBomCharge(rs.get("bom责任人").toString());
    //         list.add(sapScanMaster);
    //     });
    //     return list;
    // }



}
