package org.dromara.sfc.service.impl;


import cn.hutool.core.convert.Convert;
import cn.hutool.log.Log;
import cn.hutool.log.LogFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.sfc.domain.SfcLdmzLink;
import org.dromara.sfc.mapper.SfcLdmzLinkMapper;
import org.dromara.sfc.service.ISfcLdmzLinkService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.util.List;

/*
 *
 *sfc_ldmz_link   流道模组绑定信息表
 *
 * */
@Service
public class SfcLdmzLinkServiceImpl extends ServiceImpl<SfcLdmzLinkMapper, SfcLdmzLink> implements ISfcLdmzLinkService {

    private static final Log logger = LogFactory.get();

    @Resource
    public SfcLdmzLinkMapper sfcLdmzLinkMapper;

    /**
     * 保存模组绑定信息
     *
     * @param sfcLdmzLinkList 绑定信息
     * @return 保存结果
     */
    @Override
    public R addList(List<SfcLdmzLink> sfcLdmzLinkList) {
        if (ObjectUtils.isEmpty(sfcLdmzLinkList)) {
            return R.fail("保存数据不能为空");
        }
        LocalDateTime dateTime = LocalDateTime.now();
        for (SfcLdmzLink sfcLdmzLink : sfcLdmzLinkList) {
            // 绑定关系已存在则不保存
            LambdaQueryWrapper<SfcLdmzLink> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SfcLdmzLink::getLdmzBarcode, sfcLdmzLink.getLdmzBarcode()).eq(SfcLdmzLink::getMoBarcode, sfcLdmzLink.getMoBarcode());
            if (sfcLdmzLinkMapper.selectCount(queryWrapper) > 0) {
                logger.info("{}:{}绑定关系已存在，不保存", sfcLdmzLink.getLdmzBarcode(), sfcLdmzLink.getMoBarcode());
                continue;
            }
            // 前台的ID与数据库不对应，需重新生成
            sfcLdmzLink.setId(null);
            sfcLdmzLink.setSysdt(Convert.toStr(dateTime));
            sfcLdmzLink.setHost(LoginHelper.getUsername());
            sfcLdmzLinkMapper.insert(sfcLdmzLink);
        }
        return R.ok();
    }
}
