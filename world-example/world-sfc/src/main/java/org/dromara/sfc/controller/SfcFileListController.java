package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcFileListBo;
import org.dromara.sfc.domain.vo.SfcFileListVo;
import org.dromara.sfc.service.ISfcFileListService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 文件清单设置
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/fileList")
public class SfcFileListController extends BaseController {

    private final ISfcFileListService sfcFileListService;

    /**
     * 查询文件清单设置列表
     */
    @SaCheckPermission("information:fileList:list")
    @GetMapping("/list")
    public TableDataInfo<SfcFileListVo> list(SfcFileListBo bo, PageQuery pageQuery) {
        return sfcFileListService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出文件清单设置列表
     */
    @SaCheckPermission("information:fileList:export")
    @Log(title = "文件清单设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcFileListBo bo, HttpServletResponse response) {
        List<SfcFileListVo> list = sfcFileListService.queryList(bo);
        ExcelUtil.exportExcel(list, "文件清单设置", SfcFileListVo.class, response);
    }

    /**
     * 获取文件清单设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:fileList:query")
    @GetMapping("/{id}")
    public R<SfcFileListVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(sfcFileListService.queryById(id));
    }

    /**
     * 新增文件清单设置
     */
    @SaCheckPermission("information:fileList:add")
    @Log(title = "文件清单设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcFileListBo bo) {
        return toAjax(sfcFileListService.insertByBo(bo));
    }

    /**
     * 修改文件清单设置
     */
    @SaCheckPermission("information:fileList:edit")
    @Log(title = "文件清单设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcFileListBo bo) {
        return toAjax(sfcFileListService.updateByBo(bo));
    }

    /**
     * 删除文件清单设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:fileList:remove")
    @Log(title = "文件清单设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcFileListService.deleteWithValidByIds(List.of(ids), true));
    }
}
