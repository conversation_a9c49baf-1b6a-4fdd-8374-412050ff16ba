package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcSalmBo;
import org.dromara.sfc.domain.vo.SfcSalmVo;
import org.dromara.sfc.service.ISfcSalmService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 人员设定记录
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/sfcSalm")
public class SfcSalmController extends BaseController {

    private final ISfcSalmService sfcSalmService;

    /**
     * 查询人员设定记录列表
     */
    @SaCheckPermission("information:sfcSalm:list")
    @GetMapping("/list")
    public TableDataInfo<SfcSalmVo> list(SfcSalmBo bo, PageQuery pageQuery) {
        return sfcSalmService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出人员设定记录列表
     */
    @SaCheckPermission("information:sfcSalm:export")
    @Log(title = "人员设定记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcSalmBo bo, HttpServletResponse response) {
        List<SfcSalmVo> list = sfcSalmService.queryList(bo);
        ExcelUtil.exportExcel(list, "人员设定记录", SfcSalmVo.class, response);
    }

    /**
     * 获取人员设定记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:sfcSalm:query")
    @GetMapping("/{id}")
    public R<SfcSalmVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(sfcSalmService.queryById(id));
    }

    /**
     * 新增人员设定记录
     */
    @SaCheckPermission("information:sfcSalm:add")
    @Log(title = "人员设定记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcSalmBo bo) {
        return toAjax(sfcSalmService.insertByBo(bo));
    }

    /**
     * 修改人员设定记录
     */
    @SaCheckPermission("information:sfcSalm:edit")
    @Log(title = "人员设定记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcSalmBo bo) {
        return toAjax(sfcSalmService.updateByBo(bo));
    }

    /**
     * 删除人员设定记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:sfcSalm:remove")
    @Log(title = "人员设定记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcSalmService.deleteWithValidByIds(List.of(ids), true));
    }
}
