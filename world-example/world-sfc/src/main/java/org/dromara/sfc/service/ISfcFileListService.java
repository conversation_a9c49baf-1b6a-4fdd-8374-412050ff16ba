package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.SfcFileListBo;
import org.dromara.sfc.domain.vo.SfcFileListVo;

import java.util.Collection;
import java.util.List;

/**
 * 文件清单设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
public interface ISfcFileListService {

    /**
     * 查询文件清单设置
     *
     * @param id 主键
     * @return 文件清单设置
     */
    SfcFileListVo queryById(Long id);

    /**
     * 分页查询文件清单设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文件清单设置分页列表
     */
    TableDataInfo<SfcFileListVo> queryPageList(SfcFileListBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的文件清单设置列表
     *
     * @param bo 查询条件
     * @return 文件清单设置列表
     */
    List<SfcFileListVo> queryList(SfcFileListBo bo);

    /**
     * 新增文件清单设置
     *
     * @param bo 文件清单设置
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcFileListBo bo);

    /**
     * 修改文件清单设置
     *
     * @param bo 文件清单设置
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcFileListBo bo);

    /**
     * 校验并批量删除文件清单设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
