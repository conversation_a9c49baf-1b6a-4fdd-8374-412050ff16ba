package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SapScanSalm;
import org.dromara.sfc.domain.SfcIpqcEmp;
import org.dromara.sfc.domain.bo.SfcIpqcEmpBo;
import org.dromara.sfc.domain.dto.IpqcEmpDTO;
import org.dromara.sfc.domain.vo.SfcIpqcEmpVo;
import org.dromara.sfc.mapper.SapScanSalmMapper;
import org.dromara.sfc.mapper.SfcIpqcEmpMapper;
import org.dromara.sfc.service.ISfcIpqcEmpService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * IPQC人员设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@RequiredArgsConstructor
@Service
public class SfcIpqcEmpServiceImpl implements ISfcIpqcEmpService {

    @Resource
    private final SfcIpqcEmpMapper baseMapper;

    @Resource
    private final SapScanSalmMapper sapScanSalmMapper;

    /**
     * 查询IPQC人员设置
     *
     * @param id 主键
     * @return IPQC人员设置
     */
    @Override
    public SfcIpqcEmpVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询IPQC人员设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return IPQC人员设置分页列表
     */
    @Override
    public TableDataInfo<SfcIpqcEmpVo> queryPageList(SfcIpqcEmpBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcIpqcEmp> lqw = buildQueryWrapper(bo);
        Page<SfcIpqcEmpVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的IPQC人员设置列表
     *
     * @param bo 查询条件
     * @return IPQC人员设置列表
     */
    @Override
    public List<SfcIpqcEmpVo> queryList(SfcIpqcEmpBo bo) {
        LambdaQueryWrapper<SfcIpqcEmp> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcIpqcEmp> buildQueryWrapper(SfcIpqcEmpBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcIpqcEmp> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getEmpNo()), SfcIpqcEmp::getEmpNo, bo.getEmpNo());
        lqw.like(StringUtils.isNotBlank(bo.getEmpName()), SfcIpqcEmp::getEmpName, bo.getEmpName());
        lqw.like(StringUtils.isNotBlank(bo.getDept()), SfcIpqcEmp::getDept, bo.getDept());
        lqw.eq(StringUtils.isNotBlank(bo.getDep()), SfcIpqcEmp::getDep, bo.getDep());
        lqw.eq(StringUtils.isNotBlank(bo.getGroupLeader()), SfcIpqcEmp::getGroupLeader, bo.getGroupLeader());
        return lqw;
    }

    /**
     * 新增IPQC人员设置
     *
     * @param bo IPQC人员设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcIpqcEmpBo bo) {
        SfcIpqcEmp add = MapstructUtils.convert(bo, SfcIpqcEmp.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改IPQC人员设置
     *
     * @param bo IPQC人员设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcIpqcEmpBo bo) {
        SfcIpqcEmp update = MapstructUtils.convert(bo, SfcIpqcEmp.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcIpqcEmp entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除IPQC人员设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public R getIpqcEmpInfo(SfcIpqcEmp ipqcEmpQuery) {
        // 构建查询条件
        LambdaQueryWrapper<SfcIpqcEmp> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SfcIpqcEmp::getEmpNo, ipqcEmpQuery.getEmpNo());

        // 查询IPQC员工信息
        SfcIpqcEmp ipqcEmp = baseMapper.selectOne(wrapper);
        if (ipqcEmp == null) {
            return R.fail("未找到相关员工信息");
        }
        // 构建返回DTO
        IpqcEmpDTO ipqcEmpDTO = new IpqcEmpDTO();
        ipqcEmpDTO.setEmpNo(ipqcEmp.getEmpNo());
        ipqcEmpDTO.setEmpName(ipqcEmp.getEmpName());
        ipqcEmpDTO.setDep(ipqcEmp.getDep());
        ipqcEmpDTO.setDept(ipqcEmp.getDept());
        ipqcEmpDTO.setGroupLeader(ipqcEmp.getGroupLeader());
        // 查询SAP员工信息
        LambdaQueryWrapper<SapScanSalm> salmWrapper = Wrappers.lambdaQuery();
        salmWrapper.eq(SapScanSalm::getSalNo, ipqcEmp.getEmpNo());
        SapScanSalm sapScanSalm = sapScanSalmMapper.selectOne(salmWrapper);
        if (sapScanSalm != null) {
            ipqcEmpDTO.setArbpl(sapScanSalm.getArbpl());
        }

        return R.ok(ipqcEmpDTO);
    }
}
