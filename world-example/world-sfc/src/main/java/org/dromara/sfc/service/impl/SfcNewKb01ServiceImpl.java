package org.dromara.sfc.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.sfc.domain.SfcKbWkXl;
import org.dromara.sfc.domain.SfcNewKb01;
import org.dromara.sfc.domain.bo.SfcNewKb01Bo;
import org.dromara.sfc.domain.dto.WkXlDataDTO;
import org.dromara.sfc.domain.vo.SfcKbWkXlVo;
import org.dromara.sfc.domain.vo.SfcNewKb01Vo;
import org.dromara.sfc.mapper.SfcKbWkXlMapper;
import org.dromara.sfc.mapper.SfcNewKb01Mapper;
import org.dromara.sfc.service.ISfcNewKb01Service;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 装配看板记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SfcNewKb01ServiceImpl implements ISfcNewKb01Service {

    private static final String COUNT_CACHE_KEY = "kanban:count";
    private static final String DATE_PATTERN = "yyyy-MM-dd";
    private static final String TIME_SUFFIX = " 00:00";

    private final SfcNewKb01Mapper baseMapper;
    private final SfcKbWkXlMapper sfcKbWkXlMapper;

    /**
     * 查询装配看板记录
     *
     * @param id 主键
     * @return 装配看板记录
     */
    @Override
    public SfcNewKb01Vo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询装配看板记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配看板记录分页列表
     */
    @Override
    public TableDataInfo<SfcNewKb01Vo> queryPageList(SfcNewKb01Bo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcNewKb01> lqw = buildQueryWrapper(bo);
        Page<SfcNewKb01Vo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的装配看板记录列表
     *
     * @param bo 查询条件
     * @return 装配看板记录列表
     */
    @Override
    public List<SfcNewKb01Vo> queryList(SfcNewKb01Bo bo) {
        LambdaQueryWrapper<SfcNewKb01> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcNewKb01> buildQueryWrapper(SfcNewKb01Bo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcNewKb01> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SfcNewKb01::getId);
        lqw.like(StringUtils.isNotBlank(bo.getMoNo()), SfcNewKb01::getMoNo, bo.getMoNo());
        lqw.like(StringUtils.isNotBlank(bo.getSbName()), SfcNewKb01::getSbName, bo.getSbName());
        lqw.like(StringUtils.isNotBlank(bo.getMUsr()), SfcNewKb01::getMUsr, bo.getMUsr());
        lqw.like(StringUtils.isNotBlank(bo.getMUsr2()), SfcNewKb01::getMUsr2, bo.getMUsr2());
        return lqw;
    }

    /**
     * 新增装配看板记录
     *
     * @param bo 装配看板记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcNewKb01Bo bo) {
        SfcNewKb01 add = MapstructUtils.convert(bo, SfcNewKb01.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装配看板记录
     *
     * @param bo 装配看板记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcNewKb01Bo bo) {
        SfcNewKb01 update = MapstructUtils.convert(bo, SfcNewKb01.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcNewKb01 entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除装配看板记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public List<SfcNewKb01Vo> fetchSfcNewKb01FromSysdtTodayOrderedById() {
        LambdaQueryWrapper<SfcNewKb01> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .ge(SfcNewKb01::getSysdt, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00")
            .orderByAsc(SfcNewKb01::getId);
        // 执行查询并返回结果
        List<SfcNewKb01Vo> sfcNewKb01Vos = baseMapper.selectVoList(queryWrapper);
        return sfcNewKb01Vos;
    }

    @Override
    public List<SfcNewKb01> getSfcNewKbList1(PageQuery pageQuery) {
        try {
            Page<SfcNewKb01> pageParam = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());
            LambdaQueryWrapper<SfcNewKb01> queryWrapper = buildTodayQueryWrapper();
            return baseMapper.selectList(pageParam, queryWrapper);
        } catch (Exception e) {
            log.error("查询看板列表失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Long getSfcNewKbCount() {
        try {
            // 尝试从Redis获取计数
            Long cachedCount = RedisUtils.getCacheObject(COUNT_CACHE_KEY);
            if (cachedCount != null) {
                return cachedCount;
            }
            // 缓存未命中，查询数据库
            Long count = baseMapper.selectCount(buildTodayQueryWrapper());
            // 将结果存入Redis缓存（设置1分钟过期）
            RedisUtils.setCacheObject(COUNT_CACHE_KEY, count, Duration.ofMillis(60));
            return count;
        } catch (Exception e) {
            log.error("获取看板计数失败", e);
            return 0L;
        }
    }

    private LambdaQueryWrapper<SfcNewKb01> buildTodayQueryWrapper() {
        return Wrappers.lambdaQuery(SfcNewKb01.class)
            .ge(SfcNewKb01::getSysdt, getTodayStart())
            .orderByAsc(SfcNewKb01::getId);
    }

    private String getTodayStart() {
        return LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_PATTERN)) + TIME_SUFFIX;
    }

    @Override
    public List<WkXlDataDTO> getWkXlData() {
        String cacheKey = "kanban:wkxl:data";
        try {
            // 尝试从Redis获取数据
            List<WkXlDataDTO> cachedData = RedisUtils.getCacheObject(cacheKey);
            if (cachedData != null) {
                return cachedData;
            }
            List<WkXlDataDTO> result = getDateModels();
            // 缓存结果（1分钟过期）
            RedisUtils.setCacheObject(cacheKey, result, Duration.ofMillis(60));
            return result;
        } catch (Exception e) {
            log.error("获取工站效率数据失败", e);
            return Collections.emptyList();
        }
    }

    @NotNull
    private synchronized List<WkXlDataDTO> getDateModels() {
        // 当前时间
        Date date = DateUtil.date();
        LocalDateTime of = LocalDateTimeUtil.of(date);
        LocalDateTime offset = LocalDateTimeUtil.offset(of, -5, ChronoUnit.DAYS);
        LambdaQueryWrapper<SfcKbWkXl> lqw = Wrappers.lambdaQuery();
        lqw.ge(SfcKbWkXl::getSysDt, offset).lt(SfcKbWkXl::getWkXl, 2).ge(SfcKbWkXl::getSmGs, 0.1);
        lqw.lt(SfcKbWkXl::getSmGs, 20).ge(SfcKbWkXl::getBasGs, 0.1).lt(SfcKbWkXl::getBasGs, 20);
        lqw.orderByDesc(SfcKbWkXl::getSysDt).last("LIMIT 20");
        List<SfcKbWkXlVo> results = sfcKbWkXlMapper.selectVoList(lqw);

        List<WkXlDataDTO> dateModelList = results.stream().map(this::convertToWkXlDataVo).collect(Collectors.toList());

        return dateModelList;
    }

    private WkXlDataDTO convertToWkXlDataVo(SfcKbWkXlVo sfcKbWkXl) {
        WkXlDataDTO vo = new WkXlDataDTO();
        vo.setDate(sfcKbWkXl.getSysDt());
        vo.setLine(sfcKbWkXl.getFloorNo());
        vo.setStationNum(sfcKbWkXl.getWkNo());
        vo.setStationName(sfcKbWkXl.getWkName());
        vo.setMoNo(sfcKbWkXl.getMoNo());
        vo.setBarCode(sfcKbWkXl.getBarCode());
        double basGs = sfcKbWkXl.getBasGs();
        BigDecimal bd = new BigDecimal(basGs);

        if (bd != null) {
            BigDecimal bigDecimal = bd.setScale(0, RoundingMode.HALF_UP);
            vo.setStandardWorkHours(bigDecimal.floatValue());
        }
        double smGs = sfcKbWkXl.getSmGs();
        BigDecimal smBd = new BigDecimal(smGs);
        if (smBd != null) {
            BigDecimal bigDecimal = smBd.setScale(0, RoundingMode.HALF_UP);
            vo.setScanningManHours(bigDecimal.floatValue());
        }
        double wkGs = sfcKbWkXl.getWkXl();
        BigDecimal wkBd = new BigDecimal(wkGs);
        if (wkBd != null) {
            BigDecimal bigDecimal = NumberUtil.mul(wkBd, 100).setScale(2, RoundingMode.HALF_UP);
            vo.setPersonnelEfficiency(bigDecimal.floatValue());
        }
        return vo;
    }
}
