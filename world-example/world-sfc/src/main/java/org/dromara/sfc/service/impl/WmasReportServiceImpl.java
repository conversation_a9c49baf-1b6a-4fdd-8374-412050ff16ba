package org.dromara.sfc.service.impl;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.PLMFileTool;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.sap.code.SapFunction;
import org.dromara.common.sap.util.SapUtils;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.WmasReport;
import org.dromara.sfc.domain.bo.WmasReportBo;
import org.dromara.sfc.domain.vo.WmasReportVo;
import org.dromara.sfc.mapper.SapScanMasterMapper;
import org.dromara.sfc.mapper.WmasReportMapper;
import org.dromara.sfc.service.IWmasReportService;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sap.conn.jco.JCoException;

import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 样品清单维护Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class WmasReportServiceImpl implements IWmasReportService {
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final WmasReportMapper baseMapper;

    @Resource
    private SapScanMasterMapper sapScanMasterMapper;

    /**
     * 查询样品清单维护
     *
     * @param id 主键
     * @return 样品清单维护
     */
    @Override
    public WmasReportVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询样品清单维护列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 样品清单维护分页列表
     */
    @Override
    public TableDataInfo<WmasReportVo> queryPageList(WmasReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<WmasReport> lqw = buildQueryWrapper(bo);
        Page<WmasReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的样品清单维护列表
     *
     * @param bo 查询条件
     * @return 样品清单维护列表
     */
    @Override
    public List<WmasReportVo> queryList(WmasReportBo bo) {
        LambdaQueryWrapper<WmasReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WmasReport> buildQueryWrapper(WmasReportBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WmasReport> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WmasReport::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMoState()), WmasReport::getMoState, bo.getMoState());
        lqw.eq(StringUtils.isNotBlank(bo.getDrawNumber()), WmasReport::getDrawNumber, bo.getDrawNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderType()), WmasReport::getOrderType, bo.getOrderType());
        lqw.eq(StringUtils.isNotBlank(bo.getOrderSalm()), WmasReport::getOrderSalm, bo.getOrderSalm());
        return lqw;
    }

    /**
     * 新增样品清单维护
     *
     * @param bo 样品清单维护
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WmasReportBo bo) {
        WmasReport add = MapstructUtils.convert(bo, WmasReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改样品清单维护
     *
     * @param bo 样品清单维护
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WmasReportBo bo) {
        WmasReport update = MapstructUtils.convert(bo, WmasReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WmasReport entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除样品清单维护信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public R search(Map<String, Object> params, PageQuery pageQuery) {
        LambdaQueryWrapper<SapScanMaster> queryWrapper = getSearchReportWrapper(params);
        List<SapScanMaster> sapScanMasterList = sapScanMasterMapper.selectList(queryWrapper);
        return R.ok(sapScanMasterList);
    }

    @Override
    public R saveWmasReports(List<WmasReport> wmasReportList) {
        if (ObjectUtils.isEmpty(wmasReportList)) {
            return R.fail("data is empty");
        }
        for (WmasReport wmasReport : wmasReportList) {
            LambdaQueryWrapper<WmasReport> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WmasReport::getMoNo, wmasReport.getMoNo());
            if (ObjectUtils.isEmpty(wmasReport.getCurrentProcess())) {
                queryWrapper.isNull(WmasReport::getCurrentProcess);
            } else {
                queryWrapper.eq(WmasReport::getCurrentProcess, wmasReport.getCurrentProcess());
            }
            if (ObjectUtils.isEmpty(baseMapper.selectVoOne(queryWrapper))) {
                wmasReport.setId(null);
                baseMapper.insert(wmasReport);
            } else {
                baseMapper.update(wmasReport, queryWrapper);
            }
        }
        return R.ok();
    }

    @Override
    public R getMoGx(String moNo, Pageable pageable) {
        // MoQueryVo moQueryVo = new MoQueryVo();
        // moQueryVo.setMokey(moNo);
        // List<SapDataMoVo> vos = sapCallHandler.call(SapFuncType.ZPPM_MAST_GET_MO3.name(), moQueryVo);
        Object[] txeAfvc = null;
        try {
            // 准备表格数据
            Object[] tableData = new Object[1];
            // 使用工具方法创建表格行 工厂数据
            tableData[0] = SapUtils.createTableRow(
                "AUFNR", moNo);
            // 调用带表格参数的SAP函数
            Map<String, Object> result = SapUtils.callFunctionWithTable(
                SapFunction.ZPPM_MAST_GET_MO,
                "TXI_AUFNR",
                tableData);
            // 获取返回结果
            txeAfvc = SapUtils.getTableData(result, "TXE_AFVC");
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
        return R.ok(txeAfvc);
    }

    @Override
    public R getPicUrl(String moNo) {
        if (ObjectUtils.isEmpty(moNo)) {
            return R.fail("mo is null");
        }
        String moUrlResult = PLMFileTool.getMoUrlResult(moNo, "");
        return R.ok("", moUrlResult);
    }

    private LambdaQueryWrapper<SapScanMaster> getSearchReportWrapper(Map<String, Object> params) {
        LambdaQueryWrapper<SapScanMaster> queryWrapper = new LambdaQueryWrapper<>();
        Object custNo = params.get("custNo");
        if (!ObjectUtils.isEmpty(custNo)) {
            queryWrapper.eq(SapScanMaster::getCustNo, custNo);
        }
        Object moNo = params.get("moNo");
        if (!ObjectUtils.isEmpty(moNo)) {
            queryWrapper.eq(SapScanMaster::getMoNo, moNo);
        }
        Object orderType = params.get("orderType");
        if (!ObjectUtils.isEmpty(orderType)) {
            queryWrapper.eq(SapScanMaster::getOrderType, orderType);
        }
        Object drawNumber = params.get("drawNumber");
        if (!ObjectUtils.isEmpty(drawNumber)) {
            queryWrapper.eq(SapScanMaster::getDrawNumber, drawNumber);
        }
        Object rangeValue0 = params.get("rangeValue[0]");
        Object rangeValue1 = params.get("rangeValue[1]");
        if (!ObjectUtils.isEmpty(rangeValue0) && !ObjectUtils.isEmpty(rangeValue1)) {
            queryWrapper.between(SapScanMaster::getBargainDate,
                LocalDateTime.parse((CharSequence) rangeValue0, DATE_TIME_FORMATTER), LocalDateTime.parse((CharSequence) rangeValue1, DATE_TIME_FORMATTER));

        }
        queryWrapper.eq(SapScanMaster::getPlanType, "Y");
        queryWrapper.ne(SapScanMaster::getOrderType, "Z270");

        // 结案状态为N
        queryWrapper.in(SapScanMaster::getMoState1, "0", "1", "4", "90")
            .notIn(SapScanMaster::getMoState, "关单", "取消", "删除", "关闭", "OK", "D/N", "多余件领出",
                "进多余件成品仓", "进多余半成品仓", "进待利用仓", "SO单", "报废", "出货", "领出", "开单领出", "转关单",
                "虚拟入库发料", "成品报废", "虚拟入库发料", "钣金转焊接", "调成品仓", "进半成品仓", "领出", "出仓分包", "包装", "成品领出", "无订单多余件入库");

        // 精密零件和模组订单
        queryWrapper.and(i -> i.or(orWrapper -> orWrapper.apply("SUBSTRING(mo_no, 3, 1) = 'P'")
            .or(orWrapper1 -> orWrapper1.apply("SUBSTRING(mo_no, 3, 1) = 'N'"))
            .or(orWrapper2 -> orWrapper2.apply("SUBSTRING(mo_no, 3, 1) = 'W'")
                .and(andWrapper -> andWrapper.in(SapScanMaster::getWorkshop, "BUA", "BUB", "PMD")))
            .or(orWrapper3 -> orWrapper3.eq(SapScanMaster::getProjectType, "40"))));
        return queryWrapper;
    }
}
