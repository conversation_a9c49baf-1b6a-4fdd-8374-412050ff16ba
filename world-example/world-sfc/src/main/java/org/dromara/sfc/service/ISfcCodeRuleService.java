package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.SfcCodeRuleBo;
import org.dromara.sfc.domain.vo.SfcCodeRuleVo;
import org.springframework.cache.annotation.Cacheable;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 类别设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-05
 */
public interface ISfcCodeRuleService {

    /**
     * 查询类别设置
     *
     * @param id 主键
     * @return 类别设置
     */
    SfcCodeRuleVo queryById(Long id);

    /**
     * 分页查询类别设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 类别设置分页列表
     */
    TableDataInfo<SfcCodeRuleVo> queryPageList(SfcCodeRuleBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的类别设置列表
     *
     * @param bo 查询条件
     * @return 类别设置列表
     */
    List<SfcCodeRuleVo> queryList(SfcCodeRuleBo bo);

    /**
     * 新增类别设置
     *
     * @param bo 类别设置
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcCodeRuleBo bo);

    /**
     * 修改类别设置
     *
     * @param bo 类别设置
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcCodeRuleBo bo);

    /**
     * 校验并批量删除类别设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据条件查询
     *
     * @return
     */
    List<SfcCodeRuleVo> selectList(String codeType);

    /**
     * 获取条形码类型列表
     *
     * @return 条形码类型的字符串列表
     */
    @Cacheable(value = "barcodeTypesCache")
    List<Map<String, Object>>  getBarcodeTypes();


    /**
     * 获取标识列表
     *
     * @return 条形码类型的字符串列表
     */
    @Cacheable(value = "barcodeAncTypesCache")
    List<Map<String, Object>>  getBarcodeAncTypes();
}
