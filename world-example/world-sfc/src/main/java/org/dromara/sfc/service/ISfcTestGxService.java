package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.SfcTestGxBo;
import org.dromara.sfc.domain.vo.SfcTestGxVo;

import java.util.Collection;
import java.util.List;

/**
 * 调试SOP设置Service接口
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
public interface ISfcTestGxService {

    /**
     * 查询调试SOP设置
     *
     * @param id 主键
     * @return 调试SOP设置
     */
    SfcTestGxVo queryById(Long id);

    /**
     * 分页查询调试SOP设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 调试SOP设置分页列表
     */
    TableDataInfo<SfcTestGxVo> queryPageList(SfcTestGxBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的调试SOP设置列表
     *
     * @param bo 查询条件
     * @return 调试SOP设置列表
     */
    List<SfcTestGxVo> queryList(SfcTestGxBo bo);

    /**
     * 新增调试SOP设置
     *
     * @param bo 调试SOP设置
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcTestGxBo bo);

    /**
     * 修改调试SOP设置
     *
     * @param bo 调试SOP设置
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcTestGxBo bo);

    /**
     * 校验并批量删除调试SOP设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
