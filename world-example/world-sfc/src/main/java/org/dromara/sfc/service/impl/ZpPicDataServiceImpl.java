package org.dromara.sfc.service.impl;


import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.ZpPicData;
import org.dromara.sfc.domain.bo.ZpPicDataBo;
import org.dromara.sfc.domain.vo.ZpPicDataVo;
import org.dromara.sfc.mapper.ZpPicDataMapper;
import org.dromara.sfc.service.IZpPicDataService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【zp_pic_data】的数据库操作Service实现
 * @createDate 2025-01-08 09:18:27
 */
@Service
public class ZpPicDataServiceImpl extends ServiceImpl<ZpPicDataMapper, ZpPicData> implements IZpPicDataService {

    @Resource
    private ZpPicDataMapper baseMapper;

    /**
     * 查询装配现场图像
     *
     * @param id 主键
     * @return 装配现场图像
     */
    @Override
    public ZpPicDataVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询装配现场图像列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配现场图像分页列表
     */
    @Override
    public TableDataInfo<ZpPicDataVo> queryPageList(ZpPicDataBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZpPicData> lqw = buildQueryWrapper(bo);
        Page<ZpPicDataVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的装配现场图像列表
     *
     * @param bo 查询条件
     * @return 装配现场图像列表
     */
    @Override
    public List<ZpPicDataVo> queryList(ZpPicDataBo bo) {
        LambdaQueryWrapper<ZpPicData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZpPicData> buildQueryWrapper(ZpPicDataBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZpPicData> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ZpPicData::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), ZpPicData::getMoNo, bo.getMoNo());
        lqw.eq(StringUtils.isNotBlank(bo.getFileFolder()), ZpPicData::getFileFolder, bo.getFileFolder());
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), ZpPicData::getFileName, bo.getFileName());
        lqw.eq(StringUtils.isNotBlank(bo.getLink()), ZpPicData::getLink, bo.getLink());
        return lqw;
    }

    /**
     * 新增装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ZpPicDataBo bo) {
        ZpPicData add = MapstructUtils.convert(bo, ZpPicData.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ZpPicDataBo bo) {
        ZpPicData update = MapstructUtils.convert(bo, ZpPicData.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZpPicData entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除装配现场图像信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Page<ZpPicDataVo> getZpPicData(ZpPicDataBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ZpPicData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoPage(pageQuery.build(), lqw);
    }
}
