package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcTestGxBo;
import org.dromara.sfc.domain.vo.SfcTestGxVo;
import org.dromara.sfc.service.ISfcTestGxService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 调试SOP设置
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Validated
@RestController
@RequestMapping("/information/testGx")
public class SfcTestGxController extends BaseController {

    @Resource
    private ISfcTestGxService sfcTestGxService;

    /**
     * 查询调试SOP设置列表
     */
    @SaCheckPermission("information:testGx:list")
    @GetMapping("/list")
    public TableDataInfo<SfcTestGxVo> list(SfcTestGxBo bo, PageQuery pageQuery) {
        return sfcTestGxService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出调试SOP设置列表
     */
    @SaCheckPermission("information:testGx:export")
    @Log(title = "调试SOP设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcTestGxBo bo, HttpServletResponse response) {
        List<SfcTestGxVo> list = sfcTestGxService.queryList(bo);
        ExcelUtil.exportExcel(list, "调试SOP设置", SfcTestGxVo.class, response);
    }

    /**
     * 获取调试SOP设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:testGx:query")
    @GetMapping("/{id}")
    public R<SfcTestGxVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(sfcTestGxService.queryById(id));
    }

    /**
     * 新增调试SOP设置
     */
    @SaCheckPermission("information:testGx:add")
    @Log(title = "调试SOP设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcTestGxBo bo) {
        return toAjax(sfcTestGxService.insertByBo(bo));
    }

    /**
     * 修改调试SOP设置
     */
    @SaCheckPermission("information:testGx:edit")
    @Log(title = "调试SOP设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcTestGxBo bo) {
        return toAjax(sfcTestGxService.updateByBo(bo));
    }

    /**
     * 删除调试SOP设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:testGx:remove")
    @Log(title = "调试SOP设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcTestGxService.deleteWithValidByIds(List.of(ids), true));
    }
}
