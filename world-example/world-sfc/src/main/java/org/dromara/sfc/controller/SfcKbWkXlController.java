package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcKbWkXlBo;
import org.dromara.sfc.domain.vo.SfcKbWkXlVo;
import org.dromara.sfc.service.ISfcKbWkXlService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 装配看板隐藏数据
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/assets/kbWkXl")
public class SfcKbWkXlController extends BaseController {

    private final ISfcKbWkXlService sfcKbWkXlService;

    /**
     * 查询装配看板隐藏数据列表
     */
    @SaCheckPermission("assets:kbWkXl:list")
    @GetMapping("/list")
    public TableDataInfo<SfcKbWkXlVo> list(SfcKbWkXlBo bo, PageQuery pageQuery) {
        return sfcKbWkXlService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出装配看板隐藏数据列表
     */
    @SaCheckPermission("assets:kbWkXl:export")
    @Log(title = "装配看板隐藏数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcKbWkXlBo bo, HttpServletResponse response) {
        List<SfcKbWkXlVo> list = sfcKbWkXlService.queryList(bo);
        ExcelUtil.exportExcel(list, "装配看板隐藏数据", SfcKbWkXlVo.class, response);
    }

    /**
     * 获取装配看板隐藏数据详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("assets:kbWkXl:query")
    @GetMapping("/{id}")
    public R<SfcKbWkXlVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(sfcKbWkXlService.queryById(id));
    }

    /**
     * 新增装配看板隐藏数据
     */
    @SaCheckPermission("assets:kbWkXl:add")
    @Log(title = "装配看板隐藏数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcKbWkXlBo bo) {
        return toAjax(sfcKbWkXlService.insertByBo(bo));
    }

    /**
     * 修改装配看板隐藏数据
     */
    @SaCheckPermission("assets:kbWkXl:edit")
    @Log(title = "装配看板隐藏数据", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcKbWkXlBo bo) {
        return toAjax(sfcKbWkXlService.updateByBo(bo));
    }

    /**
     * 删除装配看板隐藏数据
     *
     * @param ids 主键串
     */
    @SaCheckPermission("assets:kbWkXl:remove")
    @Log(title = "装配看板隐藏数据", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcKbWkXlService.deleteWithValidByIds(List.of(ids), true));
    }
}
