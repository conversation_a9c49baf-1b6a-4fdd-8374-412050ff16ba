package org.dromara.sfc.controller;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SapScanMasterSn;
import org.dromara.sfc.service.ISapScanMasterSnService;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/masterSn/")
public class MasterSnController extends BaseController {
    @Resource
    ISapScanMasterSnService sapScanMasterSnService;

    @GetMapping("list")
    public R<List<SapScanMasterSn>> list(String moNo) {
        log.info("moNo:{}", moNo);
        if (ObjectUtils.isEmpty(moNo)) {
            log.error("moNo is null");
            return null;
        }
        try {
            return R.ok(sapScanMasterSnService.getSapScanMasterSns(moNo, null));
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }

    @PostMapping("/save")
    @Log(title = "维护客户SN", businessType = BusinessType.INSERT)
    public void save(@RequestBody SapScanMasterSn sapScanMasterSn) {
        sapScanMasterSnService.saveOrUpdate(sapScanMasterSn);
        // 同步sqlserver
        try {
            sapScanMasterSnService.syncRemote(sapScanMasterSn);
        } catch (SQLException e) {
            throw new RuntimeException(e);
        }
    }
}
