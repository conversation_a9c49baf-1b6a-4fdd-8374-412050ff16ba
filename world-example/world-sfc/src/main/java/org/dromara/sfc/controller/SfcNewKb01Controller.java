package org.dromara.sfc.controller;

import java.time.Duration;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SfcNewKb01;
import org.dromara.sfc.domain.bo.SfcNewKb01Bo;
import org.dromara.sfc.domain.dto.SfcNewKbDTO;
import org.dromara.sfc.domain.dto.WkXlDataDTO;
import org.dromara.sfc.domain.vo.SfcNewKb01Vo;
import org.dromara.sfc.service.ISfcNewKb01Service;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 装配看板记录
 *
 * <AUTHOR>
 * @date 2025-02-27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/assembly/kanban")
public class SfcNewKb01Controller extends BaseController {

    private final ISfcNewKb01Service sfcNewKb01Service;
    private static final String KANBAN_CACHE_KEY = "kanban:list:";
    private static final int CACHE_EXPIRE_MINUTES = 3;

    /**
     * 查询装配看板记录列表
     */
    @SaCheckPermission("assembly:kanban:list")
    @GetMapping("/list")
    public TableDataInfo<SfcNewKb01Vo> list(SfcNewKb01Bo bo, PageQuery pageQuery) {
        return sfcNewKb01Service.queryPageList(bo, pageQuery);
    }

    /**
     * 导出装配看板记录列表
     */
    @SaCheckPermission("assembly:kanban:export")
    @Log(title = "装配看板记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcNewKb01Bo bo, HttpServletResponse response) {
        List<SfcNewKb01Vo> list = sfcNewKb01Service.queryList(bo);
        ExcelUtil.exportExcel(list, "装配看板记录", SfcNewKb01Vo.class, response);
    }

    /**
     * 装配看板实时状态下载
     *
     * @param response 请求
     */
    @SaCheckPermission("assembly:kanban:download")
    @Log(title = "装配看板记录", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void downReport(HttpServletResponse response) {
        LambdaQueryWrapper<SfcNewKb01> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
            .ge(SfcNewKb01::getSysdt, LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " 00:00")
            .orderByAsc(SfcNewKb01::getId);
        List<SfcNewKb01Vo> list = sfcNewKb01Service.fetchSfcNewKb01FromSysdtTodayOrderedById();
        for (int i = 0; i < list.size(); i++) {
            SfcNewKb01Vo sfcNewKb = list.get(i);
            sfcNewKb.setId((long) (i + 1));
            sfcNewKb.setMoSta(ObjectUtils.isEmpty(sfcNewKb.getMoSta()) ? sfcNewKb.getMoNo() : sfcNewKb.getMoSta());
        }
        List<SfcNewKbDTO> dtoList = list.stream().map(en -> {
            SfcNewKbDTO dto = new SfcNewKbDTO();
            BeanUtil.copyProperties(en, dto);
            return dto;
        }).toList();
        ExcelUtil.exportExcel(dtoList, "装配看板实时状态记录", SfcNewKbDTO.class, response);
    }

    /**
     * 工站效率实时状态下载
     *
     * @param response 请求
     */
    @SaCheckPermission("assembly:kanban:download")
    @Log(title = "工站效率记录", businessType = BusinessType.EXPORT)
    @PostMapping("/download/wk")
    public void downWkReport(HttpServletResponse response) {
        List<WkXlDataDTO> data = sfcNewKb01Service.getWkXlData();
        ExcelUtil.exportExcel(data, "工站效率实时状态记录", WkXlDataDTO.class, response);
    }

    /**
     * 获取装配看板记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("assembly:kanban:query")
    @GetMapping("/{id}")
    public R<SfcNewKb01Vo> getInfo(@NotNull(message = "主键不能为空") @PathVariable Long id) {
        return R.ok(sfcNewKb01Service.queryById(id));
    }

    /**
     * 新增装配看板记录
     */
    @SaCheckPermission("assembly:kanban:add")
    @Log(title = "装配看板记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcNewKb01Bo bo) {
        return toAjax(sfcNewKb01Service.insertByBo(bo));
    }

    /**
     * 修改装配看板记录
     */
    @SaCheckPermission("assembly:kanban:edit")
    @Log(title = "装配看板记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcNewKb01Bo bo) {
        return toAjax(sfcNewKb01Service.updateByBo(bo));
    }

    /**
     * 删除装配看板记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("assembly:kanban:remove")
    @Log(title = "装配看板记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable Long[] ids) {
        return toAjax(sfcNewKb01Service.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 分页查询用户列表
     */
    @GetMapping("/getList")
    public R<Map<String, Object>> list(@RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        try {
            // 构建缓存key
            String cacheKey = KANBAN_CACHE_KEY + pageNum + ":" + pageSize;
            // 尝试从Redis缓存获取数据
            Map<String, Object> cachedResult = RedisUtils.getCacheObject(cacheKey);
            if (cachedResult != null) {
                return R.ok(cachedResult);
            }
            // 缓存未命中，查询数据库
            PageQuery pageQuery = new PageQuery(pageSize, pageNum);
            Map<String, Object> result = getDateModels(pageQuery);
            // 将结果存入Redis缓存
            RedisUtils.setCacheObject(cacheKey, result, Duration.ofMillis(60));
            return R.ok(result);
        } catch (Exception e) {
            log.error("获取看板列表失败", e);
            return R.fail("获取数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取echars数据
     *
     * @return
     */
    @GetMapping("/wk-xl")
    public R<List<WkXlDataDTO>> getWkXlData() {
        List<WkXlDataDTO> data = sfcNewKb01Service.getWkXlData();
        return R.ok(data);
    }

    @NotNull
    private Map<String, Object> getDateModels(PageQuery pageQuery) {
        // 参数校验和限制
        pageQuery.setPageNum(Math.max(1, pageQuery.getPageNum()));
        pageQuery.setPageSize(Math.min(100, Math.max(1, pageQuery.getPageSize())));

        Map<String, Object> result = new HashMap<>(4);
        result.put("data", sfcNewKb01Service.getSfcNewKbList1(pageQuery));
        result.put("total", sfcNewKb01Service.getSfcNewKbCount());
        result.put("pageNum", pageQuery.getPageNum());
        result.put("pageSize", pageQuery.getPageSize());
        return result;
    }
}
