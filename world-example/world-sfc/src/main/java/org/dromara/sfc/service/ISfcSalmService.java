package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.SfcSalmBo;
import org.dromara.sfc.domain.vo.SfcSalmVo;

import java.util.Collection;
import java.util.List;

/**
 * 人员设定记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-01
 */
public interface ISfcSalmService {

    /**
     * 查询人员设定记录
     *
     * @param id 主键
     * @return 人员设定记录
     */
    SfcSalmVo queryById(Long id);

    /**
     * 分页查询人员设定记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 人员设定记录分页列表
     */
    TableDataInfo<SfcSalmVo> queryPageList(SfcSalmBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的人员设定记录列表
     *
     * @param bo 查询条件
     * @return 人员设定记录列表
     */
    List<SfcSalmVo> queryList(SfcSalmBo bo);

    /**
     * 新增人员设定记录
     *
     * @param bo 人员设定记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcSalmBo bo);

    /**
     * 修改人员设定记录
     *
     * @param bo 人员设定记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcSalmBo bo);

    /**
     * 校验并批量删除人员设定记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
