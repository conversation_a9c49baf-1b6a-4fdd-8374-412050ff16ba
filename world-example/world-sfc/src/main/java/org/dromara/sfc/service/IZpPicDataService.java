package org.dromara.sfc.service;


import java.util.Collection;
import java.util.List;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.ZpPicData;
import org.dromara.sfc.domain.bo.ZpPicDataBo;
import org.dromara.sfc.domain.vo.ZpPicDataVo;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <AUTHOR>
 * @description 针对表【zp_pic_data】的数据库操作Service
 * @createDate 2025-01-08 09:18:27
 */
public interface IZpPicDataService extends IService<ZpPicData> {
    /**
     * 查询装配现场图像
     *
     * @param id 主键
     * @return 装配现场图像
     */
    ZpPicDataVo queryById(Long id);

    /**
     * 分页查询装配现场图像列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配现场图像分页列表
     */
    TableDataInfo<ZpPicDataVo> queryPageList(ZpPicDataBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的装配现场图像列表
     *
     * @param bo 查询条件
     * @return 装配现场图像列表
     */
    List<ZpPicDataVo> queryList(ZpPicDataBo bo);

    /**
     * 新增装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否新增成功
     */
    Boolean insertByBo(ZpPicDataBo bo);

    /**
     * 修改装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否修改成功
     */
    Boolean updateByBo(ZpPicDataBo bo);

    /**
     * 校验并批量删除装配现场图像信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Page<ZpPicDataVo> getZpPicData(ZpPicDataBo bo, PageQuery pageQuery);
}
