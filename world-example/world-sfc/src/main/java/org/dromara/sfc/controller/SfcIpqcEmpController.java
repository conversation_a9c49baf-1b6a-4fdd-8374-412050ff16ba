package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SfcIpqcEmp;
import org.dromara.sfc.domain.bo.SfcIpqcEmpBo;
import org.dromara.sfc.domain.vo.SfcIpqcEmpVo;
import org.dromara.sfc.service.ISfcIpqcEmpService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * IPQC人员设置
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/ipqcEmp")
public class SfcIpqcEmpController extends BaseController {

    private final ISfcIpqcEmpService sfcIpqcEmpService;

    /**
     * 查询IPQC人员设置列表
     */
    @SaCheckPermission("information:ipqcEmp:list")
    @GetMapping("/list")
    public TableDataInfo<SfcIpqcEmpVo> list(SfcIpqcEmpBo bo, PageQuery pageQuery) {
        return sfcIpqcEmpService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出IPQC人员设置列表
     */
    @SaCheckPermission("information:ipqcEmp:export")
    @Log(title = "IPQC人员设置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcIpqcEmpBo bo, HttpServletResponse response) {
        List<SfcIpqcEmpVo> list = sfcIpqcEmpService.queryList(bo);
        ExcelUtil.exportExcel(list, "IPQC人员设置", SfcIpqcEmpVo.class, response);
    }

    /**
     * 获取IPQC人员设置详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:ipqcEmp:query")
    @GetMapping("/{id}")
    public R<SfcIpqcEmpVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(sfcIpqcEmpService.queryById(id));
    }

    /**
     * 新增IPQC人员设置
     */
    @SaCheckPermission("information:ipqcEmp:add")
    @Log(title = "IPQC人员设置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcIpqcEmpBo bo) {
        return toAjax(sfcIpqcEmpService.insertByBo(bo));
    }

    /**
     * 修改IPQC人员设置
     */
    @SaCheckPermission("information:ipqcEmp:edit")
    @Log(title = "IPQC人员设置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcIpqcEmpBo bo) {
        return toAjax(sfcIpqcEmpService.updateByBo(bo));
    }

    /**
     * 删除IPQC人员设置
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:ipqcEmp:remove")
    @Log(title = "IPQC人员设置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcIpqcEmpService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 查询单个记录
     */
    @GetMapping("/one")
    public R getOne(SfcIpqcEmp ipqcEmpQuery) {
        return sfcIpqcEmpService.getIpqcEmpInfo(ipqcEmpQuery);
    }
}
