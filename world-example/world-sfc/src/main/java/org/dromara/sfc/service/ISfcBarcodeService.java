package org.dromara.sfc.service;

import com.baomidou.mybatisplus.extension.service.IService;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcBarcode;
import org.dromara.sfc.domain.bo.SfcBarcodeBo;
import org.dromara.sfc.domain.vo.SfcBarcodeVo;
import org.dromara.sfc.query.BarcodeQuery;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.sql.SQLException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 非生产条码Service接口
 *
 * <AUTHOR>
 * @date 2025-02-25
 */
public interface ISfcBarcodeService extends IService<SfcBarcode> {

    /**
     * 查询非生产条码
     *
     * @param id 主键
     * @return 非生产条码
     */
    SfcBarcodeVo queryById(Long id);

    /**
     * 分页查询非生产条码列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 非生产条码分页列表
     */
    TableDataInfo<SfcBarcodeVo> queryPageList(SfcBarcodeBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的非生产条码列表
     *
     * @param bo 查询条件
     * @return 非生产条码列表
     */
    List<SfcBarcodeVo> queryList(SfcBarcodeBo bo);

    /**
     * 新增非生产条码
     *
     * @param bo 非生产条码
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcBarcodeBo bo);

    /**
     * 修改非生产条码
     *
     * @param bo 非生产条码
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcBarcodeBo bo);

    /**
     * 校验并批量删除非生产条码信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 非生成接口生成条码
     * @param response
     * @param type
     * @throws IOException
     */
    void generateAnotherBarcode(HttpServletResponse response, String type) throws IOException;

    /**
     * 处理上传的SOP文件
     *
     * @param file 文件
     * @return Response
     */
    R dealSopFile(MultipartFile file);

    /**
     * 获取装配未扫描的条码
     */
    R getAbnormalData();

    /**
     * 查询
     *
     * @param barcodeQuery 条件
     * @return Response
     */
    R queryBarcodeInfo(BarcodeQuery barcodeQuery) throws SQLException;

    R createBarcode(Map<String, String> map) throws Exception;

    R queryAnotherBarcodeInfo(BarcodeQuery barcodeQuery);
}
