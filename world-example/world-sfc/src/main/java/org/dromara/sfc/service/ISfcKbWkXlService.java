package org.dromara.sfc.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.bo.SfcKbWkXlBo;
import org.dromara.sfc.domain.vo.SfcKbWkXlVo;

import java.util.Collection;
import java.util.List;

/**
 * 装配看板隐藏数据Service接口
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
public interface ISfcKbWkXlService {

    /**
     * 查询装配看板隐藏数据
     *
     * @param id 主键
     * @return 装配看板隐藏数据
     */
    SfcKbWkXlVo queryById(Long id);

    /**
     * 分页查询装配看板隐藏数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配看板隐藏数据分页列表
     */
    TableDataInfo<SfcKbWkXlVo> queryPageList(SfcKbWkXlBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的装配看板隐藏数据列表
     *
     * @param bo 查询条件
     * @return 装配看板隐藏数据列表
     */
    List<SfcKbWkXlVo> queryList(SfcKbWkXlBo bo);

    /**
     * 新增装配看板隐藏数据
     *
     * @param bo 装配看板隐藏数据
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcKbWkXlBo bo);

    /**
     * 修改装配看板隐藏数据
     *
     * @param bo 装配看板隐藏数据
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcKbWkXlBo bo);

    /**
     * 校验并批量删除装配看板隐藏数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
