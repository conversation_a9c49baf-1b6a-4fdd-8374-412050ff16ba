package org.dromara.sfc.service;

import java.util.Collection;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.sfc.domain.bo.ZpPicBo;
import org.dromara.sfc.domain.vo.ZpPicVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 装配现场图像Service接口
 *
 * <AUTHOR>
 * @date 2025-05-06
 */
public interface IZpPicService {

    /**
     * 查询装配现场图像
     *
     * @param id 主键
     * @return 装配现场图像
     */
    ZpPicVo queryById(Long id);

    /**
     * 分页查询装配现场图像列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 装配现场图像分页列表
     */
    TableDataInfo<ZpPicVo> queryPageList(ZpPicBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的装配现场图像列表
     *
     * @param bo 查询条件
     * @return 装配现场图像列表
     */
    List<ZpPicVo> queryList(ZpPicBo bo);

    /**
     * 新增装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否新增成功
     */
    Boolean insertByBo(ZpPicBo bo);

    /**
     * 修改装配现场图像
     *
     * @param bo 装配现场图像
     * @return 是否修改成功
     */
    Boolean updateByBo(ZpPicBo bo);

    /**
     * 校验并批量删除装配现场图像信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R dealMoFile(MultipartFile file);

    R dealZpFile(UploadResult file, String moNo, String picType, Boolean flag);

    ZpPicVo getZpMoData(String mo);
}
