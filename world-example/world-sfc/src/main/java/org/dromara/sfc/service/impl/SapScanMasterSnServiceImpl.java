package org.dromara.sfc.service.impl;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.SapScanMasterSn;
import org.dromara.sfc.domain.bo.SapScanMasterSnBo;
import org.dromara.sfc.domain.vo.SapScanMasterSnVo;
import org.dromara.sfc.mapper.SapScanMasterSnMapper;
import org.dromara.sfc.service.ISapScanMasterService;
import org.dromara.sfc.service.ISapScanMasterSnService;
import org.dromara.sfc.utils.RowMapperUtil;
import org.dromara.sfc.utils.SqlserverJdbcUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import lombok.RequiredArgsConstructor;

/**
 * sap_scan_master_snService业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@RequiredArgsConstructor
@Service
public class SapScanMasterSnServiceImpl extends ServiceImpl<SapScanMasterSnMapper, SapScanMasterSn> implements ISapScanMasterSnService {

    private final SapScanMasterSnMapper baseMapper;

    private final ISapScanMasterService sapScanMasterService;

    // 缓存过期时间（分钟）
    private static final int CACHE_EXPIRE_MINUTES = 1800;
    private static final String CACHE_KEY_PREFIX = "sap_scan:master_sn:";

    /**
     * 查询sap_scan_master_sn
     *
     * @param myRowId 主键
     * @return sap_scan_master_sn
     */
    @Override
    public SapScanMasterSnVo queryById(Long myRowId) {
        return baseMapper.selectVoById(myRowId);
    }

    /**
     * 分页查询sap_scan_master_sn列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return sap_scan_master_sn分页列表
     */
    @Override
    public TableDataInfo<SapScanMasterSnVo> queryPageList(SapScanMasterSnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SapScanMasterSn> lqw = buildQueryWrapper(bo);
        Page<SapScanMasterSnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的sap_scan_master_sn列表
     *
     * @param bo 查询条件
     * @return sap_scan_master_sn列表
     */
    @Override
    public List<SapScanMasterSnVo> queryList(SapScanMasterSnBo bo) {
        LambdaQueryWrapper<SapScanMasterSn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SapScanMasterSn> buildQueryWrapper(SapScanMasterSnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SapScanMasterSn> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SapScanMasterSn::getMyRowId);
        lqw.eq(StringUtils.isNotBlank(bo.getDrNo()), SapScanMasterSn::getDrNo, bo.getDrNo());
        lqw.eq(StringUtils.isNotBlank(bo.getDrItm()), SapScanMasterSn::getDrItm, bo.getDrItm());
        lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), SapScanMasterSn::getMoNo, bo.getMoNo());
        lqw.eq(bo.getMQty() != null, SapScanMasterSn::getMQty, bo.getMQty());
        lqw.eq(bo.getCnt2() != null, SapScanMasterSn::getCnt2, bo.getCnt2());
        lqw.eq(StringUtils.isNotBlank(bo.getSn()), SapScanMasterSn::getSn, bo.getSn());
        lqw.eq(StringUtils.isNotBlank(bo.getRem()), SapScanMasterSn::getRem, bo.getRem());
        lqw.eq(StringUtils.isNotBlank(bo.getHost()), SapScanMasterSn::getHost, bo.getHost());
        lqw.eq(bo.getSysdt() != null, SapScanMasterSn::getSysdt, bo.getSysdt());
        lqw.eq(StringUtils.isNotBlank(bo.getCusSn()), SapScanMasterSn::getCusSn, bo.getCusSn());
        return lqw;
    }

    /**
     * 新增sap_scan_master_sn
     *
     * @param bo sap_scan_master_sn
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SapScanMasterSnBo bo) {
        SapScanMasterSn add = MapstructUtils.convert(bo, SapScanMasterSn.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMyRowId(add.getMyRowId());
        }
        return flag;
    }

    /**
     * 修改sap_scan_master_sn
     *
     * @param bo sap_scan_master_sn
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SapScanMasterSnBo bo) {
        SapScanMasterSn update = MapstructUtils.convert(bo, SapScanMasterSn.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SapScanMasterSn entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除sap_scan_master_sn信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public List<SapScanMasterSn> getSapScanMasterSns(String mo, Integer num) throws SQLException {
        if (StringUtils.isEmpty(mo)) {
            throw new IllegalArgumentException("MO号不能为空");
        }

        LambdaQueryWrapper<SapScanMasterSn> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SapScanMasterSn::getMoNo, mo);
        List<SapScanMasterSn> masterSnList = baseMapper.selectList(queryWrapper);
        if (!ObjectUtils.isEmpty(masterSnList)) {
            // mysql存在数据，则直接返回
            return masterSnList;
        }

        // 没有则读sqlserver库
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        String sql = "select * from master_sn where mo_no = ?";
        List<SapScanMasterSn> sapScanMasterSns = SqlserverJdbcUtils.executeQuery(sql, getSapScanMasterSnRowMapper(), Collections.singletonList(mo));
        if (ObjectUtils.isEmpty(sapScanMasterSns)) {
            // master_sn表没有数据，则生成数据插入，先从master表查出DR_NO、DR_ITM
            SapScanMaster master = sapScanMasterService.getMasterByMo(mo);
            if (ObjectUtils.isEmpty(master) || ObjectUtils.isEmpty(master.getDrNo()) || ObjectUtils.isEmpty(master.getDrItm())) {
                throw new RuntimeException("MO没有对应的DR_NO、DR_ITM");
            }
            sql = "select * from master_sn where dr_no = ? and dr_itm = ?";
            String drNo = master.getDrNo().trim();
            String drItm = master.getDrItm().trim();
            num = ObjectUtils.isEmpty(num) ? master.getOrderQty().intValue() : num;
            List<SapScanMasterSn> sapScanMasterSnList = SqlserverJdbcUtils.executeQuery(sql, getSapScanMasterSnRowMapper(), Arrays.asList(drNo, drItm));
            // 获取最大的CNT2
            long maxCnt2 = sapScanMasterSnList.stream().map(SapScanMasterSn::getCnt2).max(Long::compareTo).orElse(0L);
            for (int i = 1; i <= num; i++) {
                SapScanMasterSn sapScanMasterSn = new SapScanMasterSn();
                sapScanMasterSn.setDrNo(drNo);
                sapScanMasterSn.setDrItm(drItm);
                sapScanMasterSn.setMoNo(mo);
                sapScanMasterSn.setMQty(Long.valueOf(num));
                sapScanMasterSn.setCnt2(maxCnt2 + i);
                // “WD”+DR号+DR行号+流水号（4位）共18位
                sapScanMasterSn.setSn("WD" + sapScanMasterSn.getDrNo() + sapScanMasterSn.getDrItm() + String.format("%04d", sapScanMasterSn.getCnt2()));
                sapScanMasterSns.add(sapScanMasterSn);
            }
            // 插入master_sn表
            sql = "insert into master_sn(DR_NO,DR_ITM,MO_NO,M_QTY,CNT2,SN) values(?,?,?,?,?,?)";
            List<Object[]> params = sapScanMasterSns.stream().map(en -> new Object[]{en.getDrNo(), en.getDrItm(), en.getMoNo(), en.getMQty(), en.getCnt2(), en.getSn()}).toList();
            SqlserverJdbcUtils.executeBatchInsert(sql, params);
            // SQLSERVER不支持批量返回自增ID，需重新获取数据
            sql = "select * from master_sn where mo_no = ?";
            sapScanMasterSns = SqlserverJdbcUtils.executeQuery(sql, getSapScanMasterSnRowMapper(), Collections.singletonList(mo));
        }
        // 插入sap_scan_master_sn表
        this.saveBatch(sapScanMasterSns);
        return sapScanMasterSns;

    }

    /**
     * 从MySQL获取数据
     */
    private List<SapScanMasterSn> getMysqlData(String mo) {
        return baseMapper.selectList(new LambdaQueryWrapper<SapScanMasterSn>()
            .eq(SapScanMasterSn::getMoNo, mo));
    }

    /**
     * 从SQLServer获取或生成数据
     */
    private List<SapScanMasterSn> getSqlServerData(String mo, Long num) throws SQLException {
        SqlserverJdbcUtils.switchDataSource("sqlserver1");

        // 1. 先从master_sn表查询
        List<SapScanMasterSn> sqlServerResult = queryFromMasterSn(mo);
        if (!ObjectUtils.isEmpty(sqlServerResult)) {
            return sqlServerResult;
        }

        // 2. 生成新数据
        return generateNewMasterSnData(mo, num);
    }

    /**
     * 从SQLServer的master_sn表查询数据
     */
    private List<SapScanMasterSn> queryFromMasterSn(String mo) throws SQLException {
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        String sql = "select * from master_sn where mo_no = ?";
        return SqlserverJdbcUtils.executeQuery(sql, getSapScanMasterSnRowMapper(),
            Collections.singletonList(mo));
    }

    /**
     * 生成新的master_sn数据
     */
    private List<SapScanMasterSn> generateNewMasterSnData(String mo, Long num) throws SQLException {
        // 1. 获取master表数据
        SapScanMaster master = sapScanMasterService.getMasterByMo(mo);
        if (ObjectUtils.isEmpty(master) || StringUtils.isEmpty(master.getDrNo())
            || StringUtils.isEmpty(master.getDrItm())) {
            throw new RuntimeException("未找到对应的DR_NO、DR_ITM数据");
        }

        String drNo = master.getDrNo().trim();
        String drItm = master.getDrItm().trim();
        num = ObjectUtils.isEmpty(num) ? master.getOrderQty() : num;

        // 2. 获取现有数据的最大CNT2
        long maxCnt2 = getMaxCnt2(drNo, drItm);

        // 3. 生成新数据
        List<SapScanMasterSn> newDataList = generateMasterSnList(drNo, drItm, mo, num, maxCnt2);

        // 4. 批量插入SQLServer
        batchInsertToSqlServer(newDataList);

        // 5. 重新查询获取完整数据（包含ID）
        return queryFromMasterSn(mo);
    }

    /**
     * 获取最大的CNT2值
     */
    private long getMaxCnt2(String drNo, String drItm) throws SQLException {
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        String sql = "select * from master_sn where dr_no = ? and dr_itm = ?";
        List<SapScanMasterSn> existingList = SqlserverJdbcUtils.executeQuery(sql,
            getSapScanMasterSnRowMapper(),
            Arrays.asList(drNo, drItm));

        return existingList.stream()
            .map(SapScanMasterSn::getCnt2)
            .max(Long::compareTo)
            .orElse(0L);
    }

    /**
     * 生成新的master_sn列表
     */
    private List<SapScanMasterSn> generateMasterSnList(String drNo, String drItm, String mo,
                                                       Long num, long maxCnt2) {
        List<SapScanMasterSn> newDataList = new ArrayList<>();
        for (int i = 1; i <= num; i++) {
            SapScanMasterSn entity = new SapScanMasterSn();
            entity.setDrNo(drNo);
            entity.setDrItm(drItm);
            entity.setMoNo(mo);
            entity.setMQty(num);
            entity.setCnt2(maxCnt2 + i);
            entity.setSn(generateSn(drNo, drItm, maxCnt2 + i));
            newDataList.add(entity);
        }
        return newDataList;
    }

    /**
     * 生成SN编号
     */
    private String generateSn(String drNo, String drItm, long cnt2) {
        return String.format("WD%s%s%04d", drNo, drItm, cnt2);
    }

    /**
     * 批量插入SQLServer
     */
    private void batchInsertToSqlServer(List<SapScanMasterSn> dataList) throws SQLException {
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        String sql = "insert into master_sn(DR_NO,DR_ITM,MO_NO,M_QTY,CNT2,SN) values(?,?,?,?,?,?)";
        List<Object[]> params = dataList.stream()
            .map(en -> new Object[]{
                en.getDrNo(), en.getDrItm(), en.getMoNo(),
                en.getMQty(), en.getCnt2(), en.getSn()
            })
            .collect(Collectors.toList());

        SqlserverJdbcUtils.executeBatchInsert(sql, params);
    }

    /**
     * 批量保存到MySQL
     */
    private void saveBatchToMysql(List<SapScanMasterSn> dataList) {
        if (!ObjectUtils.isEmpty(dataList)) {
            baseMapper.insertBatch(dataList);
        }
    }

    @Override
    public void syncRemote(SapScanMasterSn sapScanMasterSn) throws SQLException {
        SqlserverJdbcUtils.switchDataSource("sqlserver1");
        String sql = "update master_sn set cus_sn = ?, rem = ?, sysdt = ? where id = ?";
        SqlserverJdbcUtils.executeUpdate(sql,
            Arrays.asList(sapScanMasterSn.getCusSn(), sapScanMasterSn.getRem(), new Date(), sapScanMasterSn.getId()));
    }

    // RowMapper保持不变
    private static RowMapperUtil.RowMapper<SapScanMasterSn> getSapScanMasterSnRowMapper() {
        return (rs) -> {
            SapScanMasterSn entity = new SapScanMasterSn();
            entity.setId(rs.getLong("id"));
            entity.setDrNo(rs.getString("dr_no"));
            entity.setDrItm(rs.getString("dr_itm"));
            entity.setMoNo(rs.getString("mo_no"));
            entity.setMQty(rs.getLong("m_qty"));
            entity.setCnt2(rs.getLong("cnt2"));
            entity.setSn(rs.getString("sn"));
            entity.setRem(rs.getString("rem"));
            entity.setHost(rs.getString("host"));
            entity.setSysdt(rs.getDate("sysdt"));
            entity.setCusSn(rs.getString("cus_sn"));
            return entity;
        };
    }

}
