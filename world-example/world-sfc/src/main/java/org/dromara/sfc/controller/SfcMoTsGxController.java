package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.apache.poi.util.IOUtils;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SfcMoTsGx;
import org.dromara.sfc.domain.bo.SfcMoTsGxBo;
import org.dromara.sfc.domain.dto.TestGxDTO;
import org.dromara.sfc.domain.vo.SfcMoTsGxVo;
import org.dromara.sfc.domain.vo.TestGxInformation;
import org.dromara.sfc.service.ISfcMoTsGxService;
import org.springframework.http.HttpHeaders;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 调试SOP条码
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/moTsGx")
public class SfcMoTsGxController extends BaseController {

    private final ISfcMoTsGxService sfcMoTsGxService;

    /**
     * 查询调试SOP条码列表
     */
    @SaCheckPermission("information:moTsGx:list")
    @GetMapping("/list")
    public TableDataInfo<SfcMoTsGxVo> list(SfcMoTsGxBo bo, PageQuery pageQuery) {
        return sfcMoTsGxService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出调试SOP条码列表
     */
    @SaCheckPermission("information:moTsGx:export")
    @Log(title = "调试SOP条码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcMoTsGxBo bo, HttpServletResponse response) {
        List<SfcMoTsGxVo> list = sfcMoTsGxService.queryList(bo);
        ExcelUtil.exportExcel(list, "调试SOP条码", SfcMoTsGxVo.class, response);
    }

    /**
     * 获取调试SOP条码详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:moTsGx:query")
    @GetMapping("/{id}")
    public R<SfcMoTsGxVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(sfcMoTsGxService.queryById(id));
    }

    /**
     * 新增调试SOP条码
     */
    @SaCheckPermission("information:moTsGx:add")
    @Log(title = "调试SOP条码", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcMoTsGxBo bo) {
        return toAjax(sfcMoTsGxService.insertByBo(bo));
    }

    /**
     * 修改调试SOP条码
     */
    @SaCheckPermission("information:moTsGx:edit")
    @Log(title = "调试SOP条码", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcMoTsGxBo bo) {
        return toAjax(sfcMoTsGxService.updateByBo(bo));
    }

    /**
     * 删除调试SOP条码
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:moTsGx:remove")
    @Log(title = "调试SOP条码", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcMoTsGxService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 生成条码
     *
     * @param testGxInformation 工序信息
     * @return Response
     */
    @SaCheckPermission("information:moTsGx:generate")
    @PostMapping("/generateTsBarcode")
    @Log(title = "调试SOP条码生成", businessType = BusinessType.INSERT)
    public R<Void> generateTsBarcode(@RequestBody TestGxInformation testGxInformation) {
        return sfcMoTsGxService.generateTsBarcode(testGxInformation);
    }

    /**
     * 导出条码pdf
     *
     * @param response 响应
     * @param fileName 文件名
     */
    @SaCheckPermission("information:moTsGx:create")
    @Log(title = "调试SOP条码", businessType = BusinessType.EXPORT)
    @PostMapping("/create_excle")
    public void export(String fileName, HttpServletResponse response) {
        // 对文件名进行URL编码并替换 "+" 为 "%20"
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replace("+", "%20");
        // 配置响应
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + encodedFileName);
        // 将PDF文件内容写入响应输出流
        OssClient storage = OssFactory.instance("minio");
        // 将PDF文件内容写入响应输出流
        try (InputStream inputStream = storage.getObjectContent(fileName, Constants.BUCKET_TS_BARCODE);
             OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            IOUtils.closeQuietly(outputStream);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传SOP文件并处理
     * 该接口用于接收前端上传的SOP文件，并调用服务层方法处理该文件，返回处理结果
     *
     * @param file 上传的SOP文件
     * @return 返回包含处理结果的R<List<TestGxDTO>>对象
     */
    @Log(title = "调试SOP导入", businessType = BusinessType.IMPORT)
    @SaCheckPermission("information:moTsGx:sop")
    @PostMapping("/sop")
    public TableDataInfo<TestGxDTO> uploadSop(@RequestParam("file") MultipartFile file) {
//        TableDataInfo
        R<List<TestGxDTO>> listR = sfcMoTsGxService.dealSopFile(file);
        List<TestGxDTO> data = listR.getData();
        return new TableDataInfo<>(data,data.size());
    }


    /**
     * 联表查询
     *
     * @param moTsGxQuery 条件
     * @return Response
     */
    @GetMapping("/query")
    public R query(SfcMoTsGx moTsGxQuery) {
        return sfcMoTsGxService.queryMoTsGxInfo(moTsGxQuery);
    }
}


