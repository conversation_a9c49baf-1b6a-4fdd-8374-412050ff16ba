package org.dromara.sfc.controller;

import org.dromara.sfc.utils.SqlserverJdbcUtils;

import java.sql.Connection;

/**
 * <AUTHOR>
 * @Date 2025/3/3 8:11
 */
public class JdbcTest {
    public static void main(String[] args) {
        try {
            SqlserverJdbcUtils.switchDataSource("db1");
            // 显式加载驱动（可选，但建议旧环境使用）
            Connection connection1 = SqlserverJdbcUtils.getConnection();
//            Connection connection = SqlserverHrJdbcUtils.getConnection();
            System.out.println("连接成功！");
            connection1.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
