package org.dromara.sfc.controller;


import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SfcFileInfo;
import org.dromara.sfc.service.ISfcFileInfoService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/information/sfcFileInfo")
public class SfcFileInfoController extends BaseController {
    private final ISfcFileInfoService sfcFileInfoService;

    public SfcFileInfoController(ISfcFileInfoService sfcFileInfoService) {
        this.sfcFileInfoService = sfcFileInfoService;
    }

    // /**
    //  * 获取参数配置列表
    //  *
    //  * @param fileInfoQuery 参数配置查询条件
    //  * @param pageable      分页参数
    //  * @return 参数配置集合
    //  */
    // @GetMapping("/list")
    // public IPage<SfcFileInfo> list(FileInfoQuery fileInfoQuery, Pageable pageable) {
    //     Page<SfcFileInfo> page = QueryWrapperUtil.getPage(pageable);
    //     QueryWrapper<SfcFileInfo> queryWrapper = QueryWrapperUtil.wrapper(fileInfoQuery);
    //     sfcFileInfoService.page(page, queryWrapper);
    //     return page;
    // }

    /**
     * 查询
     *
     * @return List<SfcFileInfo>
     */
    @GetMapping("/data")
    public R data(@RequestParam("moNo") String mo, @RequestParam("type") String type) {
        return R.ok(sfcFileInfoService.getFileInfos(mo, type));
    }

    @PostMapping("/add")
    public R saveFileInfos(@RequestBody List<SfcFileInfo> sfcFileInfos) {
        sfcFileInfoService.saveOrUpdateBatch(sfcFileInfos);
        return R.ok();
    }
}
