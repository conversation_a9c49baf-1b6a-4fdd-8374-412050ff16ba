package org.dromara.sfc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.dromara.common.core.domain.R;
import org.dromara.sfc.domain.SapScanSalm;
import org.dromara.sfc.service.ISapScanSalmService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/sapscansalm")
public class SapScanSalmController {
    private ISapScanSalmService sapScanSalmService;

    public SapScanSalmController(ISapScanSalmService sapScanSalmService) {
        this.sapScanSalmService = sapScanSalmService;
    }

    @GetMapping("/one")
    public R one(@RequestParam String salNo) {
        QueryWrapper<SapScanSalm> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sal_no", salNo).last("limit 1");
        return R.ok(sapScanSalmService.getOne(queryWrapper));
    }
}
