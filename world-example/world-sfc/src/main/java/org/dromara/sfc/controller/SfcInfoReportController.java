package org.dromara.sfc.controller;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SfcInfoBo;
import org.dromara.sfc.domain.bo.SfcInfoQueryBo;
import org.dromara.sfc.domain.dto.InfoReportDTO;
import org.dromara.sfc.domain.dto.InfoReportGs2DTO;
import org.dromara.sfc.domain.dto.InfoReportGs3DTO;
import org.dromara.sfc.domain.dto.InfoReportGsDTO;
import org.dromara.sfc.domain.vo.SfcInfoReportVo;
import org.dromara.sfc.domain.vo.SfcInfoVo;
import org.dromara.sfc.query.InfoQuery;
import org.dromara.sfc.service.ISfcInfoService;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.idev.excel.util.MapUtils;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 报工扫描记录
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/scanReport")
public class SfcInfoReportController extends BaseController {

    private final ISfcInfoService sfcInfoService;

    /**
     * 查询报工扫描记录列表
     */
    @SaCheckPermission("information:scanReport:list")
    @GetMapping("/list")
    public TableDataInfo<SfcInfoVo> list(SfcInfoBo bo, PageQuery pageQuery) {
        return sfcInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报工扫描记录列表
     */
    @SaCheckPermission("information:scanReport:export")
    @Log(title = "报工扫描记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SfcInfoBo bo, HttpServletResponse response) {
        List<SfcInfoVo> list = sfcInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "报工扫描记录", SfcInfoVo.class, response);
    }

    /**
     * 获取报工扫描记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("information:scanReport:query")
    @GetMapping("/{id}")
    public R<SfcInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                @PathVariable Long id) {
        return R.ok(sfcInfoService.queryById(id));
    }

    /**
     * 新增报工扫描记录
     */
    @SaCheckPermission("information:scanReport:add")
    @Log(title = "报工扫描记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SfcInfoBo bo) {
        return toAjax(sfcInfoService.insertByBo(bo));
    }

    /**
     * 修改报工扫描记录
     */
    @SaCheckPermission("information:scanReport:edit")
    @Log(title = "报工扫描记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SfcInfoBo bo) {
        return toAjax(sfcInfoService.updateByBo(bo));
    }

    /**
     * 删除报工扫描记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("information:scanReport:remove")
    @Log(title = "报工扫描记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sfcInfoService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 分页查询报表
     *
     * @param sfcInfoQueryBo 参数
     * @param pageQuery      分页
     * @return IPage<SfcInfo>
     */
    @SaCheckPermission("information:scanReport:list")
    @GetMapping("/search")
    public TableDataInfo<SfcInfoVo> searchReport(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        return sfcInfoService.searchReport(sfcInfoQueryBo, pageQuery);
    }

    /**
     * 下载报表
     *
     * @param response       请求
     * @param sfcInfoQueryBo 参数
     */
    @Log(title = "报工扫描记录下载", businessType = BusinessType.EXPORT)
    @SaCheckPermission("information:scanReport:download")
    @PostMapping("/download")
    public void downReport(HttpServletResponse response, SfcInfoQueryBo sfcInfoQueryBo) {
        List<SfcInfoVo> list = sfcInfoService.searchReport(sfcInfoQueryBo);
        List<InfoReportDTO> dtoList = list.stream().map(en -> {
            InfoReportDTO dto = new InfoReportDTO();
            BeanUtil.copyProperties(en, dto);
            return dto;
        }).toList();
        ExcelUtil.exportExcel(dtoList, StrUtil.format("{}数据", "报工扫描记录"), InfoReportDTO.class, response);
    }


    /**
     * 报工扫描记录导出表格
     *
     * @param response
     * @param sfcInfoQueryBo
     */
    @Log(title = "报工扫描记录导出表格", businessType = BusinessType.EXPORT)
    // @SaCheckPermission("information:scanReport:report")
    @PostMapping("/report/export")
    public void exportReport(HttpServletResponse response, SfcInfoQueryBo sfcInfoQueryBo) throws IOException {
        List<SfcInfoVo> list = sfcInfoService.searchReport(sfcInfoQueryBo);

        AtomicLong i = new AtomicLong(1);
        List<SfcInfoReportVo> reportVoList = list.stream().map(en -> {
            SfcInfoReportVo vo = new SfcInfoReportVo();
            BeanUtils.copyProperties(en, vo);
            vo.setId(i.getAndIncrement());
            vo.setProType(en.getPType());
            vo.setProName(en.getPName());
            vo.setBeginDtm(en.getBDtm());
            vo.setEndDtm(en.getEDtm());
            vo.setMoSta(en.getMSta());
            return vo;
        }).toList();

        Map<String, Object> map = MapUtils.newHashMap();
        map.put("num", reportVoList.size());
        map.put("totalHours",
            reportVoList.stream().mapToDouble(en -> ObjectUtils.isEmpty(en.getBasGs()) ? 0.0 : en.getBasGs()).sum());
        map.put("scanHours",
            reportVoList.stream().mapToDouble(en -> ObjectUtils.isEmpty(en.getSjGs()) ? 0.0 : en.getSjGs()).sum());

        String fileName = URLEncoder.encode("装配生产管理系统(SFC)记录表", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        ExcelUtil.exportTemplate(CollUtil.newArrayList(map, reportVoList), fileName, "excel/装配生产管理系统(SFC)记录表.xlsx", response);
    }

    /**
     * 查找出有扫描进度，但是小于100%的，正在装配的记录
     */
    @Log(title = "查找出有扫描进度，但是小于100%的，正在装配的记录", businessType = BusinessType.EXPORT)
    @GetMapping("/noFinish")
    public R getNoFinish(InfoQuery infoQuery, Pageable pageable) {
        return R.ok(sfcInfoService.getNoFinish());
    }


    /**
     * 查询报表
     *
     * @return List<InfoReport2DTO>
     */
    @GetMapping("/report/search/all")
    public TableDataInfo<InfoReportGsDTO> searchReport1(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        List<SfcInfoVo> list = sfcInfoService.searchReport(sfcInfoQueryBo);
        List<InfoReportGsDTO> dtoList;
        // 判断入参是否有设备项次
        List<String> typeList = Arrays.asList(sfcInfoQueryBo.getSelectType1(), sfcInfoQueryBo.getSelectType2(),
            sfcInfoQueryBo.getSelectType3());
        if (typeList.contains("设备项次")) {
            // 以设备项次分组统计标准工时，扫描工时，未扫描工时
            dtoList = list.stream().collect(Collectors.groupingBy(en -> en.getSopBatno() + "," + en.getZpType()))
                .entrySet().stream().map(en -> {
                    InfoReportGsDTO dto = new InfoReportGsDTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    Map<String, List<SfcInfoVo>> pMap =
                        en.getValue().stream().filter(vo -> !ObjectUtils.isEmpty(vo.getPName()))
                            .collect(Collectors.groupingBy(vo -> vo.getPName() + vo.getBarCode()));
                    for (Map.Entry<String, List<SfcInfoVo>> entry : pMap.entrySet()) {
                        // 增加空值处理
                        Double basGs = ObjectUtils.isEmpty(entry.getValue().get(0).getBasGs()) ? 0.0
                            : entry.getValue().get(0).getBasGs();
                        dto.setBasGsAll(dto.getBasGsAll() + basGs);
                    }
                    double sumGs = en.getValue().stream()
                        .mapToDouble(in -> ObjectUtils.isEmpty(in.getSjGs()) ? 0.0 : in.getSjGs()).sum();
                    dto.setSmGsAll(NumberUtil.round(sumGs, 2).doubleValue());
                    dto.setSyGsAll(NumberUtil.round(dto.getBasGsAll() - dto.getSmGsAll(), 2).doubleValue());
                    return dto;
                }).toList();
        } else {
            // 以mo分组统计标准工时，扫描工时，未扫描工时
            dtoList = list.stream().collect(Collectors.groupingBy(en -> en.getMoNo() + "," + en.getZpType())).entrySet()
                .stream().map(en -> {
                    InfoReportGsDTO dto = new InfoReportGsDTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    Map<String, List<SfcInfoVo>> pMap =
                        en.getValue().stream().filter(vo -> !ObjectUtils.isEmpty(vo.getPName()))
                            .collect(Collectors.groupingBy(vo -> vo.getPName() + vo.getBarCode()));
                    for (Map.Entry<String, List<SfcInfoVo>> entry : pMap.entrySet()) {
                        // 增加空值处理
                        Double basGs = ObjectUtils.isEmpty(entry.getValue().get(0).getBasGs()) ? 0.0
                            : entry.getValue().get(0).getBasGs();
                        dto.setBasGsAll(dto.getBasGsAll() + basGs);
                    }
                    double sumGs = en.getValue().stream()
                        .mapToDouble(in -> ObjectUtils.isEmpty(in.getSjGs()) ? 0.0 : in.getSjGs()).sum();
                    dto.setSmGsAll(NumberUtil.round(sumGs, 2).doubleValue());
                    dto.setSyGsAll(NumberUtil.round(dto.getBasGsAll() - dto.getSmGsAll(), 2).doubleValue());
                    return dto;
                }).toList();
        }
        return TableDataInfo.build(dtoList);
    }


    /**
     * 查询报表
     *
     * @return List<InfoReport2DTO>
     */
    @GetMapping("/report/search/all2")
    public TableDataInfo<InfoReportGs2DTO> searchReport2(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        List<SfcInfoVo> list = sfcInfoService.searchReport(sfcInfoQueryBo);
        List<InfoReportGs2DTO> dtoList;
        // 判断入参是否有设备项次
        List<String> typeList = Arrays.asList(sfcInfoQueryBo.getSelectType1(), sfcInfoQueryBo.getSelectType2(),
            sfcInfoQueryBo.getSelectType3());
        if (typeList.contains("设备项次")) {
            dtoList = list.stream()
                .collect(Collectors.groupingBy(en -> en.getSopBatno() + "," + en.getZpType() + "," + en.getPName()))
                .entrySet().stream().map(en -> {
                    InfoReportGs2DTO dto = new InfoReportGs2DTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    dto.setPName(en.getKey().split(",")[2]);
                    // 取工序的标准工时
                    Map<String, List<SfcInfoVo>> pMap =
                        en.getValue().stream().filter(vo -> !ObjectUtils.isEmpty(vo.getBarCode()))
                            .collect(Collectors.groupingBy(SfcInfoVo::getBarCode));
                    for (Map.Entry<String, List<SfcInfoVo>> entry : pMap.entrySet()) {
                        // 增加空值处理
                        Double basGs = ObjectUtils.isEmpty(entry.getValue().get(0).getBasGs()) ? 0.0
                            : entry.getValue().get(0).getBasGs();
                        dto.setBasGsAll(dto.getBasGsAll() + basGs);
                    }
                    double smGs = en.getValue().stream()
                        .mapToDouble(in -> ObjectUtils.isEmpty(in.getSjGs()) ? 0.0 : in.getSjGs()).sum();
                    dto.setSmGsAll(NumberUtil.round(smGs, 2).doubleValue());
                    dto.setSyGsAll(NumberUtil.round(dto.getBasGsAll() - dto.getSmGsAll(), 2).doubleValue());
                    return dto;
                }).toList();
        } else {
            // 以mo+工序分组统计
            dtoList = list.stream()
                .collect(Collectors.groupingBy(en -> en.getMoNo() + "," + en.getZpType() + "," + en.getPName()))
                .entrySet().stream().map(en -> {
                    InfoReportGs2DTO dto = new InfoReportGs2DTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    dto.setPName(en.getKey().split(",")[2]);
                    // 取工序的标准工时
                    Map<String, List<SfcInfoVo>> pMap =
                        en.getValue().stream().filter(vo -> !ObjectUtils.isEmpty(vo.getBarCode()))
                            .collect(Collectors.groupingBy(SfcInfoVo::getBarCode));
                    for (Map.Entry<String, List<SfcInfoVo>> entry : pMap.entrySet()) {
                        // 增加空值处理
                        Double basGs = ObjectUtils.isEmpty(entry.getValue().get(0).getBasGs()) ? 0.0
                            : entry.getValue().get(0).getBasGs();
                        dto.setBasGsAll(dto.getBasGsAll() + basGs);
                    }
                    double smGs = en.getValue().stream()
                        .mapToDouble(in -> ObjectUtils.isEmpty(in.getSjGs()) ? 0.0 : in.getSjGs()).sum();
                    dto.setSmGsAll(NumberUtil.round(smGs, 2).doubleValue());
                    dto.setSyGsAll(NumberUtil.round(dto.getBasGsAll() - dto.getSmGsAll(), 2).doubleValue());
                    return dto;
                }).toList();
        }

        return TableDataInfo.build(dtoList);
    }

    /**
     * 查询报表
     *
     * @return List<InfoReport3DTO>
     */
    @GetMapping("/report/search/all3")
    public TableDataInfo<InfoReportGs3DTO> searchReport3(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery) {
        List<SfcInfoVo> list = sfcInfoService.searchReport(sfcInfoQueryBo);
        List<InfoReportGs3DTO> dtoList;
        // 判断入参是否有设备项次
        List<String> typeList = Arrays.asList(sfcInfoQueryBo.getSelectType1(), sfcInfoQueryBo.getSelectType2(),
            sfcInfoQueryBo.getSelectType3());
        if (typeList.contains("设备项次")) {
            dtoList = list.stream()
                .collect(Collectors.groupingBy(en -> en.getSopBatno() + "," + en.getZpType() + "," + en.getPName()))
                .entrySet().stream().flatMap(en -> en.getValue().stream().map(val -> {
                    InfoReportGs3DTO dto = new InfoReportGs3DTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    dto.setPName(en.getKey().split(",")[2]);
                    dto.setBDtm(val.getBDtm());
                    dto.setEDtm(val.getEDtm());
                    dto.setSjGs(
                        ObjectUtils.isEmpty(val.getSjGs()) ? 0.0 : NumberUtil.round(val.getSjGs(), 2).doubleValue());
                    dto.setEmpNo(val.getEmpNo());
                    dto.setEmpName(val.getEmpName());
                    return dto;
                })).toList();
        } else {
            // 以mo+工序分组统计
            dtoList = list.stream()
                .collect(Collectors.groupingBy(en -> en.getMoNo() + "," + en.getZpType() + "," + en.getPName()))
                .entrySet().stream().flatMap(en -> en.getValue().stream().map(val -> {
                    InfoReportGs3DTO dto = new InfoReportGs3DTO();
                    dto.setMoNo(en.getKey().split(",")[0]);
                    dto.setZpType(en.getKey().split(",")[1]);
                    dto.setPName(en.getKey().split(",")[2]);
                    dto.setBDtm(val.getBDtm());
                    dto.setEDtm(val.getEDtm());
                    dto.setSjGs(
                        ObjectUtils.isEmpty(val.getSjGs()) ? 0.0 : NumberUtil.round(val.getSjGs(), 2).doubleValue());
                    dto.setEmpNo(val.getEmpNo());
                    dto.setEmpName(val.getEmpName());
                    return dto;
                })).toList();
        }
        return TableDataInfo.build(dtoList);
    }
}
