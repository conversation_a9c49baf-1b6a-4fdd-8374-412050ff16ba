package org.dromara.sfc.service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcInfo;
import org.dromara.sfc.domain.bo.SfcInfoBo;
import org.dromara.sfc.domain.bo.SfcInfoQueryBo;
import org.dromara.sfc.domain.dto.InfoNoFinishDTO;
import org.dromara.sfc.domain.dto.InfoReportGs2DTO;
import org.dromara.sfc.domain.dto.InfoReportGs3DTO;
import org.dromara.sfc.domain.dto.InfoReportGsDTO;
import org.dromara.sfc.domain.vo.SfcInfoVo;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 报工扫描记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-26
 */
public interface ISfcInfoService extends IService<SfcInfo> {

    /**
     * 查询报工扫描记录
     *
     * @param id 主键
     * @return 报工扫描记录
     */
    SfcInfoVo queryById(Long id);

    /**
     * 分页查询报工扫描记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报工扫描记录分页列表
     */
    TableDataInfo<SfcInfoVo> queryPageList(SfcInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的报工扫描记录列表
     *
     * @param bo 查询条件
     * @return 报工扫描记录列表
     */
    List<SfcInfoVo> queryList(SfcInfoBo bo);

    /**
     * 新增报工扫描记录
     *
     * @param bo 报工扫描记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SfcInfoBo bo);

    /**
     * 修改报工扫描记录
     *
     * @param bo 报工扫描记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SfcInfoBo bo);

    /**
     * 校验并批量删除报工扫描记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 手动查询报工扫描记录
     *
     * @param sfcInfoQueryBo 查询条件
     * @param pageQuery 分页参数
     * @return 报工扫描记录
     */
    TableDataInfo<SfcInfoVo> searchReport(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery);


    /**
     * 手动查询报工扫描记录 不分页
     *
     * @param sfcInfoQueryBo 查询条件
     * @return 报工扫描记录
     */
    List<SfcInfoVo> searchReport(SfcInfoQueryBo sfcInfoQueryBo);


    List<InfoNoFinishDTO> getNoFinish();

    void checkData(List<SfcInfo> sfcInfos);

    List<SfcInfo> filter(List<SfcInfo> sfcInfos);

    List<SfcInfo> trans(List<SfcInfo> filterInfos);

    void uploadSap(List<SfcInfo> transInfos);

    /**
     * 分页查询工时统计报表
     *
     * @param sfcInfoQueryBo 查询条件
     * @param pageQuery 分页参数
     * @return 工时统计报表分页数据
     */
    TableDataInfo<InfoReportGsDTO> searchReportGs(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery);

    TableDataInfo<InfoReportGs2DTO> searchReportGs2(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery);

    TableDataInfo<InfoReportGs3DTO> searchReportGs3(SfcInfoQueryBo sfcInfoQueryBo, PageQuery pageQuery);

    List<SfcInfo> getInfosByBarcodeData(Map<String, String> params);

    R getInfoList(SfcInfo sfcInfo);
}
