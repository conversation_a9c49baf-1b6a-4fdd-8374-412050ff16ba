package org.dromara.sfc.controller;

import java.util.List;
import java.util.Map;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.WmasReport;
import org.dromara.sfc.domain.bo.WmasReportBo;
import org.dromara.sfc.domain.vo.WmasReportVo;
import org.dromara.sfc.service.IWmasReportService;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 样品清单维护
 * 前端访问路由地址为:/sfc/sample
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/sample")
public class WmasReportController extends BaseController {

    private final IWmasReportService wmasReportService;

    /**
     * 查询样品清单维护列表
     */
    @SaCheckPermission("sfc:sample:list")
    @GetMapping("/list")
    public TableDataInfo<WmasReportVo> list(WmasReportBo bo, PageQuery pageQuery) {
        return wmasReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出样品清单维护列表
     */
    @SaCheckPermission("sfc:sample:export")
    @Log(title = "样品清单维护", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WmasReportBo bo, HttpServletResponse response) {
        List<WmasReportVo> list = wmasReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "样品清单维护", WmasReportVo.class, response);
    }

    /**
     * 获取样品清单维护详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("sfc:sample:query")
    @GetMapping("/{id}")
    public R<WmasReportVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(wmasReportService.queryById(id));
    }

    /**
     * 新增样品清单维护
     */
    @SaCheckPermission("sfc:sample:add")
    @Log(title = "样品清单维护", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WmasReportBo bo) {
        return toAjax(wmasReportService.insertByBo(bo));
    }

    /**
     * 修改样品清单维护
     */
    @SaCheckPermission("sfc:sample:edit")
    @Log(title = "样品清单维护", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WmasReportBo bo) {
        return toAjax(wmasReportService.updateByBo(bo));
    }

    /**
     * 删除样品清单维护
     *
     * @param ids 主键串
     */
    @SaCheckPermission("sfc:sample:remove")
    @Log(title = "样品清单维护", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(wmasReportService.deleteWithValidByIds(List.of(ids), true));
    }

    @GetMapping("/wmas/report")
    public R report(@RequestParam Map<String, Object> params, PageQuery pageQuery) {
        return wmasReportService.search(params, pageQuery);
    }

    /**
     * 保存记录
     */
    @Log(title = "急件清单", businessType = BusinessType.INSERT)
    @PostMapping("/wmas/report/save")
    public R saveFromReport(@RequestBody List<WmasReport> wmasReportList) {
        return wmasReportService.saveWmasReports(wmasReportList);
    }

    @GetMapping("/wmas/report/gx/{moNo}")
    public R reportGx(@PathVariable String moNo, Pageable pageable) {
        return wmasReportService.getMoGx(moNo, pageable);
    }

    @GetMapping("/wmas/export/{moNo}")
    public R getPicUrl(@PathVariable String moNo) {
        return wmasReportService.getPicUrl(moNo);
    }
}
