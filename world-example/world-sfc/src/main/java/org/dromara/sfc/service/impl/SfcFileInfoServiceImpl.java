package org.dromara.sfc.service.impl;


import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import org.dromara.sfc.domain.SfcFileInfo;
import org.dromara.sfc.domain.SfcFileList;
import org.dromara.sfc.mapper.SfcFileInfoMapper;
import org.dromara.sfc.mapper.SfcFileListMapper;
import org.dromara.sfc.service.ISfcFileInfoService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SfcFileInfoServiceImpl extends ServiceImpl<SfcFileInfoMapper, SfcFileInfo> implements ISfcFileInfoService {
    @Resource
    private SfcFileInfoMapper sfcFileInfoMapper;

    @Resource
    private SfcFileListMapper sfcFileListMapper;

    /**
     * 获取文件清单
     *
     * @param mo   单号
     * @param type 类型
     * @return list
     */
    @Override
    public List<SfcFileInfo> getFileInfos(String mo, String type) {
        QueryWrapper<SfcFileInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mo_no", mo);
        // 已经存在的记录
        List<SfcFileInfo> fileInfos = sfcFileInfoMapper.selectList(queryWrapper);
        QueryWrapper<SfcFileList> fileListQueryWrapper = new QueryWrapper<>();
        if (type.equals("ZP")) {
            fileListQueryWrapper.eq("file_type", "ZP");
        }
        if (type.equals("IPQC")) {
            fileListQueryWrapper.eq("file_type", "IPQC")
                    .or()
                    .eq("file_type", "ZP");
        }
        if (type.equals("TS")) {
            fileListQueryWrapper.eq("file_type", "IPQC")
                    .or()
                    .eq("file_type", "ZP")
                    .or()
                    .eq("file_type", "TS");
        }
        List<SfcFileList> sfcFileLists = sfcFileListMapper.selectList(fileListQueryWrapper);
        List<SfcFileInfo> tableList = sfcFileLists.stream().map(en -> {
            SfcFileInfo fileInfo = new SfcFileInfo();
            fileInfo.setMoNo(mo);
            fileInfo.setFileItm(Convert.toStr(en.getFileItm()));
            fileInfo.setFileListName(en.getFileListName());
            fileInfo.setFlag("0");
            fileInfo.setChkDept(en.getFileType());
            fileInfo.setSysdt(new Date());
            return fileInfo;
        }).toList();

        // 使用Stream API将fileInfos转换为Set，以去重并提高查找效率
        Set<String> addedDepartments = fileInfos.stream()
                .map(SfcFileInfo::getFileListName)
                .collect(Collectors.toSet());

        // 使用Stream API对tableList进行过滤，去除已存在于addedDepartments中的元素
        List<SfcFileInfo> combinedList = tableList.stream()
                .filter(tableItem -> !addedDepartments.contains(tableItem.getFileListName()))
                .collect(Collectors.toList());

        // 将fileInfos中原有的元素添加到combinedList中
        combinedList.addAll(fileInfos);

        return combinedList;
    }
}
