package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.SapScanMaster;
import org.dromara.sfc.domain.bo.SapScanMasterBo;
import org.dromara.sfc.domain.vo.SapScanMasterVo;
import org.dromara.sfc.service.ISapScanMasterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

/**
 * sapMaster信息
 *
 * <AUTHOR>
 * @date 2025-02-20
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/master/scanMaster")
public class SapScanMasterController extends BaseController {

    private final ISapScanMasterService sapScanMasterService;


    /**
     * 查询sapMaster信息列表
     */
    @SaCheckPermission("master:scanMaster:list")
    @GetMapping("/list")
    public TableDataInfo<SapScanMasterVo> list(SapScanMasterBo bo, PageQuery pageQuery) {
        return sapScanMasterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出sapMaster信息列表
     */
    @SaCheckPermission("master:scanMaster:export")
    @Log(title = "sapMaster信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SapScanMasterBo bo, HttpServletResponse response) {
        List<SapScanMasterVo> list = sapScanMasterService.queryList(bo);
        ExcelUtil.exportExcel(list, "sapMaster信息", SapScanMasterVo.class, response);
    }

    /**
     * 获取sapMaster信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("master:scanMaster:query")
    @GetMapping("/{id}")
    public R<SapScanMasterVo> getInfo(@NotNull(message = "主键不能为空")
                                      @PathVariable Long id) {
        return R.ok(sapScanMasterService.queryById(id));
    }

    /**
     * 新增sapMaster信息
     */
    @SaCheckPermission("master:scanMaster:add")
    @Log(title = "sapMaster信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SapScanMasterBo bo) {
        return toAjax(sapScanMasterService.insertByBo(bo));
    }

    /**
     * 修改sapMaster信息
     */
    @SaCheckPermission("master:scanMaster:edit")
    @Log(title = "sapMaster信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SapScanMasterBo bo) {
        return toAjax(sapScanMasterService.updateByBo(bo));
    }

    /**
     * 删除sapMaster信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("master:scanMaster:remove")
    @Log(title = "sapMaster信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(sapScanMasterService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据工单号查找信息
     *
     * @param mo 工单号
     * @return 工单信息
     */
    @GetMapping("/one/{mo}")
    public R<SapScanMaster> getInfo(@NotNull(message = "MO号不能为空 ")
                                    @PathVariable String mo) throws SQLException {
        return R.ok(sapScanMasterService.getMasterByMo(mo));
    }
}
