package org.dromara.sfc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.sfc.domain.bo.SapScanMasterSnBo;
import org.dromara.sfc.domain.vo.SapScanMasterSnVo;
import org.dromara.sfc.service.ISapScanMasterSnService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * sap_scan_master_sn
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/information/scanMasterSn")
public class SapScanMasterSnController extends BaseController {

    private final ISapScanMasterSnService sapScanMasterSnService;

    /**
     * 查询sap_scan_master_sn列表
     */
    @SaCheckPermission("information:scanMasterSn:list")
    @GetMapping("/list")
    public TableDataInfo<SapScanMasterSnVo> list(SapScanMasterSnBo bo, PageQuery pageQuery) {
        return sapScanMasterSnService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出sap_scan_master_sn列表
     */
    @SaCheckPermission("information:scanMasterSn:export")
    @Log(title = "sap_scan_master_sn", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SapScanMasterSnBo bo, HttpServletResponse response) {
        List<SapScanMasterSnVo> list = sapScanMasterSnService.queryList(bo);
        ExcelUtil.exportExcel(list, "sap_scan_master_sn", SapScanMasterSnVo.class, response);
    }

    /**
     * 获取sap_scan_master_sn详细信息
     *
     * @param myRowId 主键
     */
    @SaCheckPermission("information:scanMasterSn:query")
    @GetMapping("/{myRowId}")
    public R<SapScanMasterSnVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long myRowId) {
        return R.ok(sapScanMasterSnService.queryById(myRowId));
    }

    /**
     * 新增sap_scan_master_sn
     */
    @SaCheckPermission("information:scanMasterSn:add")
    @Log(title = "sap_scan_master_sn", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SapScanMasterSnBo bo) {
        return toAjax(sapScanMasterSnService.insertByBo(bo));
    }

    /**
     * 修改sap_scan_master_sn
     */
    @SaCheckPermission("information:scanMasterSn:edit")
    @Log(title = "sap_scan_master_sn", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SapScanMasterSnBo bo) {
        return toAjax(sapScanMasterSnService.updateByBo(bo));
    }

    /**
     * 删除sap_scan_master_sn
     *
     * @param myRowIds 主键串
     */
    @SaCheckPermission("information:scanMasterSn:remove")
    @Log(title = "sap_scan_master_sn", businessType = BusinessType.DELETE)
    @DeleteMapping("/{myRowIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] myRowIds) {
        return toAjax(sapScanMasterSnService.deleteWithValidByIds(List.of(myRowIds), true));
    }
}
