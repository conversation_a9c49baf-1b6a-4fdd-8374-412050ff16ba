package org.dromara.sfc.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.sfc.domain.SfcFileList;
import org.dromara.sfc.domain.bo.SfcFileListBo;
import org.dromara.sfc.domain.vo.SfcFileListVo;
import org.dromara.sfc.mapper.SfcFileListMapper;
import org.dromara.sfc.service.ISfcFileListService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 文件清单设置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-10
 */
@RequiredArgsConstructor
@Service
public class SfcFileListServiceImpl implements ISfcFileListService {

    private final SfcFileListMapper baseMapper;

    /**
     * 查询文件清单设置
     *
     * @param id 主键
     * @return 文件清单设置
     */
    @Override
    public SfcFileListVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询文件清单设置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文件清单设置分页列表
     */
    @Override
    public TableDataInfo<SfcFileListVo> queryPageList(SfcFileListBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SfcFileList> lqw = buildQueryWrapper(bo);
        Page<SfcFileListVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的文件清单设置列表
     *
     * @param bo 查询条件
     * @return 文件清单设置列表
     */
    @Override
    public List<SfcFileListVo> queryList(SfcFileListBo bo) {
        LambdaQueryWrapper<SfcFileList> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SfcFileList> buildQueryWrapper(SfcFileListBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SfcFileList> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getFileItm() != null, SfcFileList::getFileItm, bo.getFileItm());
        lqw.like(StringUtils.isNotBlank(bo.getFileListName()), SfcFileList::getFileListName, bo.getFileListName());
        lqw.like(StringUtils.isNotBlank(bo.getFileType()), SfcFileList::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getFlag()), SfcFileList::getFlag, bo.getFlag());
        return lqw;
    }

    /**
     * 新增文件清单设置
     *
     * @param bo 文件清单设置
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SfcFileListBo bo) {
        SfcFileList add = MapstructUtils.convert(bo, SfcFileList.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改文件清单设置
     *
     * @param bo 文件清单设置
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SfcFileListBo bo) {
        SfcFileList update = MapstructUtils.convert(bo, SfcFileList.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SfcFileList entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除文件清单设置信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
