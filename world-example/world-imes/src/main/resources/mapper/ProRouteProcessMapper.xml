<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.mes.mapper.ProRouteProcessMapper">
    <resultMap type="org.dromara.mes.domain.ProRouteProcess" id="ProRouteProcessResult">
        <result property="recordId" column="record_id"/>
        <result property="routeId" column="route_id"/>
        <result property="processId" column="process_id"/>
        <result property="processCode" column="process_code"/>
        <result property="processName" column="process_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="nextProcessId" column="next_process_id"/>
        <result property="nextProcessCode" column="next_process_code"/>
        <result property="nextProcessName" column="next_process_name"/>
        <result property="linkType" column="link_type"/>
        <result property="defaultPreTime" column="default_pre_time"/>
        <result property="defaultSufTime" column="default_suf_time"/>
        <result property="colorCode" column="color_code"/>
        <result property="keyFlag" column="key_flag"></result>
        <result property="isCheck" column="is_check"></result>
        <result property="remark" column="remark"/>
        <result property="attr1" column="attr1"/>
        <result property="attr2" column="attr2"/>
        <result property="attr3" column="attr3"/>
        <result property="attr4" column="attr4"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectProRouteProcessVo">
        select record_id,
               route_id,
               process_id,
               process_code,
               process_name,
               order_num,
               next_process_id,
               next_process_code,
               next_process_name,
               link_type,
               default_pre_time,
               default_suf_time,
               color_code,
               key_flag,
               is_check,
               remark,
               attr1,
               attr2,
               attr3,
               attr4,
               create_by,
               create_time,
               update_by,
               update_time
        from pro_route_process
    </sql>
    <select id="selectByProcessIds" resultType="org.dromara.mes.domain.ProRouteProcess"
            resultMap="ProRouteProcessResult">
        select * from pro_route_process
        where process_id in
        <foreach collection="entity" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteByRouteId" parameterType="Long">
        delete
        from pro_route_process
        where route_id = #{routeId}
    </delete>

    <select id="checkOrderNumExists" resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
        where route_id = #{routeId} and order_num = #{orderNum} limit 1
    </select>

    <select id="checkProcessExists" resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
        where route_id = #{routeId} and process_id = #{processId} limit 1
    </select>

    <select id="checkUpdateFlagUnique" resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
        where route_id = #{routeId} and key_flag = 'Y' limit 1
    </select>


    <select id="findPreProcess" resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
        where route_id = #{routeId}
        <choose>
            <when test="orderNum != null ">
                and order_num &lt; #{orderNum}
            </when>
            <otherwise>
                AND order_num &lt; (
                SELECT order_num
                FROM pro_route_process
                WHERE route_id = #{routeId} limit 1
                )
            </otherwise>
        </choose>
        ORDER BY order_num DESC LIMIT 1
    </select>

    <select id="findNextProcess" resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
        where route_id = #{routeId}
        <choose>
            <when test="orderNum != null ">
                and order_num &gt; #{orderNum}
            </when>
            <otherwise>
                AND order_num &gt; (
                SELECT order_num
                FROM pro_route_process
                WHERE route_id = #{routeId} limit 1
                )
            </otherwise>
        </choose>
        ORDER BY order_num ASC LIMIT 1
    </select>

    <select id="selectByRouteId"  resultMap="ProRouteProcessResult">
        <include refid="selectProRouteProcessVo"/>
         where route_id = #{routeId}
    </select>

    <delete id="deleteProRouteProcessByRecordIds" parameterType="String">
        delete from pro_route_process where record_id in
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
</mapper>
