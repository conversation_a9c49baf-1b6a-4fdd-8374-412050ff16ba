<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.mes.mapper.PdfConvertRecordMapper">

    <resultMap id="PdfConvertRecordResult" type="org.dromara.mes.domain.PdfConvertRecord">
        <result property="id" column="id"/>
        <result property="moNumber" column="mo_number"/>
        <result property="noNumber" column="no_number"/>
        <result property="originalFileName" column="original_file_name"/>
        <result property="convertedFileName" column="converted_file_name"/>
        <result property="zipFilePath" column="zip_file_path"/>
        <result property="zipFileUrl" column="zip_file_url"/>
        <result property="status" column="status"/>
        <result property="errorMessage" column="error_message"/>
        <result property="fileSize" column="file_size"/>
        <result property="version" column="version"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

</mapper>