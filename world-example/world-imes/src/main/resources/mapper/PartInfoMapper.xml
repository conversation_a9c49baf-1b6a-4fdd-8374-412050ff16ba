<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.mes.mapper.PartInfoMapper">
    <resultMap type="org.dromara.mes.domain.vo.PartInfoVo" id="PartInfoResult">
    </resultMap>
    <!-- 优化后的分页查询 -->
    <select id="selectPartInfoList" resultMap="PartInfoResult">
        SELECT p.part_id,
        p.part_name,
        p.spec,
        p.high_1,
        p.width_1,
        p.length_1,
        p.diameter,
        p.draw_num,
        p.draw_num_version,
        p.outer_diameter,
        p.inside_diameter,
        p.brand_remarks,
        p.unit,
        p.plant_code,
        p.create_name,
        p.create_by,
        p.update_name,
        p.update_by,
        p.brand,
        p.part_type,
        p.old_part_code,
        p.part_group,
        p.density,
        p.meter_weight,
        f.factory as plant_code,
        f.safe_in_stock
        FROM part_info p
        INNER JOIN item_factory f ON p.part_id = f.part_id
        WHERE f.factory = #{factory}
        AND p.part_group != '108001'
        AND p.part_type in('Z001','Z002','Z003')
        AND (p.unable NOT IN ('Z1', 'Z2', 'Z3', 'Z4') OR p.unable IS NULL)
        <if test="partType != null and partType != ''">
            AND p.part_type = #{partType}
        </if>
        <if test="partId != null and partId != ''">
            AND p.part_id LIKE CONCAT('%', #{partId}, '%')
        </if>
        <if test="partName != null and partName != ''">
            AND p.part_name LIKE CONCAT('%', #{partName}, '%')
        </if>
        <if test="drawNum != null and drawNum != ''">
            AND p.draw_num LIKE CONCAT('%', #{drawNum}, '%')
        </if>
        <if test="drawNumVersion != null and drawNumVersion != ''">
            AND p.draw_num_version LIKE CONCAT('%', #{drawNumVersion}, '%')
        </if>
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
        ORDER BY p.id DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 优化后的总数查询 -->
    <select id="selectPartInfoCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM part_info p
        INNER JOIN item_factory f ON p.part_id = f.part_id
        WHERE f.factory = #{factory}
        AND p.part_group != '108001'
        AND p.part_type in('Z001','Z002','Z003')

        AND (p.unable NOT IN ('Z1', 'Z2', 'Z3', 'Z4') OR p.unable IS NULL)
        <if test="partType != null and partType != ''">
            AND p.part_type = #{partType}
        </if>

        <if test="partId != null and partId != ''">
            AND p.part_id LIKE CONCAT('%', #{partId}, '%')
        </if>
        <if test="partName != null and partName != ''">
            AND p.part_name LIKE CONCAT('%', #{partName}, '%')
        </if>
        <if test="drawNum != null and drawNum != ''">
            AND p.draw_num LIKE CONCAT('%', #{drawNum}, '%')
        </if>
        <if test="drawNumVersion != null and drawNumVersion != ''">
            AND p.draw_num_version LIKE CONCAT('%', #{drawNumVersion}, '%')
        </if>
        <if test="status != null and status != ''">
            AND p.status = #{status}
        </if>
    </select>

</mapper>
