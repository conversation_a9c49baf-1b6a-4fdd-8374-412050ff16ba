package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.mes.domain.CostCenter;

import java.io.Serial;
import java.io.Serializable;



/**
 * 售后成本中心视图对象 cost_center
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CostCenter.class)
public class CostCenterVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String id;

    /**
     * 成本中心代码
     */
    @ExcelProperty(value = "成本中心代码")
    private String costCenterCode;

    /**
     * 成本中心名称
     */
    @ExcelProperty(value = "成本中心名称")
    private String costCenterName;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private Double costCenterPrice;


}
