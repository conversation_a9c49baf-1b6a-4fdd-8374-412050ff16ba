package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 物料档案信息对象 part_info
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@TableName("part_info")
public class PartInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private String id;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人姓名
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 组织编码
     */
    private String sysOrgCode;

    /**
     * 公司编码
     */
    private String sysCompanyCode;

    /**
     * 长度(mm)
     */
    @TableField(value = "length_1")
    private Long length1;

    /**
     * 物料类型
     */
    private String partType;

    /**
     * 规格描述
     */
    private String spec;

    /**
     * 高度(mm)
     */
    @TableField(value = "high_1")
    private Long high1;

    /**
     * 计量单位
     */
    private String unit;

    /**
     * 物料组
     */
    private String partGroup;

    /**
     * 宽度(mm)
     */


    @TableField(value = "width_1")
    private Long width1;

    /**
     * 物料编码
     */
    private String partId;

    /**
     * 物料名称
     */
    private String partName;

    /**
     * 旧零件编码
     */
    private String oldPartCode;

    /**
     * 批次管理标识
     */
    private String batchManag;

    /**
     * 生产组
     */
    private String prodGroup;

    /**
     * 工厂代码
     */
    private String plantCode;

    /**
     * 品牌名称
     */
    private String brand;

    /**
     * 版本号
     */
    @Version
    private String version;

    /**
     * 图号
     */
    private String drawNum;

    /**
     * 供应商代码
     */
    private String compCode;

    /**
     * 是否禁用
     */
    private String unable;

    /**
     * 国产/进口
     */
    private String domesticOrImport;

    /**
     * 正品/替代品
     */
    private String authenticAlterna;

    /**
     * 密度(g/cm³)
     */
    private Long density;

    /**
     * 品牌备注
     */
    private String brandRemarks;

    /**
     * 图纸版本说明
     */
    private String drawNumVersion;

    /**
     * 客户代码
     */
    private String custCode;

    /**
     * 行业标准(Y/N)
     */
    private String industryStd;

    /**
     * 状态(0禁用/1启用)
     */
    private String status;

    /**
     * 米重(kg/m)
     */
    private Long meterWeight;

    /**
     * 图纸等级
     */
    private String drawingLevel;

    /**
     * 标准工艺
     */
    private String stdProcess;

    /**
     * 直径(mm)
     */
    private Long diameter;

    /**
     * 内径(mm)
     */
    private Long insideDiameter;

    /**
     * 外径(mm)
     */
    private Long outerDiameter;

    /**
     * 包装尺寸
     */
    private String boxSize;

    /**
     * SAP同步状态
     */
    private String sapSyncInfo;

    /**
     * 产线编号
     */
    private String lineNum;

    /**
     * 需求订单号
     */
    private String reqOrderNum;

    /**
     * SAP更新状态
     */
    private String sapUpdateInfo;

    /**
     * 原始物料号
     */
    private String originalItem;

    /**
     * 多标准管控
     */
    private String manyStandard;

    /**
     * 研发估值
     */
    private Long rdValuation;


    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 请求参数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private Map<String, Object> params = new HashMap<>();
}
