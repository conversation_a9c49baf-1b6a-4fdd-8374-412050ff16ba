package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.CostCenter;
import org.dromara.mes.domain.bo.CostCenterBo;
import org.dromara.mes.domain.vo.CostCenterVo;
import org.dromara.mes.mapper.CostCenterMapper;
import org.dromara.mes.service.ICostCenterService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 售后成本中心Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RequiredArgsConstructor
@Service
public class CostCenterServiceImpl implements ICostCenterService {

    private final CostCenterMapper baseMapper;

    /**
     * 查询售后成本中心
     *
     * @param id 主键
     * @return 售后成本中心
     */
    @Override
    public CostCenterVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询售后成本中心列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 售后成本中心分页列表
     */
    @Override
    public TableDataInfo<CostCenterVo> queryPageList(CostCenterBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CostCenter> lqw = buildQueryWrapper(bo);
        Page<CostCenterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的售后成本中心列表
     *
     * @param bo 查询条件
     * @return 售后成本中心列表
     */
    @Override
    public List<CostCenterVo> queryList(CostCenterBo bo) {
        LambdaQueryWrapper<CostCenter> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CostCenter> buildQueryWrapper(CostCenterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CostCenter> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(CostCenter::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getCostCenterCode()), CostCenter::getCostCenterCode, bo.getCostCenterCode());
        lqw.like(StringUtils.isNotBlank(bo.getCostCenterName()), CostCenter::getCostCenterName, bo.getCostCenterName());
        lqw.eq(bo.getCostCenterPrice() != null, CostCenter::getCostCenterPrice, bo.getCostCenterPrice());
        return lqw;
    }

    /**
     * 新增售后成本中心
     *
     * @param bo 售后成本中心
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CostCenterBo bo) {
        CostCenter add = MapstructUtils.convert(bo, CostCenter.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改售后成本中心
     *
     * @param bo 售后成本中心
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CostCenterBo bo) {
        CostCenter update = MapstructUtils.convert(bo, CostCenter.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CostCenter entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除售后成本中心信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
