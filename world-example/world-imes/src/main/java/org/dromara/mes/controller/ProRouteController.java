package org.dromara.mes.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.mes.domain.vo.ProRouteVo;
import org.dromara.mes.domain.bo.ProRouteBo;
import org.dromara.mes.service.IProRouteService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 工艺路线
 * 前端访问路由地址为:/mes/route
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/route")
public class ProRouteController extends BaseController {

    private final IProRouteService proRouteService;

    /**
     * 查询工艺路线列表
     */
    @SaCheckPermission("mes:route:list")
    @GetMapping("/list")
    public TableDataInfo<ProRouteVo> list(ProRouteBo bo, PageQuery pageQuery) {
        return proRouteService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工艺路线列表
     */
    @SaCheckPermission("mes:route:export")
    @Log(title = "工艺路线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProRouteBo bo, HttpServletResponse response) {
        List<ProRouteVo> list = proRouteService.queryList(bo);
        ExcelUtil.exportExcel(list, "工艺路线", ProRouteVo.class, response);
    }

    /**
     * 获取工艺路线详细信息
     *
     * @param routeId 主键
     */
    @SaCheckPermission("mes:route:query")
    @GetMapping("/{routeId}")
    public R<ProRouteVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("routeId") Long routeId) {
        return R.ok(proRouteService.queryById(routeId));
    }

    /**
     * 新增工艺路线
     */
    @SaCheckPermission("mes:route:add")
    @Log(title = "工艺路线", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProRouteBo bo) {
        return toAjax(proRouteService.insertByBo(bo));
    }

    /**
     * 修改工艺路线
     */
    @SaCheckPermission("mes:route:edit")
    @Log(title = "工艺路线", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProRouteBo bo) {
        return toAjax(proRouteService.updateByBo(bo));
    }

    /**
     * 删除工艺路线
     *
     * @param routeIds 主键串
     */
    @SaCheckPermission("mes:route:remove")
    @Log(title = "工艺路线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{routeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("routeIds") Long[] routeIds) {
        return toAjax(proRouteService.deleteWithValidByIds(List.of(routeIds), true));
    }
}
