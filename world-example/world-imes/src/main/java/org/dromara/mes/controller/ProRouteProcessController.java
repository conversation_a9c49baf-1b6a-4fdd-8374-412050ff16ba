package org.dromara.mes.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.mes.domain.vo.ProRouteProcessVo;
import org.dromara.mes.domain.bo.ProRouteProcessBo;
import org.dromara.mes.service.IProRouteProcessService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 工艺组成
 * 前端访问路由地址为:/mes/routeProcess
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/routeProcess")
public class ProRouteProcessController extends BaseController {

    private final IProRouteProcessService proRouteProcessService;

    /**
     * 查询工艺组成列表
     */
    @SaCheckPermission("mes:routeProcess:list")
    @GetMapping("/list")
    public TableDataInfo<ProRouteProcessVo> list(ProRouteProcessBo bo, PageQuery pageQuery) {
        return proRouteProcessService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工艺组成列表
     */
    @SaCheckPermission("mes:routeProcess:export")
    @Log(title = "工艺组成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProRouteProcessBo bo, HttpServletResponse response) {
        List<ProRouteProcessVo> list = proRouteProcessService.queryList(bo);
        ExcelUtil.exportExcel(list, "工艺组成", ProRouteProcessVo.class, response);
    }

    /**
     * 获取工艺组成详细信息
     *
     * @param recordId 主键
     */
    @SaCheckPermission("mes:routeProcess:query")
    @GetMapping("/{recordId}")
    public R<ProRouteProcessVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("recordId") Long recordId) {
        return R.ok(proRouteProcessService.queryById(recordId));
    }

    /**
     * 新增工艺组成
     */
    @SaCheckPermission("mes:routeProcess:add")
    @Log(title = "工艺组成", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProRouteProcessBo bo) {
        return toAjax(proRouteProcessService.insertByBo(bo));
    }

    /**
     * 修改工艺组成
     */
    @SaCheckPermission("mes:routeProcess:edit")
    @Log(title = "工艺组成", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProRouteProcessBo bo) {
        return toAjax(proRouteProcessService.updateByBo(bo));
    }

    /**
     * 删除工艺组成
     *
     * @param recordIds 主键串
     */
    @SaCheckPermission("mes:routeProcess:remove")
    @Log(title = "工艺组成", businessType = BusinessType.DELETE)
    @DeleteMapping("/{recordIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("recordIds") Long[] recordIds) {
        return toAjax(proRouteProcessService.deleteWithValidByIds(recordIds, true));
    }
}
