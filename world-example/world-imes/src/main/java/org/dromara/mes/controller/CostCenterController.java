package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.CostCenterBo;
import org.dromara.mes.domain.vo.CostCenterVo;
import org.dromara.mes.service.ICostCenterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 售后成本中心
 * 前端访问路由地址为:/mes/costCenter
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/costCenter")
public class CostCenterController extends BaseController {

    private final ICostCenterService costCenterService;

    /**
     * 查询售后成本中心列表
     */
    @SaCheckPermission("mes:costCenter:list")
    @GetMapping("/list")
    public TableDataInfo<CostCenterVo> list(CostCenterBo bo, PageQuery pageQuery) {
        return costCenterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出售后成本中心列表
     */
    @SaCheckPermission("mes:costCenter:export")
    @Log(title = "售后成本中心", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CostCenterBo bo, HttpServletResponse response) {
        List<CostCenterVo> list = costCenterService.queryList(bo);
        ExcelUtil.exportExcel(list, "售后成本中心", CostCenterVo.class, response);
    }

    /**
     * 获取售后成本中心详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:costCenter:query")
    @GetMapping("/{id}")
    public R<CostCenterVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") String id) {
        return R.ok(costCenterService.queryById(id));
    }

    /**
     * 新增售后成本中心
     */
    @SaCheckPermission("mes:costCenter:add")
    @Log(title = "售后成本中心", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CostCenterBo bo) {
        return toAjax(costCenterService.insertByBo(bo));
    }

    /**
     * 修改售后成本中心
     */
    @SaCheckPermission("mes:costCenter:edit")
    @Log(title = "售后成本中心", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CostCenterBo bo) {
        return toAjax(costCenterService.updateByBo(bo));
    }

    /**
     * 删除售后成本中心
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:costCenter:remove")
    @Log(title = "售后成本中心", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") String[] ids) {
        return toAjax(costCenterService.deleteWithValidByIds(List.of(ids), true));
    }
}
