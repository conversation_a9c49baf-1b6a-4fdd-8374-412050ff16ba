package org.dromara.mes.service;

import org.dromara.mes.domain.SupplierInfo;
import org.dromara.mes.domain.vo.SupplierInfoVo;
import org.dromara.mes.domain.bo.SupplierInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 供应商信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
public interface ISupplierInfoService {

    /**
     * 查询供应商信息
     *
     * @param id 主键
     * @return 供应商信息
     */
    SupplierInfoVo queryById(Long id);

    /**
     * 分页查询供应商信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 供应商信息分页列表
     */
    TableDataInfo<SupplierInfoVo> queryPageList(SupplierInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的供应商信息列表
     *
     * @param bo 查询条件
     * @return 供应商信息列表
     */
    List<SupplierInfoVo> queryList(SupplierInfoBo bo);

    /**
     * 新增供应商信息
     *
     * @param bo 供应商信息
     * @return 是否新增成功
     */
    Boolean insertByBo(SupplierInfoBo bo);

    /**
     * 修改供应商信息
     *
     * @param bo 供应商信息
     * @return 是否修改成功
     */
    Boolean updateByBo(SupplierInfoBo bo);

    /**
     * 校验并批量删除供应商信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 根据供应商编码查询供应商信息
     * @param supplierCode
     * @return java.lang.String
     */
    String selectSupplierByCode(String supplierCode);

    /**
     * 重置供应商信息数据缓存
     */
    void resetSupplierCache();

    /**
     * 检查
     * @param supplierInfo
     * @return boolean
     */
    boolean checkSupplierCodeUnique(SupplierInfoBo supplierInfo);

    /**
     * 异步同步处理sap供应商数据信息
     * @param tenantId
     * @return java.util.concurrent.CompletableFuture<java.lang.Boolean>
     */
    CompletableFuture<Boolean> syncSapToSupplierAsync(String tenantId);
    /**
     * 同步处理sap供应商数据信息
     * @param tenantId
     * @return java.util.concurrent.CompletableFuture<java.lang.Boolean>
     */
    Boolean syncSapToSupplier(String tenantId);
}
