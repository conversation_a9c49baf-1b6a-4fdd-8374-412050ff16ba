package org.dromara.mes.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.OfficeUser;
import org.dromara.mes.domain.bo.OfficeUserBo;
import org.dromara.mes.domain.vo.OfficeUserVo;

import java.util.Collection;
import java.util.List;

/**
 * 报工用户Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IOfficeUserService extends IService<OfficeUser> {

    /**
     * 查询报工用户
     *
     * @param id 主键
     * @return 报工用户
     */
    OfficeUserVo queryById(String id);

    /**
     * 分页查询报工用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报工用户分页列表
     */
    TableDataInfo<OfficeUserVo> queryPageList(OfficeUserBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的报工用户列表
     *
     * @param bo 查询条件
     * @return 报工用户列表
     */
    List<OfficeUserVo> queryList(OfficeUserBo bo);

    /**
     * 新增报工用户
     *
     * @param bo 报工用户
     * @return 是否新增成功
     */
    Boolean insertByBo(OfficeUserBo bo);

    /**
     * 修改报工用户
     *
     * @param bo 报工用户
     * @return 是否修改成功
     */
    Boolean updateByBo(OfficeUserBo bo);

    /**
     * 校验并批量删除报工用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    Boolean importData(List<OfficeUser> convert);
}
