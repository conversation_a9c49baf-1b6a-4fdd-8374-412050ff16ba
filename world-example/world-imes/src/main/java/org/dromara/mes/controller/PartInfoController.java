package org.dromara.mes.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.mes.domain.vo.PartInfoVo;
import org.dromara.mes.domain.bo.PartInfoBo;
import org.dromara.mes.service.IPartInfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.cache.annotation.Cacheable;

/**
 * 物料档案信息
 * 前端访问路由地址为:/mes/partInfo
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/partInfo")
public class PartInfoController extends BaseController {

    private final IPartInfoService partInfoService;

    /**
     * 查询物料档案信息列表
     */
    @SaCheckPermission("mes:partInfo:list")
    @GetMapping("/list")
    @Cacheable(value = "partInfoList", key = "#bo.toString() + '_' + #pageQuery.pageNum + '_' + #pageQuery.pageSize", condition = "#pageQuery.pageNum <= 10", unless = "#result == null")
    public TableDataInfo<PartInfoVo> list(PartInfoBo bo, PageQuery pageQuery) {
        return partInfoService.selectPartInfoList(bo, pageQuery);
    }

    /**
     * 获取物料档案信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:partInfo:query")
    @GetMapping("/{id}")
    public R<PartInfoVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") String id) {
        return R.ok(partInfoService.queryById(id));
    }

    /**
     * 新增物料档案信息
     */
    @SaCheckPermission("mes:partInfo:add")
    @Log(title = "物料档案信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PartInfoBo bo) {
        return toAjax(partInfoService.insertByBo(bo));
    }

    /**
     * 修改物料档案信息
     */
    @SaCheckPermission("mes:partInfo:edit")
    @Log(title = "物料档案信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PartInfoBo bo) {
        return toAjax(partInfoService.updateByBo(bo));
    }

    /**
     * 删除物料档案信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:partInfo:remove")
    @Log(title = "物料档案信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") String[] ids) {
        return toAjax(partInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
