package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.SystemConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.ProProcess;
import org.dromara.mes.domain.ProRouteProcess;
import org.dromara.mes.domain.bo.ProRouteProcessBo;
import org.dromara.mes.domain.vo.ProProcessVo;
import org.dromara.mes.domain.vo.ProRouteProcessVo;
import org.dromara.mes.mapper.ProProcessMapper;
import org.dromara.mes.mapper.ProRouteProcessMapper;
import org.dromara.mes.service.IProProcessService;
import org.dromara.mes.service.IProRouteProcessService;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工艺组成Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class ProRouteProcessServiceImpl implements IProRouteProcessService {

    private final ProRouteProcessMapper baseMapper;
    private final IProProcessService proProcessService;
    private final ProProcessMapper processMapper;

    /**
     * 查询工艺组成
     *
     * @param recordId 主键
     * @return 工艺组成
     */
    @Override
    public ProRouteProcessVo queryById(Long recordId) {
        return baseMapper.selectVoById(recordId);
    }

    /**
     * 分页查询工艺组成列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺组成分页列表
     */
    @Override
    public TableDataInfo<ProRouteProcessVo> queryPageList(ProRouteProcessBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProRouteProcess> lqw = buildQueryWrapper(bo);
        Page<ProRouteProcessVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工艺组成列表
     *
     * @param bo 查询条件
     * @return 工艺组成列表
     */
    @Override
    public List<ProRouteProcessVo> queryList(ProRouteProcessBo bo) {
        LambdaQueryWrapper<ProRouteProcess> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProRouteProcess> buildQueryWrapper(ProRouteProcessBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProRouteProcess> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProRouteProcess::getRecordId);
        lqw.eq(bo.getRouteId() != null, ProRouteProcess::getRouteId, bo.getRouteId());
        lqw.eq(bo.getProcessId() != null, ProRouteProcess::getProcessId, bo.getProcessId());
        lqw.like(StringUtils.isNotBlank(bo.getProcessCode()), ProRouteProcess::getProcessCode, bo.getProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), ProRouteProcess::getProcessName, bo.getProcessName());
        lqw.eq(bo.getOrderNum() != null, ProRouteProcess::getOrderNum, bo.getOrderNum());
        lqw.eq(bo.getNextProcessId() != null, ProRouteProcess::getNextProcessId, bo.getNextProcessId());
        lqw.like(StringUtils.isNotBlank(bo.getNextProcessCode()), ProRouteProcess::getNextProcessCode, bo.getNextProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getNextProcessName()), ProRouteProcess::getNextProcessName, bo.getNextProcessName());
        lqw.eq(StringUtils.isNotBlank(bo.getLinkType()), ProRouteProcess::getLinkType, bo.getLinkType());
        lqw.eq(bo.getDefaultPreTime() != null, ProRouteProcess::getDefaultPreTime, bo.getDefaultPreTime());
        lqw.eq(bo.getDefaultSufTime() != null, ProRouteProcess::getDefaultSufTime, bo.getDefaultSufTime());
        lqw.eq(StringUtils.isNotBlank(bo.getColorCode()), ProRouteProcess::getColorCode, bo.getColorCode());
        lqw.eq(StringUtils.isNotBlank(bo.getKeyFlag()), ProRouteProcess::getKeyFlag, bo.getKeyFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getIsCheck()), ProRouteProcess::getIsCheck, bo.getIsCheck());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr1()), ProRouteProcess::getAttr1, bo.getAttr1());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr2()), ProRouteProcess::getAttr2, bo.getAttr2());
        lqw.eq(bo.getAttr3() != null, ProRouteProcess::getAttr3, bo.getAttr3());
        lqw.eq(bo.getAttr4() != null, ProRouteProcess::getAttr4, bo.getAttr4());
        return lqw;
    }

    /**
     * 新增工艺组成
     *
     * @param bo 工艺组成
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProRouteProcessBo bo) {
        ProRouteProcess proRouteProcess = MapstructUtils.convert(bo, ProRouteProcess.class);
        validEntityBeforeSave(proRouteProcess);
        ProProcessVo proProcessVo = proProcessService.queryById(proRouteProcess.getProcessId());
        proRouteProcess.setProcessCode(proProcessVo.getProcessCode());
        proRouteProcess.setProcessName(proProcessVo.getProcessName());
        //更新上一个工序的nextProcess
        ProRouteProcess preProcess = findPreProcess(proRouteProcess);
        if (StringUtils.isNotNull(preProcess)) {
            preProcess.setNextProcessId(proRouteProcess.getProcessId());
            preProcess.setNextProcessCode(proRouteProcess.getProcessCode());
            preProcess.setNextProcessName(proRouteProcess.getProcessName());
            baseMapper.updateById(preProcess);
        }
        //设置当前工序的nextProcess
        ProRouteProcess nextProcess = findNextProcess(proRouteProcess);
        if (StringUtils.isNotNull(nextProcess)) {
            proRouteProcess.setNextProcessId(nextProcess.getProcessId());
            proRouteProcess.setNextProcessCode(nextProcess.getProcessCode());
            proRouteProcess.setNextProcessName(nextProcess.getProcessName());
        } else {
            proRouteProcess.setNextProcessId(0L);
            proRouteProcess.setNextProcessName("无");
        }
        boolean flag = baseMapper.insert(proRouteProcess) > 0;
        if (flag) {
            bo.setRecordId(proRouteProcess.getRecordId());
        }
        return flag;
    }

    @Override
    public ProRouteProcess findNextProcess(ProRouteProcess proRouteProcess) {
        return baseMapper.findNextProcess(proRouteProcess);
    }

    /**
     * 修改工艺组成
     *
     * @param bo 工艺组成
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProRouteProcessBo bo) {

        ProRouteProcess proRouteProcess = MapstructUtils.convert(bo, ProRouteProcess.class);
        validEntityBeforeSave(proRouteProcess);

        ProProcess process = processMapper.selectById(proRouteProcess.getProcessId());
        proRouteProcess.setProcessCode(process.getProcessCode());
        proRouteProcess.setProcessName(process.getProcessName());

        // 先更新当前数据
        baseMapper.updateById(proRouteProcess);
        // 查询所有工艺路线数据
        List<ProRouteProcess> list = baseMapper.selectByRouteId(proRouteProcess.getRouteId());
        if (list != null && list.size() > 0) {
            List<ProRouteProcess> collect = list.stream().sorted(Comparator.comparing(ProRouteProcess::getOrderNum)).collect(Collectors.toList());
            // 重新生成工序
            for (int i = 0; i < collect.size(); i++) {
                ProRouteProcess item = collect.get(i);
                if ((i + 1) >= collect.size()) {
                    item.setNextProcessId(0L);
                    item.setNextProcessName("无");
                } else {
                    ProRouteProcess next = collect.get(i + 1);
                    item.setNextProcessId(next.getProcessId());
                    item.setNextProcessCode(next.getProcessCode());
                    item.setNextProcessName(next.getProcessName());
                }
                baseMapper.updateById(item);
            }
        }

        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProRouteProcess entity) {
        //TODO 做一些数据校验,如唯一约束
        if (!checkOrderNumExists(entity)) {
            throw new ServiceException("序号已存在！");
        }
        if (!checkProcessExists(entity)) {
            throw new ServiceException("不能重复添加工序！");
        }
        if (SystemConstants.YES.equals(entity.getKeyFlag()) && !checkUpdateFlagUnique(entity)) {
            throw new ServiceException("当前工艺路线已经指定过关键工序");
        }
    }

    /**
     * 校验并批量删除工艺组成信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Long[] ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        ProRouteProcess proRouteProcess = baseMapper.selectById(ids[0]);
        Long routeId = proRouteProcess.getRouteId();
        boolean status = baseMapper.deleteByIds(List.of(ids)) > 0;
        List<ProRouteProcess> list = baseMapper.selectByRouteId(routeId);
        if (list != null && !list.isEmpty()) {
            List<ProRouteProcess> collect = list.stream().sorted(Comparator.comparing(ProRouteProcess::getOrderNum)).collect(Collectors.toList());
            // 重新生成工序
            for (int i = 0; i < collect.size(); i++) {
                ProRouteProcess item = collect.get(i);
                if ((i + 1) >= collect.size()) {
                    item.setNextProcessId(0L);
                    item.setNextProcessName("无");
                } else {
                    ProRouteProcess next = collect.get(i + 1);
                    item.setNextProcessId(next.getProcessId());
                    item.setNextProcessCode(next.getProcessCode());
                    item.setNextProcessName(next.getProcessName());
                }
                baseMapper.updateById(item);
            }
        }
        return status;
    }

    /**
     * 根据流程ID数组查询对应的流程路由处理记录
     *
     * @param processIds 流程ID数组
     * @return 查询到的流程路由处理记录列表
     */
    @Override
    public List<ProRouteProcess> selectByProcessIds(Long[] processIds) {
        return baseMapper.selectByProcessIds(processIds);
    }

    /**
     * 根据路由ID删除路由
     *
     * @param routeId 路由ID
     * @return 删除的条数
     */
    @Override
    public int deleteByRouteId(Long routeId) {
        return baseMapper.deleteByRouteId(routeId);
    }

    /**
     * 检查订单编号是否存在
     *
     * @param proRouteProcess ProRouteProcess对象，包含订单编号等相关信息
     * @return 如果订单编号存在，则返回false；否则返回true
     */
    @Override
    public boolean checkOrderNumExists(ProRouteProcess proRouteProcess) {
        ProRouteProcess process = baseMapper.checkOrderNumExists(proRouteProcess);
        Long recordId = proRouteProcess.getRecordId() == null ? -1L : proRouteProcess.getRecordId();
        if (StringUtils.isNotNull(process) && process.getRecordId().longValue() != recordId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 检查指定流程是否存在
     *
     * @param proRouteProcess ProRouteProcess 对象，包含流程的相关信息
     * @return 如果流程存在且流程ID与传入的记录ID一致，则返回false；否则返回true
     */
    @Override
    public boolean checkProcessExists(ProRouteProcess proRouteProcess) {
        ProRouteProcess process = baseMapper.checkProcessExists(proRouteProcess);
        Long recordId = proRouteProcess.getRecordId() == null ? -1L : proRouteProcess.getRecordId();
        if (StringUtils.isNotNull(process) && process.getRecordId().longValue() != recordId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 检查更新标志是否唯一
     *
     * @param proRouteProcess 业务路由处理对象
     * @return 如果更新标志唯一，则返回false；否则返回true
     */
    @Override
    public boolean checkUpdateFlagUnique(ProRouteProcess proRouteProcess) {
        ProRouteProcess process = baseMapper.checkUpdateFlagUnique(proRouteProcess);
        Long recordId = proRouteProcess.getRecordId() == null ? -1L : proRouteProcess.getRecordId();
        if (StringUtils.isNotNull(process) && process.getRecordId().longValue() != recordId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查找前置流程处理对象
     *
     * @param proRouteProcess 流程处理对象
     * @return 返回前置流程处理对象
     */
    @Override
    public ProRouteProcess findPreProcess(ProRouteProcess proRouteProcess) {
        return baseMapper.findPreProcess(proRouteProcess);
    }

}
