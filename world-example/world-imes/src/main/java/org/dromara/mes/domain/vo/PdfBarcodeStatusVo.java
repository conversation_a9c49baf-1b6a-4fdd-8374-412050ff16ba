package org.dromara.mes.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * PDF条形码处理状态响应对象
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Data
public class PdfBarcodeStatusVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 处理状态：processing-处理中，completed-已完成，failed-失败
     */
    private String status;

    /**
     * 状态描述
     */
    private String message;

    /**
     * 上传文件总数
     */
    private Integer totalCount;

    /**
     * 成功处理数量
     */
    private Integer successCount;

    /**
     * 处理失败数量
     */
    private Integer failCount;

    /**
     * 成功率
     */
    private Double successRate;

    /**
     * 任务开始时间
     */
    private LocalDateTime startTime;

    /**
     * 任务完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 处理耗时（毫秒）
     */
    private Long duration;

    /**
     * ZIP包大小（字节）
     */
    private Long zipSize;

    /**
     * ZIP包文件名
     */
    private String zipFileName;

    /**
     * 错误信息（如果处理失败）
     */
    private String errorMessage;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
