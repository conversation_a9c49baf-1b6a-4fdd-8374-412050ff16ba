package org.dromara.mes.service;


import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.bo.PdfConvertRecordBo;
import org.dromara.mes.domain.vo.PdfConvertRecordVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface IPdfConvertRecordService {

    /**
     * 查询PDF转换记录
     *
     * @param id 主键
     * @return PDF转换记录
     */
    PdfConvertRecordVo queryById(Long id);

    /**
     * 分页查询PDF转换记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return PDF转换记录分页列表
     */
    TableDataInfo<PdfConvertRecordVo> queryPageList(PdfConvertRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的PDF转换记录列表
     *
     * @param bo 查询条件
     * @return PDF转换记录列表
     */
    List<PdfConvertRecordVo> queryList(PdfConvertRecordBo bo);

    /**
     * 新增PDF转换记录
     *
     * @param bo PDF转换记录
     * @return 是否新增成功
     */
    Boolean insertByBo(PdfConvertRecordBo bo);

    /**
     * 修改PDF转换记录
     *
     * @param bo PDF转换记录
     * @return 是否修改成功
     */
    Boolean updateByBo(PdfConvertRecordBo bo);

    /**
     * 校验并批量删除PDF转换记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    boolean scalePdf(String inputPath, String outputPath, float scale, boolean drawBorder);

    /**
     * 通过MO号或NO号下载并进行PDF缩放处理
     *
     * @param moNumber   MO号，可为空
     * @param noNumber   NO号，可为空
     * @param outputPath 输出目录，空则使用默认目录
     * @return 处理结果，包含 success、message、finalPdf、fileName、fileSize 等键
     */
    Map<String, Object> downloadAndScaleByMoOrNo(String moNumber, String noNumber, String outputPath);

    /**
     * 处理单个文件（下载、缩放）
     *
     * @param identifier 标识符（MO号或NO号）
     * @param type       类型（MO或NO）
     * @param scale      缩放比例
     * @return 处理结果
     */
    Map<String, Object> processFile(String identifier, String type, float scale);

    /**
     * 通过MO号下载文件
     *
     * @param moNumber   MO号
     * @param outputPath 输出路径
     * @param no         NO号，可选
     * @return 下载结果
     */
    Map<String, Object> downloadFileByMoNumber(String moNumber, String outputPath, String no);

    /**
     * 使用iText缩放PDF
     *
     * @param inputPath  输入文件路径
     * @param outputPath 输出文件路径
     * @param scale      缩放比例
     * @param drawBorder 是否绘制边框
     * @return 是否成功
     */
    boolean scalePdfWithIText(String inputPath, String outputPath, float scale, boolean drawBorder);

    /**
     * 清理参数
     *
     * @param param 参数
     * @return 清理后的参数
     */
    String cleanParam(String param);

    /**
     * 将处理好的文件添加到ZIP
     */
    void addFileToZip(Map<String, Object> fileResult, java.util.zip.ZipOutputStream zipOut, java.util.Set<String> addedFiles);
}
