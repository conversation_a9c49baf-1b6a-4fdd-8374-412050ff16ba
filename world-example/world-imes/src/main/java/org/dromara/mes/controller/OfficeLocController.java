package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.OfficeLoc;
import org.dromara.mes.domain.bo.OfficeLocBo;
import org.dromara.mes.domain.vo.OfficeLocVo;
import org.dromara.mes.service.IOfficeLocService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 工作地点组
 * 前端访问路由地址为:/mes/officeLoc
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/officeLoc")
public class OfficeLocController extends BaseController {

    private final IOfficeLocService officeLocService;

    /**
     * 查询工作地点组列表
     */
    @SaCheckPermission("mes:officeLoc:list")
    @GetMapping("/list")
    public TableDataInfo<OfficeLocVo> list(OfficeLocBo bo, PageQuery pageQuery) {
        return officeLocService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工作地点组列表
     */
    @SaCheckPermission("mes:officeLoc:export")
    @Log(title = "工作地点组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OfficeLocBo bo, HttpServletResponse response) {
        List<OfficeLocVo> list = officeLocService.queryList(bo);
        ExcelUtil.exportExcel(list, "工作地点组", OfficeLocVo.class, response);
    }

    /**
     * 导入工作地点组列表
     */
    @SaCheckPermission("mes:officeLoc:import")
    @Log(title = "工作地点组", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        // 导入方法
        List<OfficeLocVo> officeLocVos = ExcelUtil.importExcel(file.getInputStream(), OfficeLocVo.class);
        officeLocService.saveBatch(MapstructUtils.convert(officeLocVos, OfficeLoc.class));
        return R.ok();
    }

    /**
     * 获取工作地点组详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:officeLoc:query")
    @GetMapping("/{id}")
    public R<OfficeLocVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable("id") String id) {
        return R.ok(officeLocService.queryById(id));
    }

    /**
     * 新增工作地点组
     */
    @SaCheckPermission("mes:officeLoc:add")
    @Log(title = "工作地点组", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OfficeLocBo bo) {
        return toAjax(officeLocService.insertByBo(bo));
    }

    /**
     * 修改工作地点组
     */
    @SaCheckPermission("mes:officeLoc:edit")
    @Log(title = "工作地点组", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OfficeLocBo bo) {
        return toAjax(officeLocService.updateByBo(bo));
    }

    /**
     * 删除工作地点组
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:officeLoc:remove")
    @Log(title = "工作地点组", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") String[] ids) {
        return toAjax(officeLocService.deleteWithValidByIds(List.of(ids), true));
    }
}
