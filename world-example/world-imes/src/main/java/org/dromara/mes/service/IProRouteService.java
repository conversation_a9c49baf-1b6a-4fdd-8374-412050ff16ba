package org.dromara.mes.service;

import org.dromara.mes.domain.ProRoute;
import org.dromara.mes.domain.vo.ProRouteVo;
import org.dromara.mes.domain.bo.ProRouteBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 工艺路线Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface IProRouteService {

    /**
     * 查询工艺路线
     *
     * @param routeId 主键
     * @return 工艺路线
     */
    ProRouteVo queryById(Long routeId);

    /**
     * 分页查询工艺路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线分页列表
     */
    TableDataInfo<ProRouteVo> queryPageList(ProRouteBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工艺路线列表
     *
     * @param bo 查询条件
     * @return 工艺路线列表
     */
    List<ProRouteVo> queryList(ProRouteBo bo);

    /**
     * 新增工艺路线
     *
     * @param bo 工艺路线
     * @return 是否新增成功
     */
    Boolean insertByBo(ProRouteBo bo);

    /**
     * 修改工艺路线
     *
     * @param bo 工艺路线
     * @return 是否修改成功
     */
    Boolean updateByBo(ProRouteBo bo);

    /**
     * 校验并批量删除工艺路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
