package org.dromara.mes.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * PDF转换记录对象 pdf_convert_record
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pdf_convert_record")
public class PdfConvertRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * MO号
     */
    private String moNumber;

    /**
     * NO号
     */
    private String noNumber;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 转换后文件名
     */
    private String convertedFileName;

    /**
     * ZIP文件路径（MinIO）
     */
    private String zipFilePath;

    /**
     * ZIP文件URL
     */
    private String zipFileUrl;

    /**
     * 处理状态：0-处理中，1-成功，2-失败
     */
    private Long status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 版本
     */
    @Version
    private Long version;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;


}
