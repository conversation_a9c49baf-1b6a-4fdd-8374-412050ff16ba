package org.dromara.mes.domain.vo;

import org.dromara.mes.domain.SupplierInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 供应商信息视图对象 supplier_info
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SupplierInfo.class)
public class SupplierInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商编号
     */
    @ExcelProperty(value = "供应商编号")
    private String supplierCode;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    private String contactPerson;

    /**
     * 联系人邮箱
     */
    @ExcelProperty(value = "联系人邮箱")
    private String contactEmail;

    /**
     * 联系人电话
     */
    @ExcelProperty(value = "联系人电话")
    private String contactPhone;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_common_status")
    private String status;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

}
