package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.GoodsDeclarationBo;
import org.dromara.mes.domain.vo.GoodsDeclarationVo;
import org.dromara.mes.listener.ExcelGoodsDataListener;
import org.dromara.mes.service.IGoodsDeclarationService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 备件备案信息
 * 前端访问路由地址为:/mes/declaration
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/declaration")
public class GoodsDeclarationController extends BaseController {

    private final IGoodsDeclarationService goodsDeclarationService;

    /**
     * 查询备件备案信息列表
     */
    @SaCheckPermission("mes:declaration:list")
    @GetMapping("/list")
    public TableDataInfo<GoodsDeclarationVo> list(GoodsDeclarationBo bo, PageQuery pageQuery) {
        return goodsDeclarationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出备件备案信息列表
     */
    @SaCheckPermission("mes:declaration:export")
    @Log(title = "备件备案信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @RepeatSubmit(interval = 30000)
    public void export(GoodsDeclarationBo bo, HttpServletResponse response) {
        List<GoodsDeclarationVo> list = goodsDeclarationService.exportData(bo);
        Map<String, Object> multiListMap = new HashMap<>();
        multiListMap.put("title", "备件备案信息");
        multiListMap.put("data", list);
        ExcelUtil.exportTemplate(CollUtil.newArrayList(multiListMap, list), "备件备案信息.xlsx", "excel/备件备案信息.xlsx", response);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    @RepeatSubmit(interval = 30000)
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil.exportTemplate(CollUtil.newArrayList(new HashMap<>(), new ArrayList<>()), "备件备案信息.xlsx", "excel/备件备案信息.xlsx", response);
    }

    /**
     * 导入备件备案信息数据
     *
     * @param file 导入文件
     */
    @PostMapping(value = "/importData", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Log(title = "导入备件备案数据", businessType = BusinessType.IMPORT)
    @RepeatSubmit()
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws IOException {
        ExcelGoodsDataListener excelDataListener = new ExcelGoodsDataListener();
        // 处理数据
        ExcelResult<GoodsDeclarationVo> excelResult = ExcelUtil.importExcel(file.getInputStream(),
            GoodsDeclarationVo.class, excelDataListener);
        List<GoodsDeclarationVo> volist = excelResult.getList();
        log.info("导入备件备案数据条数: {}", volist.size());
        // 处理导入数据
        return goodsDeclarationService.processImportData(file, volist);
    }

    /**
     * 获取备件备案信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:declaration:query")
    @GetMapping("/{id}")
    public R<GoodsDeclarationVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(goodsDeclarationService.queryById(id));
    }

    /**
     * 新增备件备案信息
     */
    @SaCheckPermission("mes:declaration:add")
    @Log(title = "备件备案信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody GoodsDeclarationBo bo) {
        return toAjax(goodsDeclarationService.insertByBo(bo));
    }

    /**
     * 修改备件备案信息
     */
    @SaCheckPermission("mes:declaration:edit")
    @Log(title = "备件备案信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody GoodsDeclarationBo bo) {
        return toAjax(goodsDeclarationService.updateByBo(bo));
    }

    /**
     * 删除备件备案信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:declaration:remove")
    @Log(title = "备件备案信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(goodsDeclarationService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 提交审批
     *
     * @param ids 主键
     */
    @SaCheckPermission("mes:declaration:submit")
    @Log(title = "备案信息提交", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/submit/{ids}")
    public R<Void> submitForApproval(@NotNull(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(goodsDeclarationService.submitForApproval(List.of(ids)));
    }

    /**
     * 审批退回
     *
     * @param ids              主键
     * @param reason 审批意见
     */
    @SaCheckPermission("mes:declaration:reject")
    @Log(title = "备案信息退回", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/reject/{ids}")
    public R<Void> reject(@NotNull(message = "主键不能为空") @PathVariable("ids") Long[] ids,
                          @RequestParam(value = "reason", required = false) String reason) {
        return toAjax(goodsDeclarationService.reject(List.of(ids), reason));
    }

    /**
     * 归档
     *
     * @param ids 主键
     */
    @SaCheckPermission("mes:declaration:archive")
    @Log(title = "备案信息归档", businessType = BusinessType.UPDATE)
    @RepeatSubmit
    @PutMapping("/archive/{ids}")
    public R<Void> archive(@NotNull(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(goodsDeclarationService.archive(List.of(ids)));
    }


    /**
     * 查询待审批列表
     */
    @SaCheckPermission("mes:declaration:pending")
    @GetMapping("/pending")
    public TableDataInfo<GoodsDeclarationVo> pendingList(GoodsDeclarationBo bo, PageQuery pageQuery) {
        return goodsDeclarationService.queryPendingApprovalList(bo, pageQuery);
    }


    /**
     * 修改备件备案信息
     */
    @SaCheckPermission("mes:declaration:editDeclaration")
    @Log(title = "备件备案信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/editDeclaration")
    public R<Void> editDeclaration(@Validated(EditGroup.class) @RequestBody GoodsDeclarationBo bo) {
        return toAjax(goodsDeclarationService.updateByDeclaration(bo));
    }
}
