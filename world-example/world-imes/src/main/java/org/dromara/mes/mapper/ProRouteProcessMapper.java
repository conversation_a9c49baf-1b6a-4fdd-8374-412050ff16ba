package org.dromara.mes.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.mes.domain.ProRouteProcess;
import org.dromara.mes.domain.bo.ProRouteProcessBo;
import org.dromara.mes.domain.vo.ProRouteProcessVo;

import java.util.List;

/**
 * 工艺组成Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface ProRouteProcessMapper extends BaseMapperPlus<ProRouteProcess, ProRouteProcessVo> {

    List<ProRouteProcess> selectByProcessIds(@Param("entity") Long[] processIds);

    /**
     * 根据工艺路线ID删除所有工序配置
     *
     * @param routeId
     * @return
     */
    int deleteByRouteId(Long routeId);

    ProRouteProcess checkOrderNumExists(ProRouteProcess proRouteProcess);

    ProRouteProcess checkProcessExists(ProRouteProcess proRouteProcess);

    ProRouteProcess checkUpdateFlagUnique(ProRouteProcess proRouteProcess);

    ProRouteProcess findPreProcess(ProRouteProcess proRouteProcess);

    ProRouteProcess findNextProcess(ProRouteProcess proRouteProcess);

    List<ProRouteProcess> selectByRouteId(Long routeId);

    /**
     * 批量删除工艺组成
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteProRouteProcessByRecordIds(Long[] ids);
}
