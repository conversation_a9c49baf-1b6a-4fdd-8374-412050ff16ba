package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.BgAction;
import org.dromara.mes.domain.bo.BgActionBo;
import org.dromara.mes.domain.vo.BgActionVo;
import org.dromara.mes.mapper.BgActionMapper;
import org.dromara.mes.service.IBgActionService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 报工动作Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RequiredArgsConstructor
@Service
public class BgActionServiceImpl implements IBgActionService {

    private final BgActionMapper baseMapper;

    /**
     * 查询报工动作
     *
     * @param id 主键
     * @return 报工动作
     */
    @Override
    public BgActionVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询报工动作列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报工动作分页列表
     */
    @Override
    public TableDataInfo<BgActionVo> queryPageList(BgActionBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<BgAction> lqw = buildQueryWrapper(bo);
        Page<BgActionVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的报工动作列表
     *
     * @param bo 查询条件
     * @return 报工动作列表
     */
    @Override
    public List<BgActionVo> queryList(BgActionBo bo) {
        LambdaQueryWrapper<BgAction> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<BgAction> buildQueryWrapper(BgActionBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<BgAction> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(BgAction::getId);
        lqw.eq(bo.getSortNum() != null, BgAction::getSortNum, bo.getSortNum());
        lqw.eq(StringUtils.isNotBlank(bo.getActionCode()), BgAction::getActionCode, bo.getActionCode());
        lqw.eq(StringUtils.isNotBlank(bo.getActionName()), BgAction::getActionName, bo.getActionName());
        lqw.eq(StringUtils.isNotBlank(bo.getDeptCode()), BgAction::getDeptCode, bo.getDeptCode());
        lqw.like(StringUtils.isNotBlank(bo.getDeptName()), BgAction::getDeptName, bo.getDeptName());
        lqw.eq(StringUtils.isNotBlank(bo.getIsUse()), BgAction::getIsUse, bo.getIsUse());
        lqw.eq(StringUtils.isNotBlank(bo.getIsSap()), BgAction::getIsSap, bo.getIsSap());
        lqw.eq(StringUtils.isNotBlank(bo.getIsMaster()), BgAction::getIsMaster, bo.getIsMaster());
        lqw.eq(StringUtils.isNotBlank(bo.getNeedMachin()), BgAction::getNeedMachin, bo.getNeedMachin());
        lqw.eq(StringUtils.isNotBlank(bo.getNeedClose()), BgAction::getNeedClose, bo.getNeedClose());
        lqw.eq(StringUtils.isNotBlank(bo.getIsOut()), BgAction::getIsOut, bo.getIsOut());
        lqw.eq(StringUtils.isNotBlank(bo.getIsMult()), BgAction::getIsMult, bo.getIsMult());
        return lqw;
    }

    /**
     * 新增报工动作
     *
     * @param bo 报工动作
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(BgActionBo bo) {
        BgAction add = MapstructUtils.convert(bo, BgAction.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改报工动作
     *
     * @param bo 报工动作
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(BgActionBo bo) {
        BgAction update = MapstructUtils.convert(bo, BgAction.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(BgAction entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除报工动作信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
