package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 工作地点组对象 office_loc
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("office_loc")
public class OfficeLoc extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private String id;

    /**
     * 办事处代码
     */
    private String officeCode;

    /**
     * 描述
     */
    private String officeDesc;

    /**
     * 工号
     */
    private String workNum;

    /**
     * 主管
     */
    private String officeSupervisor;

    /**
     * 工作中心
     */
    private String workCenter;

    /**
     * 办事处员工数
     */
    private Integer totalUser;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 所属公司
     */
    private String sysCompanyCode;

    /**
     * 工时单价
     */
    private Long price;

    /**
     *
     */
    private String userId;


}
