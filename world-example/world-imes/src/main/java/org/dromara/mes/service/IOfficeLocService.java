package org.dromara.mes.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.OfficeLoc;
import org.dromara.mes.domain.bo.OfficeLocBo;
import org.dromara.mes.domain.vo.OfficeLocVo;

import java.util.Collection;
import java.util.List;

/**
 * 工作地点组Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IOfficeLocService extends IService<OfficeLoc> {

    /**
     * 查询工作地点组
     *
     * @param id 主键
     * @return 工作地点组
     */
    OfficeLocVo queryById(String id);

    /**
     * 分页查询工作地点组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工作地点组分页列表
     */
    TableDataInfo<OfficeLocVo> queryPageList(OfficeLocBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工作地点组列表
     *
     * @param bo 查询条件
     * @return 工作地点组列表
     */
    List<OfficeLocVo> queryList(OfficeLocBo bo);

    /**
     * 新增工作地点组
     *
     * @param bo 工作地点组
     * @return 是否新增成功
     */
    Boolean insertByBo(OfficeLocBo bo);

    /**
     * 修改工作地点组
     *
     * @param bo 工作地点组
     * @return 是否修改成功
     */
    Boolean updateByBo(OfficeLocBo bo);

    /**
     * 校验并批量删除工作地点组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
