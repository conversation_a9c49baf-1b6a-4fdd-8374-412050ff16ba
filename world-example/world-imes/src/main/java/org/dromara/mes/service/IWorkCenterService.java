package org.dromara.mes.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.bo.WorkCenterBo;
import org.dromara.mes.domain.vo.WorkCenterVo;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 工作中心Service接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IWorkCenterService {

    /**
     * 查询工作中心
     *
     * @param id 主键
     * @return 工作中心
     */
    WorkCenterVo queryById(Long id);

    /**
     * 分页查询工作中心列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工作中心分页列表
     */
    TableDataInfo<WorkCenterVo> queryPageList(WorkCenterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工作中心列表
     *
     * @param bo 查询条件
     * @return 工作中心列表
     */
    List<WorkCenterVo> queryList(WorkCenterBo bo);

    /**
     * 新增工作中心
     *
     * @param bo 工作中心
     * @return 是否新增成功
     */
    Boolean insertByBo(WorkCenterBo bo);

    /**
     * 修改工作中心
     *
     * @param bo 工作中心
     * @return 是否修改成功
     */
    Boolean updateByBo(WorkCenterBo bo);

    /**
     * 校验并批量删除工作中心信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 同步SAP到工作中心
     * @param tenantId 租户ID
     * @return java.lang.Boolean
     */
    Boolean syncSapToWorkCenter(String tenantId);

    /**
     * 异步同步SAP到工作中心
     *
     * @param tenantId 租户ID
     * @return java.lang.Boolean
     */
    CompletableFuture<Boolean> syncSapToWorkCenterAsync(String tenantId);
}
