package org.dromara.mes.domain.bo;

import org.apache.dubbo.common.logger.FluentLogger;
import org.dromara.mes.domain.WorkCenter;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 工作中心业务对象 work_center
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = WorkCenter.class, reverseConvertGenerate = false)
public class WorkCenterBo extends BaseEntity {

    /**
     * 主键ID
     */
    @NotNull(message = "主键ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 代号
     */
    @NotBlank(message = "代号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String code;

    /**
     * 说明
     */
    @NotBlank(message = "说明不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String type;

    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 人工费用
     */
    private Long laborCost;

    /**
     * 制造费用
     */
    private Long manufacturingCost;

    /**
     * 是否委外
     */
    private String isOutsourced;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 部门
     */
    private String completionDepartment;

    /**
     * 备注信息
     */
    private String remarks;


}
