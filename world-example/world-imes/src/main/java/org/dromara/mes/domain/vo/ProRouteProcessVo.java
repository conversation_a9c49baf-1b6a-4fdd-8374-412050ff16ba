package org.dromara.mes.domain.vo;

import org.dromara.mes.domain.ProRouteProcess;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 工艺组成视图对象 pro_route_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProRouteProcess.class)
public class ProRouteProcessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long recordId;

    /**
     * 工艺路线ID
     */
    @ExcelProperty(value = "工艺路线ID")
    private Long routeId;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long processId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long orderNum;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long nextProcessId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String nextProcessCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String nextProcessName;

    /**
     * 与下一道工序关系
     */
    @ExcelProperty(value = "与下一道工序关系")
    private String linkType;

    /**
     * 批量等待时间
     */
    @ExcelProperty(value = "批量等待时间")
    private Long defaultPreTime;

    /**
     * 等待时间
     */
    @ExcelProperty(value = "等待时间")
    private Long defaultSufTime;

    /**
     * 急单时间
     */
    @ExcelProperty(value = "急单时间")
    private Long urgWaitTime;

    /**
     * 甘特图显示颜色
     */
    @ExcelProperty(value = "甘特图显示颜色")
    private String colorCode;

    /**
     * 关键工序
     */
    @ExcelProperty(value = "关键工序")
    private String keyFlag;

    /**
     * 是否检验
     */
    @ExcelProperty(value = "是否检验")
    private String isCheck;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 预留字段1
     */
    @ExcelProperty(value = "预留字段1")
    private String attr1;

    /**
     * 预留字段2
     */
    @ExcelProperty(value = "预留字段2")
    private String attr2;

    /**
     * 预留字段3
     */
    @ExcelProperty(value = "预留字段3")
    private Long attr3;

    /**
     * 预留字段4
     */
    @ExcelProperty(value = "预留字段4")
    private Long attr4;


}
