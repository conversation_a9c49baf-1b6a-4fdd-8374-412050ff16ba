package org.dromara.mes.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.mes.domain.GoodsDeclaration;

import java.util.Date;

/**
 * 备件备案信息业务对象 goods_declaration
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = GoodsDeclaration.class, reverseConvertGenerate = false)
public class GoodsDeclarationBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;


    /**
     * 单台净重（Net weigh）
     */
    private Double netWeight;

    /**
     * 货物图片（picture）
     */
    private String goodsPicture;

    private String mo;

    /**
     * 型号图片
     */
    private String modelPicture;

    /**
     * 标签_名牌图片（nameplate picture）
     */
    private String nameplatePicture;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 申报品名
     */
    private String declarationName;

    /**
     * HS CODE
     */
    private String hsCode;

    /**
     * 申报要素
     */
    private String declarationElements;

    //   料号
    private String materialNumber;

    //    零件号
    private String partNumber;

    // 中文品名 Chinese Name
    private String chineseName;

    //英文品名 English Name
    private String englishName;

    //生产原厂 manufacture
    private String manufacturer;

    //品牌 Brand
    private String brand;

    //型号 Model
    private String model;

    //序列 (serial number）
    private String serialNumber;

    //设备情况 （旧/新）
    private String equipmentCondition;

    //原产地 Origin
    private String origin;

    //生产/购买年月 Date
    private String productionDate;

    //数量 Quantity
    private Integer quantity;

    //单位 unit
    private String unit;

    //单价 Unit price
    private Double unitPrice;

    /**
     * 总价（数量*单价）
     */
    private Double totalAmount;

    //材质(非设备) （中英文） Material (if not Equipment)
    private String material;

    //尺寸 demension
    private String dimension;

    //  工作原理（设备） working principle
    private String workingPrinciple;

    //用途（中英文） Function
    private String functionEn;

    //功能
    private String functionality;

    //功率（如有） Power
    private String power;

    //电压（如有） Voltage
    private String voltage;

    //加工方法
    private String processingMethod;

    //    是否有接头 （针对线材类）
    private String hasConnector;

    // 胶管类的需确认以下5项
    // 橡胶类的需确认以下5项

    //结构类型
    private String structureType;

    //总净重/KG
    private Double totalNetWeight;

    //    @ExcelProperty("胶管类的需确认以下5项")
    private String hydraulicHose;

    //    @ExcelProperty("橡胶类的需确认以下5项")
    private String rubberMaterial;

    //0草稿，1已提交，2归档，3退回，默认为0
    private String status;

    /**
     * 审批意见
     */
    private String approvalOpinion;

    /**
     * 审批人
     */
    private Long approver;

    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 提交人
     */
    private Long submitter;

    /**
     * 提交时间
     */
    private Date submitTime;

}
