package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.ProRoute;
import org.dromara.mes.domain.bo.ProRouteBo;
import org.dromara.mes.domain.vo.ProRouteVo;
import org.dromara.mes.mapper.ProRouteMapper;
import org.dromara.mes.service.IProRouteProcessService;
import org.dromara.mes.service.IProRouteService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工艺路线Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class ProRouteServiceImpl implements IProRouteService {

    private final ProRouteMapper baseMapper;
    private final IProRouteProcessService proRouteProcessService;

    /**
     * 查询工艺路线
     *
     * @param routeId 主键
     * @return 工艺路线
     */
    @Override
    public ProRouteVo queryById(Long routeId) {
        return baseMapper.selectVoById(routeId);
    }

    /**
     * 分页查询工艺路线列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺路线分页列表
     */
    @Override
    public TableDataInfo<ProRouteVo> queryPageList(ProRouteBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProRoute> lqw = buildQueryWrapper(bo);
        Page<ProRouteVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工艺路线列表
     *
     * @param bo 查询条件
     * @return 工艺路线列表
     */
    @Override
    public List<ProRouteVo> queryList(ProRouteBo bo) {
        LambdaQueryWrapper<ProRoute> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProRoute> buildQueryWrapper(ProRouteBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProRoute> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProRoute::getRouteId);
        lqw.like(StringUtils.isNotBlank(bo.getRouteCode()), ProRoute::getRouteCode, bo.getRouteCode());
        lqw.like(StringUtils.isNotBlank(bo.getRouteName()), ProRoute::getRouteName, bo.getRouteName());
        lqw.like(StringUtils.isNotBlank(bo.getRouteDesc()), ProRoute::getRouteDesc, bo.getRouteDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getEnableFlag()), ProRoute::getEnableFlag, bo.getEnableFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr1()), ProRoute::getAttr1, bo.getAttr1());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr2()), ProRoute::getAttr2, bo.getAttr2());
        lqw.eq(bo.getAttr3() != null, ProRoute::getAttr3, bo.getAttr3());
        lqw.eq(bo.getAttr4() != null, ProRoute::getAttr4, bo.getAttr4());
        return lqw;
    }

    /**
     * 新增工艺路线
     *
     * @param bo 工艺路线
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProRouteBo bo) {
        ProRoute add = MapstructUtils.convert(bo, ProRoute.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRouteId(add.getRouteId());
        }
        return flag;
    }

    /**
     * 修改工艺路线
     *
     * @param bo 工艺路线
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProRouteBo bo) {
        ProRoute update = MapstructUtils.convert(bo, ProRoute.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProRoute entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除工艺路线信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {

        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        for (Long routeId : ids) {
            proRouteProcessService.deleteByRouteId(routeId);
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
