package org.dromara.mes.domain.bo;

import org.dromara.mes.domain.ProRouteProcess;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 工艺组成业务对象 pro_route_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ProRouteProcess.class, reverseConvertGenerate = false)
public class ProRouteProcessBo extends BaseEntity {

    /**
     * 记录ID
     */
    @NotNull(message = "记录ID不能为空", groups = { EditGroup.class })
    private Long recordId;

    /**
     * 工艺路线ID
     */
    @NotNull(message = "工艺路线ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long routeId;

    /**
     * 工序ID
     */
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 序号
     */
    private Long orderNum;

    /**
     * 工序ID
     */
    @NotNull(message = "工序ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long nextProcessId;

    /**
     * 工序编码
     */
    private String nextProcessCode;

    /**
     * 工序名称
     */
    private String nextProcessName;

    /**
     * 与下一道工序关系
     */
    private String linkType;

    /**
     * 批量等待时间
     */
    private Long defaultPreTime;

    /**
     * 等待时间
     */
    private Long defaultSufTime;


    /**
     * 急单时间
     */
    private Long urgWaitTime;

    /**
     * 甘特图显示颜色
     */
    private String colorCode;

    /**
     * 关键工序
     */
    private String keyFlag;

    /**
     * 是否检验
     */
    private String isCheck;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
