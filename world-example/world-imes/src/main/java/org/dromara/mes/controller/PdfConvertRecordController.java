package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateUtil;
import cn.idev.excel.context.AnalysisContext;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.PdfConvertRecordBo;
import org.dromara.mes.domain.vo.PdfConvertRecordVo;
import org.dromara.mes.service.IPdfConvertRecordService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.io.FileUtil;
import org.dromara.common.core.utils.PLMFileTool;
import cn.idev.excel.EasyExcel;
import cn.idev.excel.read.listener.ReadListener;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * PDF转换记录
 * 前端访问路由地址为:/mes/convertRecord
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/convertRecord")
public class PdfConvertRecordController extends BaseController {

    private final IPdfConvertRecordService pdfConvertRecordService;

    // 线程池用于并行处理 - 改为static final避免@RequiredArgsConstructor冲突
    // 根据CPU核心数和系统负载动态调整线程池大小
    private static final int CORE_POOL_SIZE = Math.max(4, Runtime.getRuntime().availableProcessors());
    private static final int MAX_POOL_SIZE = Math.min(50, CORE_POOL_SIZE * 2);
    private static final ExecutorService executorService = Executors.newFixedThreadPool(MAX_POOL_SIZE);
    // API 基础地址（开发/生产）
    private static final String BASE_URL_DEV = "https://tplm.world-machining.com/world-web/api/view/BrowseFile";
    private static final String BASE_URL_PRO = "https://plm.world-machining.com/world-web/api/view/BrowseFile";

    // 认证用户名（可通过环境变量 PLM_USERNAME 覆盖，默认 WDPLM）
    private static final String DEFAULT_USERNAME = "WDPLM";

    // 从环境变量读取密码，避免在代码中明文存放
    // 开发环境密码：PLM_PASSWORD_DEV，生产环境密码：PLM_PASSWORD_PRO
    private static final String ENV_USERNAME = "WDPLM";
    private static final String ENV_PASSWORD_DEV = "12345678";
    private static final String ENV_PASSWORD_PRO = "adm123456";
    private static final String ENV_RUN_ENV = "prod"; // 可为 dev / prod


    // 使用通用存储桶常量
    private static final String BUCKET_NAME = Constants.BUCKET_BARCODE;


    /**
     * 查询PDF转换记录列表
     */
    @SaCheckPermission("mes:convertRecord:list")
    @GetMapping("/list")
    public TableDataInfo<PdfConvertRecordVo> list(PdfConvertRecordBo bo, PageQuery pageQuery) {
        return pdfConvertRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出PDF转换记录列表
     */
    @SaCheckPermission("mes:convertRecord:export")
    @Log(title = "PDF转换记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PdfConvertRecordBo bo, HttpServletResponse response) {
        List<PdfConvertRecordVo> list = pdfConvertRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "PDF转换记录", PdfConvertRecordVo.class, response);
    }

    /**
     * 获取PDF转换记录详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:convertRecord:query")
    @GetMapping("/{id}")
    public R<PdfConvertRecordVo> getInfo(@NotNull(message = "主键不能为空")
                                         @PathVariable("id") Long id) {
        return R.ok(pdfConvertRecordService.queryById(id));
    }

    /**
     * 新增PDF转换记录
     */
    @SaCheckPermission("mes:convertRecord:add")
    @Log(title = "PDF转换记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PdfConvertRecordBo bo) {
        return toAjax(pdfConvertRecordService.insertByBo(bo));
    }

    /**
     * 修改PDF转换记录
     */
    @SaCheckPermission("mes:convertRecord:edit")
    @Log(title = "PDF转换记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PdfConvertRecordBo bo) {
        return toAjax(pdfConvertRecordService.updateByBo(bo));
    }

    /**
     * 删除PDF转换记录
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:convertRecord:remove")
    @Log(title = "PDF转换记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(pdfConvertRecordService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 批量下载并转换MO或NO对应的文件为ZIP格式
     *
     * @param mos      MO号列表
     * @param nos      NO号列表
     * @param scale    缩略比例
     * @param response HTTP响应对象
     */
    @PostMapping("/batchDownloadAndConvertByMoOrNoWithZip")
    public void batchDownloadAndConvertByMoOrNoWithZip(
        @RequestParam(value = "mos", required = false) String mos,
        @RequestParam(value = "nos", required = false) String nos,
        @RequestParam(value = "scale", defaultValue = "0.94") float scale,
        HttpServletResponse response) {

        try {
            // 参数验证
            if ((mos == null || mos.trim().isEmpty()) && (nos == null || nos.trim().isEmpty())) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"至少需要提供一个图号号\"}");
                return;
            }

            // 缩放比例验证
            if (scale <= 0 || scale > 1.0f) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"缩放比例必须在0到1.0之间\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"converted_files.zip\"");
            response.setHeader("Cache-Control", "no-cache");

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                Set<String> addedFiles = new HashSet<>();
                List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

                // 处理MO号列表
                if (mos != null && !mos.trim().isEmpty()) {
                    String[] moArray = mos.split(",");
                    for (String mo : moArray) {
                        String cleanMo = mo != null ? mo.trim() : "";
                        if (!cleanMo.isEmpty()) {
                            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    return pdfConvertRecordService.processFile(cleanMo, "MO", scale);
                                } catch (Exception e) {
                                    log.error("处理MO号 {} 时发生错误: {}", cleanMo, e.getMessage());
                                    Map<String, Object> errorResult = new HashMap<>();
                                    errorResult.put("success", false);
                                    errorResult.put("identifier", cleanMo);
                                    errorResult.put("type", "MO");
                                    errorResult.put("error", e.getMessage());
                                    return errorResult;
                                }
                            }, executorService);
                            futures.add(future);
                        }
                    }
                }

                // 处理NO号列表
                if (nos != null && !nos.trim().isEmpty()) {
                    String[] noArray = nos.split(",");
                    for (String no : noArray) {
                        String cleanNo = no != null ? no.trim() : "";
                        if (!cleanNo.isEmpty()) {
                            CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    return pdfConvertRecordService.processFile(cleanNo, "NO", scale);
                                } catch (Exception e) {
                                    log.error("处理NO号 {} 时发生错误: {}", cleanNo, e.getMessage());
                                    Map<String, Object> errorResult = new HashMap<>();
                                    errorResult.put("success", false);
                                    errorResult.put("identifier", cleanNo);
                                    errorResult.put("type", "NO");
                                    errorResult.put("error", e.getMessage());
                                    return errorResult;
                                }
                            }, executorService);
                            futures.add(future);
                        }
                    }
                }

                // 等待所有任务完成，设置超时时间
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(5, java.util.concurrent.TimeUnit.MINUTES); // 5分钟超时
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("批量处理超时");
                    // 取消未完成的任务
                    futures.forEach(future -> future.cancel(true));
                }

                // 收集成功处理的文件并添加到ZIP
                int successCount = 0;
                int failCount = 0;
                Set<String> duplicateNames = new HashSet<>();

                for (CompletableFuture<Map<String, Object>> future : futures) {
                    try {
                        if (future.isDone() && !future.isCancelled()) {
                            Map<String, Object> result = future.get();
                            if (result != null) {
                                if ((Boolean) result.getOrDefault("success", false)) {
                                    // 处理重复文件名
                                    String originalName = (String) result.get("zipEntryName");
                                    String uniqueName = generateUniqueFileName(originalName, addedFiles, duplicateNames);
                                    result.put("zipEntryName", uniqueName);

                                    pdfConvertRecordService.addFileToZip(result, zipOut, addedFiles);

                                    // 保存转换记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存转换记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存转换记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    successCount++;
                                } else {
                                    // 保存失败记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存失败记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存失败记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    failCount++;
                                    log.warn("文件处理失败: {} - {}", result.get("identifier"), result.get("message"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("获取处理结果时发生错误", e);
                    }
                }

                // 添加处理结果摘要到ZIP
                String summary = String.format("处理完成: 成功 %d 个，失败 %d 个", successCount, failCount);
                zipOut.putNextEntry(new ZipEntry("处理结果.txt"));
                zipOut.write(summary.getBytes("UTF-8"));
                zipOut.closeEntry();

                log.info("批量处理完成: 成功 {} 个，失败 {} 个", successCount, failCount);
            }
        } catch (IOException e) {
            log.error("批量下载转换文件时发生IO错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error("批量下载转换文件时发生未知错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 批量上传多文件图纸转换缩略后下载ZIP
     *
     * @param files    上传的PDF文件数组
     * @param scale    缩放比例，默认0.94
     * @param response HTTP响应
     */
    @PostMapping(value = "/batchUploadAndConvertWithZip", consumes = org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE)
    public void batchUploadAndConvertWithZip(
        @RequestParam("files") MultipartFile[] files,
        @RequestParam(value = "scale", defaultValue = "0.94") float scale,
        HttpServletResponse response) {

        try {
            // 参数验证
            if (files == null || files.length == 0) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"至少需要上传一个PDF文件\"}");
                return;
            }

            // 缩放比例验证
            if (scale <= 0 || scale > 1.0f) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"缩放比例必须在0到1.0之间\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"converted_uploaded_files.zip\"");
            response.setHeader("Cache-Control", "no-cache");

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                Set<String> addedFiles = new HashSet<>();
                List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();
                Set<String> duplicateNames = new HashSet<>();

                // 处理每个上传的文件
                for (int i = 0; i < files.length; i++) {
                    MultipartFile file = files[i];
                    final int fileIndex = i; // 为闭包使用

                    // 跳过空文件
                    if (file.isEmpty()) {
                        log.warn("跳过空文件: {}", file.getOriginalFilename());
                        continue;
                    }

                    // 验证文件类型
                    String originalFilename = file.getOriginalFilename();
                    if (originalFilename == null || !originalFilename.toLowerCase().endsWith(".pdf")) {
                        log.warn("跳过非PDF文件: {}", originalFilename);
                        continue;
                    }

                    CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                        try {
                            return processUploadedFile(file, fileIndex, scale);
                        } catch (Exception e) {
                            log.error("处理上传文件 {} 时发生错误: {}", originalFilename, e.getMessage());
                            Map<String, Object> errorResult = new HashMap<>();
                            errorResult.put("success", false);
                            errorResult.put("identifier", originalFilename);
                            errorResult.put("type", "UPLOAD");
                            errorResult.put("error", e.getMessage());
                            errorResult.put("originalFileName", originalFilename);
                            return errorResult;
                        }
                    }, executorService);
                    futures.add(future);
                }

                // 等待所有任务完成，设置超时时间
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(10, java.util.concurrent.TimeUnit.MINUTES); // 10分钟超时
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("批量处理超时");
                    // 取消未完成的任务
                    futures.forEach(future -> future.cancel(true));
                }

                // 收集成功处理的文件并添加到ZIP
                int successCount = 0;
                int failCount = 0;

                for (CompletableFuture<Map<String, Object>> future : futures) {
                    try {
                        if (future.isDone() && !future.isCancelled()) {
                            Map<String, Object> result = future.get();
                            if (result != null) {
                                if ((Boolean) result.getOrDefault("success", false)) {
                                    // 处理重复文件名
                                    String originalName = (String) result.get("zipEntryName");
                                    String uniqueName = generateUniqueFileName(originalName, addedFiles, duplicateNames);
                                    result.put("zipEntryName", uniqueName);

                                    pdfConvertRecordService.addFileToZip(result, zipOut, addedFiles);

                                    // 保存转换记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存转换记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存转换记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    successCount++;
                                } else {
                                    // 保存失败记录到数据库
                                    try {
                                        savePdfConvertRecord(result, scale);
                                        log.info("成功保存失败记录: {}", result.get("identifier"));
                                    } catch (Exception e) {
                                        log.error("保存失败记录失败: {} - {}", result.get("identifier"), e.getMessage());
                                    }

                                    failCount++;
                                    log.warn("文件处理失败: {} - {}", result.get("identifier"), result.get("message"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("获取处理结果时发生错误", e);
                    }
                }

                // 添加处理结果摘要到ZIP
                String summary = String.format("处理完成: 成功 %d 个，失败 %d 个", successCount, failCount);
                zipOut.putNextEntry(new ZipEntry("处理结果.txt"));
                zipOut.write(summary.getBytes("UTF-8"));
                zipOut.closeEntry();

                log.info("批量上传文件处理完成: 成功 {} 个，失败 {} 个", successCount, failCount);
            }
        } catch (IOException e) {
            log.error("批量上传转换文件时发生IO错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        } catch (Exception e) {
            log.error("批量上传转换文件时发生未知错误", e);
            if (!response.isCommitted()) {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            }
        }
    }

    /**
     * 处理上传的PDF文件
     *
     * @param file      上传的文件
     * @param fileIndex 文件索引
     * @param scale     缩放比例
     * @return 处理结果
     */
    private Map<String, Object> processUploadedFile(MultipartFile file, int fileIndex, float scale) {
        Map<String, Object> result = new HashMap<>();
        String tempDir = null;
        String tempFilePath = null;
        String scaledFilePath = null;

        try {
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || originalFilename.isEmpty()) {
                originalFilename = "uploaded_file_" + fileIndex + ".pdf";
            }

            // 在执行 transferTo 之前获取原始文件大小，避免 Undertow 在移动临时文件后 getSize 抛出 NoSuchFileException
            long originalFileSize = 0L;
            try {
                originalFileSize = file.getSize();
            } catch (Exception ex) {
                log.warn("获取上传文件大小失败，将在保存后再计算: {}", ex.getMessage());
            }

            // 创建临时目录
            tempDir = System.getProperty("java.io.tmpdir") + File.separator + "pdf_upload_" + System.currentTimeMillis() + "_" + fileIndex;
            File tempDirFile = new File(tempDir);
            if (!tempDirFile.exists()) {
                tempDirFile.mkdirs();
            }

            // 保存上传文件到临时目录
            tempFilePath = tempDir + File.separator + originalFilename;
            File tempFile = new File(tempFilePath);
            file.transferTo(tempFile);

            // 生成输出文件名
            String baseName = originalFilename;
            int dotIndex = originalFilename.lastIndexOf('.');
            if (dotIndex > 0) {
                baseName = originalFilename.substring(0, dotIndex);
            }
            String scaledFileName = baseName + "_scaled.pdf";
            scaledFilePath = tempDir + File.separator + scaledFileName;

            // 执行PDF缩放
            boolean scaleSuccess = pdfConvertRecordService.scalePdfWithIText(tempFilePath, scaledFilePath, scale, false);

            if (!scaleSuccess) {
                result.put("success", false);
                result.put("message", "PDF缩放失败");
                result.put("identifier", originalFilename);
                result.put("type", "UPLOAD");
                result.put("originalFileName", originalFilename);
                return result;
            }

            // 验证缩放后的文件
            File scaledFile = new File(scaledFilePath);
            if (!scaledFile.exists() || scaledFile.length() == 0) {
                result.put("success", false);
                result.put("message", "缩放后的文件不存在或为空");
                result.put("identifier", originalFilename);
                result.put("type", "UPLOAD");
                result.put("originalFileName", originalFilename);
                return result;
            }

            // 如果之前未能获取原始大小，则使用保存后的源文件大小
            if (originalFileSize <= 0L && tempFile.exists()) {
                originalFileSize = tempFile.length();
            }

            // 成功处理
            result.put("success", true);
            result.put("identifier", originalFilename);
            result.put("type", "UPLOAD");
            result.put("originalFileName", originalFilename);
            result.put("scaledFilePath", scaledFilePath);
            result.put("zipEntryName", originalFilename);
            result.put("tempDir", tempDir);
            result.put("fileSize", scaledFile.length());
            result.put("originalFileSize", originalFileSize);
            result.put("scaledFileSize", scaledFile.length());

            log.info("成功处理上传文件: {} -> {}", originalFilename, originalFilename);
            return result;

        } catch (Exception e) {
            log.error("处理上传文件时发生错误: {}", e.getMessage(), e);
            // 避免在 Undertow 已移动/清理临时文件后继续访问 MultipartFile 导致的异常
            String safeOriginalName;
            try {
                safeOriginalName = file.getOriginalFilename();
            } catch (Exception ignore) {
                safeOriginalName = "unknown.pdf";
            }
            result.put("success", false);
            result.put("message", "处理上传文件时发生错误: " + e.getMessage());
            result.put("identifier", safeOriginalName);
            result.put("type", "UPLOAD");
            result.put("originalFileName", safeOriginalName);
            result.put("error", e.getMessage());
            return result;
        }
    }

    /**
     * 生成唯一的文件名，避免重复
     */
    private String generateUniqueFileName(String originalName, Set<String> addedFiles, Set<String> duplicateNames) {
        if (originalName == null) {
            originalName = "unknown.pdf";
        }

        String baseName = originalName;
        String extension = "";
        int dotIndex = originalName.lastIndexOf('.');
        if (dotIndex > 0) {
            baseName = originalName.substring(0, dotIndex);
            extension = originalName.substring(dotIndex);
        }

        String uniqueName = originalName;
        int counter = 1;
        while (addedFiles.contains(uniqueName) || duplicateNames.contains(uniqueName)) {
            uniqueName = baseName + "_" + counter + extension;
            counter++;
        }

        duplicateNames.add(uniqueName);
        return uniqueName;
    }

    /**
     * 下载Excel模板文件
     *
     * @param response HTTP响应对象
     */
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 模板文件路径
            String templatePath = "excel/drwa_download.xlsx";
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream(templatePath);

            if (inputStream == null) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"error\":\"模板文件不存在\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=\"drwa_download_template.xlsx\"");
            response.setHeader("Cache-Control", "no-cache");

            // 复制文件内容到响应流
            IoUtil.copy(inputStream, response.getOutputStream());
            inputStream.close();

            log.info("Excel模板下载成功");
        } catch (Exception e) {
            log.error("下载Excel模板时发生错误", e);
            if (!response.isCommitted()) {
                try {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"error\":\"下载模板失败: " + e.getMessage().replace("\"", "\\\"") + "\"}");
                } catch (IOException ioException) {
                    log.error("写入错误响应时发生IO异常", ioException);
                }
            }
        }
    }

    /**
     * 批量上传Excel文件并下载PLM图纸
     *
     * @param file     Excel文件
     * @param response HTTP响应对象
     */
    @PostMapping(value = "/batchDownloadFromExcel", consumes = org.springframework.http.MediaType.MULTIPART_FORM_DATA_VALUE)
    public void batchDownloadFromExcel(
        @RequestParam("file") MultipartFile file,
        HttpServletResponse response) {

        try {
            response.setContentType("application/json;charset=UTF-8");
            // 参数验证
            if (file == null || file.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"请上传Excel文件\"}");
                return;
            }

            // 验证文件类型
            String originalFilename = file.getOriginalFilename();
            if (originalFilename == null || (!originalFilename.toLowerCase().endsWith(".xlsx") && !originalFilename.toLowerCase().endsWith(".xls"))) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"仅支持Excel文件(.xlsx/.xls)\"}");
                return;
            }

            // 解析Excel文件获取图号列表
            List<String> drawingNumbers = parseExcelForDrawingNumbers(file);
            if (drawingNumbers.isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"Excel文件中未找到有效的图号数据\"}");
                return;
            }

            log.info("从Excel文件中解析到 {} 个图号", drawingNumbers.size());

            // 性能优化：限制最大图号数量，避免系统过载
            final int MAX_DRAWING_COUNT = 500; // 最大500个图号
            if (drawingNumbers.size() > MAX_DRAWING_COUNT) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("{\"error\":\"图号数量超过限制，最多支持" + MAX_DRAWING_COUNT + "个图号\"}");
                return;
            }

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"plm_drawings_" + System.currentTimeMillis() + ".zip\"");
            response.setHeader("Cache-Control", "no-cache");

            // 创建ZIP输出流
            try (ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream())) {
                Set<String> addedFiles = new HashSet<>();
                List<CompletableFuture<Map<String, Object>>> futures = new ArrayList<>();

                // 并行处理每个图号
                for (String drawingNumber : drawingNumbers) {
                    String cleanDrawingNumber = drawingNumber != null ? drawingNumber.trim() : "";
                    if (!cleanDrawingNumber.isEmpty()) {
                        CompletableFuture<Map<String, Object>> future = CompletableFuture.supplyAsync(() -> {
                            try {
                                return downloadPLMDrawing(cleanDrawingNumber);
                            } catch (Exception e) {
                                log.error("下载图号 {} 的图纸时发生错误: {}", cleanDrawingNumber, e.getMessage());
                                Map<String, Object> errorResult = new HashMap<>();
                                errorResult.put("success", false);
                                errorResult.put("drawingNumber", cleanDrawingNumber);
                                errorResult.put("error", e.getMessage());
                                return errorResult;
                            }
                        }, executorService);
                        futures.add(future);
                    }
                }

                // 性能优化：根据图号数量动态调整超时时间
                int timeoutMinutes = Math.max(5, Math.min(30, drawingNumbers.size() / 10 + 5));
                log.info("设置超时时间: {} 分钟", timeoutMinutes);

                // 等待所有任务完成，设置动态超时时间
                try {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .get(timeoutMinutes, java.util.concurrent.TimeUnit.MINUTES);
                } catch (java.util.concurrent.TimeoutException e) {
                    log.error("批量下载PLM图纸超时，超时时间: {} 分钟", timeoutMinutes);
                    // 取消未完成的任务
                    futures.forEach(future -> future.cancel(true));
                }

                // 收集成功下载的文件并添加到ZIP
                int successCount = 0;
                int failCount = 0;
                Set<String> duplicateNames = new HashSet<>();
                List<PdfConvertRecordBo> batchRecords = new ArrayList<>(); // 批量保存记录
                List<String> failedDrawingNumbers = new ArrayList<>(); // 收集失败的图号

                for (CompletableFuture<Map<String, Object>> future : futures) {
                    try {
                        if (future.isDone() && !future.isCancelled()) {
                            Map<String, Object> result = future.get();
                            if (result != null) {
                                if ((Boolean) result.getOrDefault("success", false)) {
                                    // 处理重复文件名
                                    String originalName = (String) result.get("fileName");
                                    String uniqueName = generateUniqueFileName(originalName, addedFiles, duplicateNames);
                                    result.put("fileName", uniqueName);

                                    // 添加文件到ZIP
                                    addFileToZipFromPath(result, zipOut, addedFiles);

                                    // 准备批量保存记录
                                    PdfConvertRecordBo record = createPLMDownloadRecord(result);
                                    if (record != null) {
                                        batchRecords.add(record);
                                    }

                                    successCount++;
                                } else {
                                    // 收集失败的图号
                                    String failedDrawingNumber = (String) result.get("drawingNumber");
                                    String errorMessage = (String) result.get("error");
                                    if (failedDrawingNumber != null) {
                                        failedDrawingNumbers.add(failedDrawingNumber + " (失败原因: " + errorMessage + ")");
                                    }

                                    // 准备批量保存失败记录
                                    PdfConvertRecordBo record = createPLMDownloadRecord(result);
                                    if (record != null) {
                                        batchRecords.add(record);
                                    }

                                    failCount++;
                                    log.warn("PLM图纸下载失败: {} - {}", result.get("drawingNumber"), result.get("error"));
                                }
                            }
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("获取PLM下载结果时发生错误", e);
                    }
                }

                // 性能优化：批量保存数据库记录
                if (!batchRecords.isEmpty()) {
                    try {
                        batchSavePLMDownloadRecords(batchRecords);
                        log.info("批量保存PLM下载记录成功，共 {} 条", batchRecords.size());
                    } catch (Exception e) {
                        log.error("批量保存PLM下载记录失败", e);
                    }
                }

                // 添加处理结果摘要到ZIP
                String summary = String.format("PLM图纸下载完成: 成功 %d 个，失败 %d 个\n", successCount, failCount);
                summary += "处理时间: " + DateUtil.date() + "\n";
                summary += "总图号数: " + drawingNumbers.size() + "\n";
                summary += "实际处理图号数: " + (successCount + failCount) + "\n";

                if (!failedDrawingNumbers.isEmpty()) {
                    summary += "\n失败的图号列表:\n";
                    for (String failedDrawing : failedDrawingNumbers) {
                        summary += "- " + failedDrawing + "\n";
                    }
                }

                zipOut.putNextEntry(new ZipEntry("下载结果.txt"));
                zipOut.write(summary.getBytes("UTF-8"));
                zipOut.closeEntry();

                log.info("批量下载PLM图纸完成: 成功 {} 个，失败 {} 个", successCount, failCount);
            }
        } catch (IOException e) {
            log.error("批量下载PLM图纸时发生IO错误", e);
            if (!response.isCommitted()) {
                try {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"error\":\"文件下载过程中发生IO错误: " + e.getMessage().replace("\"", "\\\"") + "\"}");
                } catch (IOException ioException) {
                    log.error("写入错误响应时发生IO异常", ioException);
                }
            }
        } catch (Exception e) {
            log.error("批量下载PLM图纸时发生未知错误", e);
            if (!response.isCommitted()) {
                try {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"error\":\"系统内部错误: " + e.getMessage().replace("\"", "\\\"") + "\"}");
                } catch (IOException ioException) {
                    log.error("写入错误响应时发生IO异常", ioException);
                }
            }
        }
    }

    /**
     * 解析Excel文件获取图号列表
     * 根据drwa_download.xlsx模板，图号&版本在第E列（索引4）
     *
     * @param file Excel文件
     * @return 图号列表
     */
    private List<String> parseExcelForDrawingNumbers(MultipartFile file) {
        List<String> drawingNumbers = new ArrayList<>();
        List<String> filteredOut = new ArrayList<>(); // 记录被过滤掉的数据

        try (InputStream inputStream = file.getInputStream()) {
            // 使用EasyExcel读取Excel文件的图号&版本列（第E列，索引4）
            EasyExcel.read(inputStream)
                .sheet(0) // 读取第一个sheet
                .headRowNumber(1) // 跳过前2行（第1行空行）
                .registerReadListener(new ReadListener<Map<Integer, String>>() {
                    private int rowCount = 0;

                    @Override
                    public void invoke(Map<Integer, String> data, AnalysisContext context) {
                        rowCount++;
                        // 获取第E列的数据（索引4）- 图号&版本列
                        String cellValue = data.get(4);
                        if (cellValue != null && !cellValue.trim().isEmpty()) {
                            String drawingNumber = cellValue.trim();
                            // 只过滤表头，其他数据都处理
                            if (!"图号&版本".equals(drawingNumber)) {
                                drawingNumbers.add(drawingNumber);
                            } else {
                                filteredOut.add(drawingNumber + " (过滤原因: 表头)");
                            }
                        } else {
                            log.debug("第{}行E列数据为空或null", rowCount);
                        }
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        log.info("Excel解析完成 - 总读取行数: {}, 有效图号: {} 个, 过滤掉: {} 个", rowCount, drawingNumbers.size(), filteredOut.size());
                    }
                })
                .doRead();

        } catch (Exception e) {
            log.error("解析Excel文件时发生错误", e);
            throw new RuntimeException("解析Excel文件失败: " + e.getMessage());
        }

        return drawingNumbers;
    }

    /**
     * 下载PLM图纸
     *
     * @param drawingNumber 图号
     * @return 下载结果
     */
    private Map<String, Object> downloadPLMDrawing(String drawingNumber) {
        Map<String, Object> result = new HashMap<>();
        String tempDir = null;
        String tempFilePath = null;

        try {
            log.debug("开始处理图号: {}", drawingNumber);

            // 调用PLM工具类获取图纸URL
            String downloadUrl = PLMFileTool.getMoUrlResult("", drawingNumber);
            log.debug("图号 {} 获取到的URL: {}", drawingNumber, downloadUrl);

            if ("DATA_NOT_FOUND".equals(downloadUrl) || StrUtil.isBlank(downloadUrl)) {
                log.warn("图号 {} 未找到对应的PLM图纸", drawingNumber);
                result.put("success", false);
                result.put("drawingNumber", drawingNumber);
                result.put("error", "未找到对应的PLM图纸");
                return result;
            }

            // 清理URL
            downloadUrl = downloadUrl.replace("`", "").trim();
            log.debug("图号 {} 清理后的URL: {}", drawingNumber, downloadUrl);

            // 性能优化：使用共享临时目录，避免创建过多目录
            String baseTempDir = System.getProperty("java.io.tmpdir") + File.separator + "plm_download_" + Thread.currentThread().getId();
            File baseTempDirFile = new File(baseTempDir);
            if (!baseTempDirFile.exists()) {
                baseTempDirFile.mkdirs();
            }
            tempDir = baseTempDir;

            // 生成文件名
            String fileName = generatePLMFileName(drawingNumber, downloadUrl);
            tempFilePath = tempDir + File.separator + fileName;
            log.debug("图号 {} 生成的文件路径: {}", drawingNumber, tempFilePath);

            // 下载文件
            try {
                cn.hutool.http.HttpUtil.downloadFile(downloadUrl, new File(tempFilePath));
                log.debug("图号 {} 文件下载完成", drawingNumber);
            } catch (Exception downloadException) {
                log.error("图号 {} 下载文件时发生异常: {}", drawingNumber, downloadException.getMessage(), downloadException);
                result.put("success", false);
                result.put("drawingNumber", drawingNumber);
                result.put("error", "文件下载异常: " + downloadException.getMessage());
                return result;
            }

            File downloadedFile = new File(tempFilePath);
            if (downloadedFile.exists() && downloadedFile.length() > 0) {
                result.put("success", true);
                result.put("drawingNumber", drawingNumber);
                result.put("fileName", fileName);
                result.put("filePath", tempFilePath);
                result.put("tempDir", tempDir);
                result.put("fileSize", downloadedFile.length());
                result.put("downloadUrl", downloadUrl);
                log.info("成功下载PLM图纸: {} -> {} (大小: {} bytes)", drawingNumber, fileName, downloadedFile.length());
                return result;
            } else {
                log.warn("图号 {} 下载的文件不存在或为空: {}", drawingNumber, tempFilePath);
                result.put("success", false);
                result.put("drawingNumber", drawingNumber);
                result.put("error", "文件下载失败或文件为空");
                return result;
            }

        } catch (Exception e) {
            log.error("下载PLM图纸时发生错误: 图号={}, 错误={}", drawingNumber, e.getMessage(), e);
            result.put("success", false);
            result.put("drawingNumber", drawingNumber);
            result.put("error", "下载过程发生错误: " + e.getMessage());
            return result;
        }
    }

    /**
     * 生成PLM文件名
     *
     * @param drawingNumber 图号
     * @param downloadUrl   下载URL
     * @return 文件名
     */
    private String generatePLMFileName(String drawingNumber, String downloadUrl) {
        // 从URL中提取文件扩展名
        String extension = ".pdf"; // 默认扩展名
        if (downloadUrl.contains(".")) {
            String urlExtension = downloadUrl.substring(downloadUrl.lastIndexOf("."));
            if (urlExtension.length() <= 5) { // 合理的扩展名长度
                extension = urlExtension;
            }
        }

        // 清理图号中的特殊字符
        String cleanDrawingNumber = drawingNumber.replaceAll("[\\\\/:*?\"<>|]", "_");

        return cleanDrawingNumber + extension;
    }

    /**
     * 将文件添加到ZIP中（从文件路径）
     *
     * @param result  文件信息
     * @param zipOut  ZIP输出流
     * @param addedFiles 已添加文件集合
     */
    private void addFileToZipFromPath(Map<String, Object> result, ZipOutputStream zipOut, Set<String> addedFiles) {
        try {
            String filePath = (String) result.get("filePath");
            String fileName = (String) result.get("fileName");

            if (filePath != null && fileName != null) {
                File file = new File(filePath);
                if (file.exists() && !addedFiles.contains(fileName)) {
                    zipOut.putNextEntry(new ZipEntry(fileName));
                    try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
                        IoUtil.copy(fis, zipOut);
                    }
                    zipOut.closeEntry();
                    addedFiles.add(fileName);

                    // 清理临时文件
                    try {
                        FileUtil.del(file);
                    } catch (Exception e) {
                        log.warn("清理临时文件失败: {}", filePath);
                    }
                }
            }
        } catch (Exception e) {
            log.error("添加文件到ZIP时发生错误", e);
        }
    }

    /**
     * 创建PLM下载记录对象（不保存到数据库）
     */
    private PdfConvertRecordBo createPLMDownloadRecord(Map<String, Object> result) {
        try {
            PdfConvertRecordBo bo = new PdfConvertRecordBo();

            // 设置基本信息
            String drawingNumber = (String) result.get("drawingNumber");
            bo.setNoNumber(drawingNumber);
            bo.setMoNumber(""); // PLM下载时MO号设为空字符串以满足数据库非空约束

            // 设置文件信息
            bo.setOriginalFileName((String) result.get("fileName"));
            bo.setConvertedFileName((String) result.get("fileName"));

            // 设置处理状态
            Boolean success = (Boolean) result.getOrDefault("success", false);
            bo.setStatus(success ? 1L : 2L); // 1-成功，2-失败

            // 设置错误信息（如果有）
            if (!success) {
                String errorMessage = (String) result.get("error");
                bo.setErrorMessage(errorMessage);
            }

            // 设置文件大小
            Object fileSizeObj = result.get("fileSize");
            if (fileSizeObj != null) {
                if (fileSizeObj instanceof Long) {
                    bo.setFileSize((Long) fileSizeObj);
                } else if (fileSizeObj instanceof Integer) {
                    bo.setFileSize(((Integer) fileSizeObj).longValue());
                }
            }

            return bo;

        } catch (Exception e) {
            log.error("创建PLM下载记录时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 批量保存PLM下载记录到数据库
     */
    private void batchSavePLMDownloadRecords(List<PdfConvertRecordBo> records) {
        try {
            // 分批保存，避免单次操作过大
            final int BATCH_SIZE = 100;
            for (int i = 0; i < records.size(); i += BATCH_SIZE) {
                int endIndex = Math.min(i + BATCH_SIZE, records.size());
                List<PdfConvertRecordBo> batch = records.subList(i, endIndex);

                for (PdfConvertRecordBo record : batch) {
                    pdfConvertRecordService.insertByBo(record);
                }
            }
        } catch (Exception e) {
            log.error("批量保存PLM下载记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存PLM下载记录到数据库（单个保存，已废弃，使用批量保存）
     */
    @Deprecated
    private void savePLMDownloadRecord(Map<String, Object> result) {
        try {
            PdfConvertRecordBo bo = new PdfConvertRecordBo();

            // 设置基本信息
            String drawingNumber = (String) result.get("drawingNumber");
            bo.setNoNumber(drawingNumber);
            bo.setMoNumber(""); // PLM下载时MO号设为空字符串以满足数据库非空约束

            // 设置文件信息
            bo.setOriginalFileName((String) result.get("fileName"));
            bo.setConvertedFileName((String) result.get("fileName"));

            // 设置处理状态
            Boolean success = (Boolean) result.getOrDefault("success", false);
            bo.setStatus(success ? 1L : 2L); // 1-成功，2-失败

            // 设置错误信息（如果有）
            if (!success) {
                String errorMessage = (String) result.get("error");
                bo.setErrorMessage(errorMessage);
            }

            // 设置文件大小
            Object fileSizeObj = result.get("fileSize");
            if (fileSizeObj != null) {
                if (fileSizeObj instanceof Long) {
                    bo.setFileSize((Long) fileSizeObj);
                } else if (fileSizeObj instanceof Integer) {
                    bo.setFileSize(((Integer) fileSizeObj).longValue());
                }
            }

            // 保存到数据库
            pdfConvertRecordService.insertByBo(bo);

        } catch (Exception e) {
            log.error("保存PLM下载记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 保存PDF转换记录到数据库
     */
    private void savePdfConvertRecord(Map<String, Object> result, float scale) {
        try {
            PdfConvertRecordBo bo = new PdfConvertRecordBo();

            // 设置基本信息
            String identifier = (String) result.get("identifier");
            String type = (String) result.get("type");

            if ("MO".equals(type)) {
                bo.setMoNumber(identifier);
                // MO类型时，NO号可以为空
                bo.setNoNumber(null);
            } else if ("NO".equals(type)) {
                bo.setNoNumber(identifier);
                // 如果是NO类型，MO号设置为空字符串以满足非空约束，但不使用NO号值
                bo.setMoNumber("");
            } else if ("UPLOAD".equals(type)) {
                // 前端上传的文件没有MO/NO，保持NO为空，MO设为空字符串以满足数据库非空约束
                bo.setMoNumber("");
                bo.setNoNumber(null);
            }

            // 设置文件信息
            bo.setOriginalFileName((String) result.get("originalFileName"));
            bo.setConvertedFileName((String) result.get("zipEntryName"));

            // 设置处理状态
            Boolean success = (Boolean) result.getOrDefault("success", false);
            bo.setStatus(success ? 1L : 2L); // 1-成功，2-失败

            // 设置错误信息（如果有）
            if (!success) {
                String errorMessage = (String) result.get("message");
                if (errorMessage == null) {
                    errorMessage = (String) result.get("error");
                }
                bo.setErrorMessage(errorMessage);
            }

            // 设置文件大小
            Object fileSizeObj = result.get("fileSize");
            if (fileSizeObj != null) {
                if (fileSizeObj instanceof Long) {
                    bo.setFileSize((Long) fileSizeObj);
                } else if (fileSizeObj instanceof Integer) {
                    bo.setFileSize(((Integer) fileSizeObj).longValue());
                } else if (fileSizeObj instanceof String) {
                    try {
                        bo.setFileSize(Long.parseLong((String) fileSizeObj));
                    } catch (NumberFormatException e) {
                        log.warn("无法解析文件大小: {}", fileSizeObj);
                    }
                }
            }

            // 保存到数据库
            pdfConvertRecordService.insertByBo(bo);

        } catch (Exception e) {
            log.error("保存PDF转换记录时发生错误: {}", e.getMessage(), e);
            throw e;
        }
    }

}
