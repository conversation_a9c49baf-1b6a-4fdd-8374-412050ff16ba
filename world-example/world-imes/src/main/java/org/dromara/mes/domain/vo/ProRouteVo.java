package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.mes.domain.ProRoute;

import java.io.Serial;
import java.io.Serializable;


/**
 * 工艺路线视图对象 pro_route
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProRoute.class)
public class ProRouteVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工艺路线ID
     */
    @ExcelProperty(value = "工艺路线ID")
    private Long routeId;

    /**
     * 工艺路线编号
     */
    @ExcelProperty(value = "工艺路线编号")
    private String routeCode;

    /**
     * 工艺路线名称
     */
    @ExcelProperty(value = "工艺路线名称")
    private String routeName;

    /**
     * 工艺路线说明
     */
    @ExcelProperty(value = "工艺路线说明")
    private String routeDesc;

    /**
     * 是否启用
     */
    @ExcelProperty(value = "是否启用")
    private String enableFlag;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 预留字段1
     */
    @ExcelProperty(value = "预留字段1")
    private String attr1;

    /**
     * 预留字段2
     */
    @ExcelProperty(value = "预留字段2")
    private String attr2;

    /**
     * 预留字段3
     */
    @ExcelProperty(value = "预留字段3")
    private Long attr3;

    /**
     * 预留字段4
     */
    @ExcelProperty(value = "预留字段4")
    private Long attr4;


}
