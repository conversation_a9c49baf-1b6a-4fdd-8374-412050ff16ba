package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import org.dromara.mes.domain.BgAction;

import java.io.Serial;
import java.io.Serializable;



/**
 * 报工动作视图对象 bg_action
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = BgAction.class)
public class BgActionVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String id;

    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    private Long sortNum;

    /**
     * 动作代码
     */
    @ExcelProperty(value = "动作代码")
    private String actionCode;

    /**
     * 动作名称
     */
    @ExcelProperty(value = "动作名称")
    private String actionName;

    /**
     * 部门代码
     */
    @ExcelProperty(value = "部门代码")
    private String deptCode;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    private String deptName;

    /**
     * 在用
     */
    @ExcelProperty(value = "在用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String isUse;

    /**
     * 需同步SAP
     */
    @ExcelProperty(value = "需同步SAP", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String isSap;

    /**
     * 需同步Master
     */
    @ExcelProperty(value = "需同步Master", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String isMaster;

    /**
     * 需要机床
     */
    @ExcelProperty(value = "需要机床", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String needMachin;

    /**
     * 需单独关单
     */
    @ExcelProperty(value = "需单独关单", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String needClose;

    /**
     * 外发
     */
    @ExcelProperty(value = "外发", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String isOut;

    /**
     * 多次报工
     */
    @ExcelProperty(value = "多次报工", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String isMult;


}
