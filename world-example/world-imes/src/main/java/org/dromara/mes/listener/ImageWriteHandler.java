package org.dromara.mes.listener;

import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.write.handler.CellWriteHandler;
import cn.idev.excel.write.handler.context.CellWriteHandlerContext;
import cn.idev.excel.write.metadata.holder.WriteSheetHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.util.Units;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * 图片写入处理器
 * 精确控制Excel中图片的尺寸和单元格大小
 *
 * <AUTHOR>
 * @date 2024-07-01
 */
@Slf4j
public class ImageWriteHandler implements CellWriteHandler {

    private static final int URL_CONNECT_TIMEOUT = 2000;
    private static final int URL_READ_TIMEOUT = 6000;
    private static final double IMAGE_WIDTH_CM = 2.0; // 图片宽度（厘米）
    private static final double IMAGE_HEIGHT_CM = 2.0; // 图片高度（厘米）
    private static final double PIXELS_PER_CENTIMETER = 96.0 / 2.54; // 96 DPI下的厘米转像素比率
    private static final int MAX_COLUMN_WIDTH = 255 * 256; // 最大列宽限制
    private static final float MAX_ROW_HEIGHT = 409.5f; // 最大行高限制

    @Override
    public void afterCellDataConverted(CellWriteHandlerContext context) {
        if (context.getHead()) {
            return;
        }

        WriteCellData<?> cellData = context.getFirstCellData();
        Cell cell = context.getCell();

        // 获取单元格的值
        String value = cellData.getStringValue();
        if (StringUtils.hasText(value) && isValidUrl(value)) {
            try {
                URL url = new URL(value);
                byte[] imageData = getImageData(url);
                if (imageData != null) {
                    createImage(context.getWriteSheetHolder(), cell, imageData);
                }
            } catch (Exception e) {
                log.error("处理图片URL时发生错误: {}", value, e);
            }
        }
    }

    /**
     * 检查字符串是否是有效的URL
     */
    private boolean isValidUrl(String urlString) {
        try {
            new URL(urlString);
            return true;
        } catch (Exception e) {
            log.debug("无效的URL格式: {}", urlString);
            return false;
        }
    }

    /**
     * 获取图片数据（兼容Java 8的读取方式）
     */
    private byte[] getImageData(URL url) throws IOException {
        URLConnection connection = url.openConnection();
        connection.setConnectTimeout(URL_CONNECT_TIMEOUT);
        connection.setReadTimeout(URL_READ_TIMEOUT);
        try (InputStream inputStream = connection.getInputStream()) {
            return inputStream.readAllBytes();
        } catch (IOException e) {
            log.error("读取图片数据失败: {}", url, e);
            throw e;
        }
    }

    /**
     * 创建图片并自动调整单元格尺寸
     */
    private void createImage(WriteSheetHolder writeSheetHolder, Cell cell, byte[] imageData) {
        Sheet sheet = writeSheetHolder.getSheet();
        Workbook workbook = sheet.getWorkbook();
        Drawing<?> drawing = sheet.createDrawingPatriarch();

        try {
            // 添加图片到工作簿
            int pictureIdx = workbook.addPicture(imageData, Workbook.PICTURE_TYPE_JPEG);

            // 计算像素尺寸
            int pixelWidth = (int) Math.ceil(IMAGE_WIDTH_CM * PIXELS_PER_CENTIMETER);
            int pixelHeight = (int) Math.ceil(IMAGE_HEIGHT_CM * PIXELS_PER_CENTIMETER);

            // 自适应调整列宽和行高
            adjustCellDimensions(sheet, cell, pixelWidth, pixelHeight);

            // 创建锚点定位图片
            ClientAnchor anchor = createAnchor(drawing, cell, pixelWidth, pixelHeight);

            // 插入图片
            drawing.createPicture(anchor, pictureIdx);
        } catch (Exception e) {
            log.error("创建Excel图片失败", e);
        }
    }

    /**
     * 调整单元格的行高和列宽
     */
    private void adjustCellDimensions(Sheet sheet, Cell cell, int pixelWidth, int pixelHeight) {
        int colIndex = cell.getColumnIndex();
        int rowIndex = cell.getRowIndex();

        // 调整列宽（基于像素到Excel列宽单位的转换）
        int columnWidth = Math.min(
            (int) Math.ceil(256 * pixelWidth / 7.0), // 标准字符宽度7像素
            MAX_COLUMN_WIDTH
        );
        sheet.setColumnWidth(colIndex, columnWidth);

        // 调整行高（像素转磅值）
        float rowHeight = Math.min(
            (float) (pixelHeight * 72.0 / 96), // 1像素=72/96磅
            MAX_ROW_HEIGHT
        );
        Row row = sheet.getRow(rowIndex);
        if (row == null) {
            row = sheet.createRow(rowIndex);
        }
        row.setHeightInPoints(rowHeight);
    }

    /**
     * 创建精确定位的锚点对象
     */
    private ClientAnchor createAnchor(Drawing<?> drawing, Cell cell, int pixelWidth, int pixelHeight) {
        // 创建初始锚点
        ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, cell.getColumnIndex(), cell.getRowIndex(), cell.getColumnIndex() + 1, cell.getRowIndex() + 1);

        // 设置精确尺寸（单位转换：像素->EMU->Excel单位）
        if (anchor instanceof ClientAnchor) {
            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
            anchor.setDx1(0);
            anchor.setDy1(0);
            anchor.setDx2(Units.pixelToEMU(pixelWidth));
            anchor.setDy2(Units.pixelToEMU(pixelHeight));
        }
        return anchor;
    }
}
