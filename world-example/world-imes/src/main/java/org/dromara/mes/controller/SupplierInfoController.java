package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.SupplierInfoBo;
import org.dromara.mes.domain.vo.SupplierInfoVo;
import org.dromara.mes.service.ISupplierInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 供应商信息
 * 前端访问路由地址为:/mes/supplier
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/supplier")
public class SupplierInfoController extends BaseController {

    private final ISupplierInfoService supplierInfoService;

    /**
     * 查询供应商信息列表
     */
    @SaCheckPermission("mes:supplier:list")
    @GetMapping("/list")
    public TableDataInfo<SupplierInfoVo> list(SupplierInfoBo bo, PageQuery pageQuery) {
        return supplierInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出供应商信息列表
     */
    @SaCheckPermission("mes:supplier:export")
    @Log(title = "供应商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SupplierInfoBo bo, HttpServletResponse response) {
        List<SupplierInfoVo> list = supplierInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商信息", SupplierInfoVo.class, response);
    }

    /**
     * 获取供应商信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:supplier:query")
    @GetMapping("/{id}")
    public R<SupplierInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(supplierInfoService.queryById(id));
    }

    /**
     * 新增供应商信息
     */
    @SaCheckPermission("mes:supplier:add")
    @Log(title = "供应商信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SupplierInfoBo bo) {
        if (!supplierInfoService.checkSupplierCodeUnique(bo)) {
            return R.fail("修改参数'" + bo.getSupplierCode() + "'失败，供应商编码已存在");
        }
        return toAjax(supplierInfoService.insertByBo(bo));
    }

    /**
     * 修改供应商信息
     */
    @SaCheckPermission("mes:supplier:edit")
    @Log(title = "供应商信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SupplierInfoBo bo) {
        if (!supplierInfoService.checkSupplierCodeUnique(bo)) {
            return R.fail("修改参数'" + bo.getSupplierCode() + "'失败，供应商编码已存在");
        }
        return toAjax(supplierInfoService.updateByBo(bo));
    }

    /**
     * 删除供应商信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:supplier:remove")
    @Log(title = "供应商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(supplierInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 同步SAP数据到供应商信息
     *
     * @return com.alibaba.nacos.common.model.RestResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/4/11 16:54
     */
    @GetMapping( "/syncSapToSupplier")
    @Log(title = "供应商信息", businessType = BusinessType.SYNC)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "{repeat.submit.message}")
    public R<Void> syncSapToSupplier() {
        String tenantId = TenantHelper.getTenantId();
        // 调用异步方法
        CompletableFuture<Boolean> future = supplierInfoService.syncSapToSupplierAsync(tenantId);
        Boolean result = false;
        try {
            result = future.get(1, TimeUnit.MINUTES);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            R.fail("等待同步结果时发生错误", e);
        }
        return toAjax(result);
    }
}
