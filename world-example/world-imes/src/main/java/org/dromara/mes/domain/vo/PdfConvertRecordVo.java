package org.dromara.mes.domain.vo;

import org.dromara.mes.domain.PdfConvertRecord;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * PDF转换记录视图对象 pdf_convert_record
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PdfConvertRecord.class)
public class PdfConvertRecordVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNumber;

    /**
     * NO号
     */
    @ExcelProperty(value = "NO号")
    private String noNumber;

    /**
     * 原始文件名
     */
    @ExcelProperty(value = "原始文件名")
    private String originalFileName;

    /**
     * 转换后文件名
     */
    @ExcelProperty(value = "转换后文件名")
    private String convertedFileName;

    /**
     * ZIP文件路径（MinIO）
     */
    @ExcelProperty(value = "ZIP文件路径", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "M=inIO")
    private String zipFilePath;

    /**
     * ZIP文件URL
     */
    @ExcelProperty(value = "ZIP文件URL")
    private String zipFileUrl;

    /**
     * 处理状态：0-处理中，1-成功，2-失败
     */
    @ExcelProperty(value = "处理状态：0-处理中，1-成功，2-失败")
    private Long status;

    /**
     * 错误信息
     */
    @ExcelProperty(value = "错误信息")
    private String errorMessage;

    /**
     * 文件大小（字节）
     */
    @ExcelProperty(value = "文件大小", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "字=节")
    private Long fileSize;


}
