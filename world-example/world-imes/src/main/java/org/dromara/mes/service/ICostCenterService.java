package org.dromara.mes.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.bo.CostCenterBo;
import org.dromara.mes.domain.vo.CostCenterVo;

import java.util.Collection;
import java.util.List;

/**
 * 售后成本中心Service接口
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface ICostCenterService {

    /**
     * 查询售后成本中心
     *
     * @param id 主键
     * @return 售后成本中心
     */
    CostCenterVo queryById(String id);

    /**
     * 分页查询售后成本中心列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 售后成本中心分页列表
     */
    TableDataInfo<CostCenterVo> queryPageList(CostCenterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的售后成本中心列表
     *
     * @param bo 查询条件
     * @return 售后成本中心列表
     */
    List<CostCenterVo> queryList(CostCenterBo bo);

    /**
     * 新增售后成本中心
     *
     * @param bo 售后成本中心
     * @return 是否新增成功
     */
    Boolean insertByBo(CostCenterBo bo);

    /**
     * 修改售后成本中心
     *
     * @param bo 售后成本中心
     * @return 是否修改成功
     */
    Boolean updateByBo(CostCenterBo bo);

    /**
     * 校验并批量删除售后成本中心信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
