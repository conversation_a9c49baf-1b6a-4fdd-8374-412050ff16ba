package org.dromara.mes.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工艺路线对象 pro_route
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_route")
public class ProRoute extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工艺路线ID
     */
    @TableId(value = "route_id")
    private Long routeId;

    /**
     * 工艺路线编号
     */
    private String routeCode;

    /**
     * 工艺路线名称
     */
    private String routeName;

    /**
     * 工艺路线说明
     */
    private String routeDesc;

    /**
     * 是否启用
     */
    private String enableFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
