package org.dromara.mes.listener;

import cn.idev.excel.context.AnalysisContext;
import org.dromara.common.core.utils.ValidatorUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.excel.core.DefaultExcelListener;
import org.dromara.mes.domain.vo.GoodsDeclarationVo;

/**
 * <AUTHOR>
 * @date 2025-06-04- 09:15
 */
public class ExcelGoodsDataListener extends DefaultExcelListener<GoodsDeclarationVo> {

    public ExcelGoodsDataListener() {
        // 显示使用构造函数，否则将导致空指针
        super(true);
    }

    @Override
    public void invoke(GoodsDeclarationVo data, AnalysisContext context) {
        // 先校验必填
        ValidatorUtils.validate(data, AddGroup.class);
        // 获取数据所在行的行号，后续和文件中的图片数据做匹配使用
        Integer rowIndex = context.readRowHolder().getRowIndex();
        data.setRowId(rowIndex);
        // 添加到处理结果中
        getExcelResult().getList().add(data);
    }

}
