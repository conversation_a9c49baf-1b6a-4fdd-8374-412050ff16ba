package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnore;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.mes.domain.GoodsDeclaration;
import org.dromara.mes.listener.SxjgUrlImageConverter;

import java.io.Serial;
import java.io.Serializable;
import java.net.URL;
import java.util.Date;


/**
 * 备件备案信息视图对象 goods_declaration
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@ExcelIgnoreUnannotated
@AllArgsConstructor
@NoArgsConstructor
@AutoMapper(target = GoodsDeclaration.class)
public class GoodsDeclarationVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
//    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 该字段用于保存数据所在行的行号
     */
//    @ExcelIgnore
    private Integer rowId;


    /**
     * MO号
     */
    @ExcelProperty(value = "MO")
    @NotEmpty(message = "MO号不能为空", groups = AddGroup.class)
    private String mo;

    /**
     * 单台净重（Net weigh）
     */
    @ExcelProperty(value = "单台净重(kg)")
    // @ExcelProperty(value = "单台净重", converter = ExcelDictConvert.class)
    // @ExcelDictFormat(readConverterExp = "N=et,w=eigh")
    // @NotNull(message = "净重不能为空")
    private Double netWeight;

    /**
     * 总净重/KG
     */
    @ExcelProperty(value = "总净重/KG")
    private Double totalNetWeight;

    /**
     * 货物图片（picture）
     */
    @ExcelProperty(value = "货物图片URL")
    @ExcelIgnore
    private String goodsPicture;


    @ExcelProperty(value = {"货物图片 picture"}, converter = SxjgUrlImageConverter.class)
    private URL goodsPictureUrl;
    /**
     * 型号图片
     */
    @ExcelProperty(value = "型号图片URL")
    @ExcelIgnore
    private String modelPicture;

    @ExcelProperty(value = {"型号图片"}, converter = SxjgUrlImageConverter.class)
    private URL modelPictureUrl;

    /**
     * 标签_名牌图片（nameplate picture）
     */
    @ExcelProperty(value = {"标签，名牌图片"}, converter = SxjgUrlImageConverter.class)
    private URL nameplatePictureUrl;

    @ExcelProperty(value = "标签，名牌图片URL")
    @ExcelIgnore
    private String nameplatePicture;


    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remarks;

    /**
     * 申报品名
     */
    @ExcelProperty(value = "申报品名")
//    @NotEmpty(message = "申报品名", groups = AddGroup.class)
    private String declarationName;

    /**
     * HS CODE
     */
    @ExcelProperty(value = "HS CODE")
//    @NotEmpty(message = "HS CODE不能为空", groups = AddGroup.class)
    private String hsCode;

    /**
     * 申报要素
     */
    @ExcelProperty(value = "申报要素")
    private String declarationElements;

    @ExcelProperty("料号")
    @NotEmpty(message = "料号不能为空", groups = AddGroup.class)
    private String materialNumber;

    @ExcelProperty("零件号")
    private String partNumber;

    @ExcelProperty("中文品名 Chinese Name")
    private String chineseName;

    @ExcelProperty("英文品名 English Name")
    private String englishName;

    @ExcelProperty("生产原厂")
    private String manufacturer;

    @ExcelProperty("品牌")
    private String brand;

    @ExcelProperty("型号")
    private String model;

    @ExcelProperty("序列")
    private String serialNumber;

    @ExcelProperty("设备情况")
    private String equipmentCondition;

    @ExcelProperty("原产地 Origin")
    private String origin;

    @ExcelProperty("生产/购买年月")
    private String productionDate;

    @ExcelProperty("数量")
    private Integer quantity;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("单价")
    private Double unitPrice;
    /**
     * 总价（数量*单价）
     */
    @ExcelProperty("总价")
    private Double totalAmount;

    @ExcelProperty("材质(非设备)")
    private String material;

    @ExcelProperty("尺寸")
    private String dimension;

    @ExcelProperty("工作原理（设备）")
    private String workingPrinciple;

    @ExcelProperty("用途（中英文）")
    private String functionEn;

    @ExcelProperty("功能")
    private String functionality;

    @ExcelProperty("功率（如有）")
    private String power;

    @ExcelProperty("电压（如有）")
    private String voltage;

    @ExcelProperty("加工方法")
    private String processingMethod;

    @ExcelProperty("是否有接头")
    private String hasConnector;

    @ExcelProperty("结构类型")
    private String structureType;

    @ExcelProperty("胶管类的需确认以下5项")
    private String hydraulicHose;

    @ExcelProperty("橡胶类的需确认以下5项")
    private String rubberMaterial;

    private Date createTime;

    private Long createBy;

    /**
     * 上传人名称
     */
    @Translation(type = TransConstant.USER_ID_TO_NAME, mapper = "createBy")
    private String createByName;

    @ExcelProperty(value = "状态")
    @ExcelDictFormat(dictType = "good_status")
    //0草稿，1已提交，2归档，3退回，默认为0
    private String status;
    /**
     * 审批意见
     */
    @ExcelProperty(value = "审批意见")
    private String approvalOpinion;

    /**
     * 审批人
     */
    @ExcelProperty(value = "审批人")
    private Long approver;

    /**
     * 审批时间
     */
    @ExcelProperty(value = "审批时间")
    private Date approvalTime;

    /**
     * 提交人
     */
    private Long submitter;

    /**
     * 提交时间
     */
    private Date submitTime;


}
