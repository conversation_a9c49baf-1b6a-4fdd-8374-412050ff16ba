package org.dromara.mes.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.http.ContentType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.PictureData;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.*;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.IdUtil;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.mes.domain.GoodsDeclaration;
import org.dromara.mes.domain.bo.GoodsDeclarationBo;
import org.dromara.mes.domain.vo.GoodsDeclarationVo;
import org.dromara.mes.mapper.GoodsDeclarationMapper;
import org.dromara.mes.service.IGoodsDeclarationService;
import org.dromara.resource.api.RemoteFileService;
import org.dromara.system.api.RemoteRoleService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 备件备案信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class GoodsDeclarationServiceImpl implements IGoodsDeclarationService {

    private final GoodsDeclarationMapper baseMapper;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteRoleService remoteRoleService;
    @DubboReference
    private RemoteFileService remoteFileService;

    /**
     * 查询备件备案信息
     *
     * @param id 主键
     * @return 备件备案信息
     */
    @Override
    public GoodsDeclarationVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询备件备案信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 备件备案信息分页列表
     */
    @Override
    public TableDataInfo<GoodsDeclarationVo> queryPageList(GoodsDeclarationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GoodsDeclaration> lqw = buildQueryWrapper(bo);
        Page<GoodsDeclarationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的备件备案信息列表
     *
     * @param bo 查询条件
     * @return 备件备案信息列表
     */
    @Override
    public List<GoodsDeclarationVo> queryList(GoodsDeclarationBo bo) {
        LambdaQueryWrapper<GoodsDeclaration> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<GoodsDeclaration> buildQueryWrapper(GoodsDeclarationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<GoodsDeclaration> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(GoodsDeclaration::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMo()), GoodsDeclaration::getMo, bo.getMo());
        lqw.like(StringUtils.isNotBlank(bo.getDeclarationName()), GoodsDeclaration::getDeclarationName, bo.getDeclarationName());
        lqw.eq(StringUtils.isNotBlank(bo.getHsCode()), GoodsDeclaration::getHsCode, bo.getHsCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDeclarationElements()), GoodsDeclaration::getDeclarationElements, bo.getDeclarationElements());
        lqw.eq(StringUtils.isNotBlank(bo.getMaterialNumber()), GoodsDeclaration::getMaterialNumber, bo.getMaterialNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getPartNumber()), GoodsDeclaration::getPartNumber, bo.getPartNumber());
        lqw.like(StringUtils.isNotBlank(bo.getChineseName()), GoodsDeclaration::getChineseName, bo.getChineseName());
        lqw.like(StringUtils.isNotBlank(bo.getBrand()), GoodsDeclaration::getBrand, bo.getBrand());
        lqw.eq(StringUtils.isNotBlank(bo.getHydraulicHose()), GoodsDeclaration::getHydraulicHose, bo.getHydraulicHose());
        lqw.eq(StringUtils.isNotBlank(bo.getRubberMaterial()), GoodsDeclaration::getRubberMaterial, bo.getRubberMaterial());
        // 添加审批状态查询条件
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), GoodsDeclaration::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增备件备案信息
     *
     * @param bo 备件备案信息
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(GoodsDeclarationBo bo) {
        GoodsDeclaration add = MapstructUtils.convert(bo, GoodsDeclaration.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改备件备案信息
     *
     * @param bo 备件备案信息
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(GoodsDeclarationBo bo) {
        GoodsDeclaration update = MapstructUtils.convert(bo, GoodsDeclaration.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(GoodsDeclaration entity) {
        // TODO 做一些数据校验,如唯一约束
        // 检查当前状态是否为草稿/退回
        Assert.isTrue("0".equals(entity.getStatus()) || "3".equals(entity.getStatus()), "只能修改草稿/退回状态的记录");
    }

    /**
     * 校验并批量删除备件备案信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        log.info("开始批量删除备件备案信息，ID列表: {}, 是否校验: {}", ids, isValid);

        if (isValid) {
            // 校验记录是否存在且状态正确
            ids.forEach(id -> {
                GoodsDeclarationVo vo = baseMapper.selectVoById(id);
                if (vo != null) {
                    // 检查当前状态是否为草稿或退回
                    Assert.isTrue("0".equals(vo.getStatus()) || "3".equals(vo.getStatus()), "只能删除草稿/退回状态的记录");
                } else {
                    throw new IllegalArgumentException("ID " + id + " 对应的记录不存在");
                }
            });
        }

        // 删除OSS中的图片文件
//        OssClient storage = OssFactory.instance("minio");
        List<String> list = new ArrayList<>();
        ids.forEach(id -> {
            GoodsDeclarationVo vo = baseMapper.selectVoById(id);
            if (vo != null) {
                String goodsPicture = vo.getGoodsPicture();
                String modelPicture = vo.getModelPicture();
                String nameplatePicture = vo.getNameplatePicture();
                if (StringUtils.isNotBlank(goodsPicture)) {
//                    storage.delete(goodsPicture);
                    list.add(goodsPicture);
                }
                if (StringUtils.isNotBlank(modelPicture)) {
//                    storage.delete(modelPicture);
                    list.add(modelPicture);
                }
                if (StringUtils.isNotBlank(nameplatePicture)) {
//                    storage.delete(nameplatePicture);
                    list.add(nameplatePicture);
                }

            }
        });
        // 批量删除OSS中的文件
        if (!list.isEmpty()) {
            remoteFileService.deleteSysOssByUrl(String.join(StringUtils.SEPARATOR, list));
        }
        // 执行删除
        int deleteCount = baseMapper.deleteByIds(ids);
        log.info("批量删除完成，删除记录数: {}", deleteCount);

        return deleteCount > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveBatch(List<GoodsDeclaration> list) {
        baseMapper.insertBatch(list);
    }

    /**
     * 根据MO列表查询申报单信息
     *
     * @param list 包含MO信息的申报单列表，不可为null但可为空
     * @return 匹配的申报单VO列表，无匹配时返回空列表
     */
    @Override
    public List<GoodsDeclarationVo> getListByMo(List<GoodsDeclaration> list) {
        log.debug("查询申报单信息，输入列表大小: {}", list == null ? "null" : list.size());

        LambdaQueryWrapper<GoodsDeclaration> wrapper = new LambdaQueryWrapper<GoodsDeclaration>();

        // 处理MO列表
        if (list != null && !list.isEmpty()) {
            List<String> moList = list.stream().map(GoodsDeclaration::getMo).filter(Objects::nonNull).distinct().collect(Collectors.toList());

            if (!moList.isEmpty()) {
                wrapper.in(GoodsDeclaration::getMo, moList);
                log.debug("查询条件中包含{}个MO", moList.size());
            }
        }

        // 执行查询
        List<GoodsDeclarationVo> result = baseMapper.selectVoList(wrapper);
        log.debug("查询到{}条申报单信息", result.size());

        return result;
    }

    @Override
    public R<Void> processImportData(MultipartFile file, List<GoodsDeclarationVo> volist) {
        Assert.isTrue(volist.size() <= 50, "导入数据不能超过50条");
        // 获取所有需要处理的记录
        List<GoodsDeclaration> list = BeanUtil.copyToList(volist, GoodsDeclaration.class);
        // 查询所有已存在的记录
        List<GoodsDeclarationVo> existingList = getListByMo(list);

        // 创建现有记录的映射，用于快速查找
        Map<String, GoodsDeclarationVo> existingMap = existingList.stream().collect(Collectors.toMap(GoodsDeclarationVo::getMo, Function.identity()));

        // 需要插入的数据
        List<GoodsDeclarationVo> insertList = new ArrayList<>();

        // 处理每条记录
        for (GoodsDeclaration declaration : list) {
            GoodsDeclarationVo existing = existingMap.get(declaration.getMo());
            if (existing != null) {
                if (!existing.getStatus().equals("0")) {
                    break;
                }
                // 保留原有的图片信息
                declaration.setId(existing.getId());
                declaration.setGoodsPicture(existing.getGoodsPicture());
                declaration.setModelPicture(existing.getModelPicture());
                declaration.setNameplatePicture(existing.getNameplatePicture());
                // 更新其他字段
                updateByBo(BeanUtil.copyProperties(declaration, GoodsDeclarationBo.class));
            } else {
                // 新记录，处理图片
                GoodsDeclarationVo vo = volist.stream().filter(v -> v.getMo().equals(declaration.getMo())).findFirst().orElse(null);
                if (vo != null) {
                    // 处理图片
                    processImagesForRow(file, vo);
                    insertList.add(vo);
                }
            }
        }

        // 判断insertList不为null
        if (!insertList.isEmpty()) {
            // 保存新记录
            List<GoodsDeclaration> listUp = BeanUtil.copyToList(insertList, GoodsDeclaration.class);
            saveBatch(listUp);
        }

        return R.ok();
    }

    /**
     * 处理单行数据的图片
     *
     * @param file Excel文件
     * @param vo   数据对象
     */
    private void processImagesForRow(MultipartFile file, GoodsDeclarationVo vo) {
        try (InputStream inputStream = file.getInputStream()) {
            Workbook workbook = new XSSFWorkbook(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            Map<Integer, Map<Integer, PictureData>> pictureMap = getPictures(sheet);

            // 定义列号与字段的映射关系
            Map<Integer, String> columnFieldMap = new HashMap<>();
            // 第30列是商品图片
            columnFieldMap.put(29, "goodsPicture");
            // 第31列是型号图片
            columnFieldMap.put(30, "modelPicture");
            // 第32列是铭牌图片
            columnFieldMap.put(31, "nameplatePicture");

            OssClient storage = OssFactory.instance("minio");

            // 获取当前行的图片
            Map<Integer, PictureData> rowPictures = pictureMap.get(vo.getRowId());
            if (rowPictures != null) {
                for (Map.Entry<Integer, PictureData> pictureEntry : rowPictures.entrySet()) {
                    int colId = pictureEntry.getKey();
                    PictureData pictureData = pictureEntry.getValue();
                    byte[] pictureBytes = pictureData.getData();

                    // 生成唯一的文件名
                    String fileName = DateUtil.today() + "/" + IdUtil.simpleUUID() + getImageSuffix(pictureData);

                    // 上传图片到OSS
                    UploadResult uploadResult = storage.uploadSuffix(pictureBytes, getImageSuffix(pictureData), fileName, Constants.BUCKET_EXCLE_BARCODE, ContentType.OCTET_STREAM.getValue());

                    // 根据列号设置对应的字段
                    String fieldName = columnFieldMap.get(colId);
                    if (fieldName != null) {
                        switch (fieldName) {
                            case "goodsPicture" -> vo.setGoodsPicture(uploadResult.getUrl());
                            case "modelPicture" -> vo.setModelPicture(uploadResult.getUrl());
                            case "nameplatePicture" -> vo.setNameplatePicture(uploadResult.getUrl());
                        }
                    }

                    log.info("Processed image - Row: {}, Column: {}, Field: {}, Size: {} bytes", vo.getRowId(), colId, fieldName, pictureBytes.length);
                }
            }
        } catch (IOException e) {
            log.error("Failed to process Excel images for row: " + vo.getRowId(), e);
            throw new RuntimeException("Failed to process Excel images: " + e.getMessage(), e);
        }
    }

    // 根据文件头信息，得到对应的文件后缀名
    private static String getImageSuffix(PictureData pictureData) {
        int pictureType = pictureData.getPictureType();
        return switch (pictureType) {
            case Workbook.PICTURE_TYPE_JPEG -> ".jpg";
            case Workbook.PICTURE_TYPE_PNG -> ".png";
            case Workbook.PICTURE_TYPE_DIB -> ".dib";
            case XSSFWorkbook.PICTURE_TYPE_GIF -> ".gif";
            case Workbook.PICTURE_TYPE_EMF -> ".emf";
            case Workbook.PICTURE_TYPE_WMF -> ".wmf";
            case Workbook.PICTURE_TYPE_PICT -> ".pict";
            case XSSFWorkbook.PICTURE_TYPE_WPG -> ".wpg";
            case XSSFWorkbook.PICTURE_TYPE_TIFF -> ".tif";
            case XSSFWorkbook.PICTURE_TYPE_EPS -> ".eps";
            case XSSFWorkbook.PICTURE_TYPE_BMP -> ".bmp";
            default -> "未知后缀名";
        };
    }

    // 处理图片
    private Map<Integer, Map<Integer, PictureData>> getPictures(Sheet sheet) {
        Map<Integer, Map<Integer, PictureData>> pictureMap = new HashMap<>();
        Drawing<?> drawing = sheet.getDrawingPatriarch();
        if (drawing == null) {
            drawing = sheet.createDrawingPatriarch();
        }
        if (drawing instanceof XSSFDrawing xssfDrawing) {
            for (XSSFShape shape : xssfDrawing.getShapes()) {
                if (shape instanceof XSSFPicture picture) {
                    XSSFClientAnchor anchor = picture.getClientAnchor();
                    int row = anchor.getRow1();
                    short col = anchor.getCol1();

                    log.debug("Found image - Row: {}, Column: {}", row, col);
                    PictureData pictureData = picture.getPictureData();
                    if (pictureMap.containsKey(row)) {
                        Map<Integer, PictureData> integerPictureDataMap = pictureMap.get(row);
                        integerPictureDataMap.put((int) col, pictureData);
                        pictureMap.put(row, integerPictureDataMap);
                    } else {
                        Map<Integer, PictureData> pictureSubMap = new HashMap<>();
                        pictureSubMap.put((int) col, pictureData);
                        pictureMap.put(row, pictureSubMap);
                    }
                }
            }
        }
        return pictureMap;
    }

    @Override
    public List<GoodsDeclarationVo> exportData(GoodsDeclarationBo bo) {
        // 查询数据
        List<GoodsDeclarationVo> list = queryList(bo);
        Assert.isTrue(list.size() <= 50, "导出数据不能超过50条");
        for (GoodsDeclarationVo vo : list) {
            try {
                // 处理商品图片
                if (StringUtils.isNotEmpty(vo.getGoodsPicture())) {
                    String filePath = vo.getGoodsPicture();
                    vo.setGoodsPictureUrl(new URL(filePath));
                }
                // 处理型号图片
                if (StringUtils.isNotEmpty(vo.getModelPicture())) {
                    String filePath = vo.getModelPicture();
                    vo.setModelPictureUrl(new URL(filePath));
                }
                // 处理铭牌图片
                if (StringUtils.isNotEmpty(vo.getNameplatePicture())) {
                    String filePath = vo.getNameplatePicture();
                    vo.setNameplatePictureUrl(new URL(filePath));
                }
                // 计算总价
                if (vo.getQuantity() != null && vo.getUnitPrice() != null) {
                    vo.setTotalAmount(vo.getQuantity() * vo.getUnitPrice());
                } else {
                    vo.setTotalAmount(null);
                }
            } catch (Exception e) {
                log.error("处理图片URL失败", e);
            }
        }
        return list;
    }

    // 工具方法：根据角色code查所有用户邮箱
    private List<String> getEmailsByRoleKey(String roleKey) {
        Long roleId = remoteRoleService.selectRoleIdByRoleKey(roleKey);
        List<RemoteUserVo> roleUsers = remoteUserService.selectUsersByRoleIds(List.of(roleId));
        if (roleUsers == null || roleUsers.isEmpty()) return Collections.emptyList();
        Set<String> emails = new HashSet<>();
        for (RemoteUserVo user : roleUsers) {
            if (user != null && org.dromara.common.core.utils.StringUtils.isNotBlank(user.getEmail())) {
                emails.add(user.getEmail());
            }
        }
        return new ArrayList<>(emails);
    }

    // 根据userId查邮箱
    private String getEmailByUserId(Long userId) {
        return remoteUserService.selectEmailById(userId);
    }

    // 根据userId查昵称
    private String getUserNameByUserId(Long userId) {
        return remoteUserService.selectNicknameById(userId);
    }

    // 美观的HTML邮件模板
    private String buildSubmitMailHtml(String submitter, int count) {
        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#1890ff;margin:0;font-size:22px;letter-spacing:1px;\">备案信息提交通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">提交人：</span>"
            + "<b style=\"color:#222;\">" + submitter + "</b>，已提交"
            + "<b style=\"color:#fa541c;font-size:18px;\">" + count + "</b>项备案信息。</p>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#1890ff;\">请及时核对并完善相关备案信息，谢谢！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }

    private String buildArchiveMailHtml(String materialNumber) {
        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#52c41a;margin:0;font-size:22px;letter-spacing:1px;\">备案信息归档通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">料号：</span>"
            + "<b style=\"color:#222;font-size:18px;\">" + materialNumber + "</b>，已归档！</p>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#52c41a;\">备案信息已审核通过并归档，感谢您的配合！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }

    private String buildRejectMailHtml(String materialNumber, String reason) {
        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#fa541c;margin:0;font-size:22px;letter-spacing:1px;\">备案信息退回通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">料号：</span>"
            + "<b style=\"color:#222;font-size:18px;\">" + materialNumber + "</b>，审核退回</p>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#888;\">退回原因：</span>"
            + "<span style=\"color:#fa541c;font-weight:bold;\">" + reason + "</span></p>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#fa541c;\">请修改备案信息之后再重新提交审核！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }

    // 批量归档邮件模板
    private String buildBatchArchiveMailHtml(List<String> materialNumbers) {
        StringBuilder materialList = new StringBuilder();
        for (String materialNumber : materialNumbers) {
            materialList.append("<li style=\"margin:4px 0;color:#222;\">").append(materialNumber).append("</li>");
        }

        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#52c41a;margin:0;font-size:22px;letter-spacing:1px;\">备案信息归档通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">以下</span>"
            + "<b style=\"color:#fa541c;font-size:18px;\">" + materialNumbers.size() + "</b>"
            + "<span style=\"color:#888;\">项备案信息已归档：</span></p>"
            + "<ul style=\"margin:16px 0;padding-left:20px;color:#333;\">"
            + materialList.toString()
            + "</ul>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#52c41a;\">备案信息已审核通过并归档，感谢您的配合！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }

    // 批量退回邮件模板
    private String buildBatchRejectMailHtml(List<String> materialNumbers, String reason) {
        StringBuilder materialList = new StringBuilder();
        for (String materialNumber : materialNumbers) {
            materialList.append("<li style=\"margin:4px 0;color:#222;\">").append(materialNumber).append("</li>");
        }

        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#fa541c;margin:0;font-size:22px;letter-spacing:1px;\">备案信息退回通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">以下</span>"
            + "<b style=\"color:#fa541c;font-size:18px;\">" + materialNumbers.size() + "</b>"
            + "<span style=\"color:#888;\">项备案信息审核退回：</span></p>"
            + "<ul style=\"margin:16px 0;padding-left:20px;color:#333;\">"
            + materialList
            + "</ul>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#888;\">退回原因：</span>"
            + "<span style=\"color:#fa541c;font-weight:bold;\">" + reason + "</span></p>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#fa541c;\">请修改备案信息之后再重新提交审核！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }

    // 批量提交审批
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitForApproval(Collection<Long> ids) {
        boolean allSuccess = true;
        String submitter = getUserNameByUserId(LoginHelper.getUserId());
        // 先分组：收件人邮箱 -> 料号列表（此处为message_info角色所有用户）
        List<String> emails = getEmailsByRoleKey("message_info");
        List<String> materialNumbers = new ArrayList<>();
        for (Long id : ids) {
            GoodsDeclaration declaration = baseMapper.selectById(id);
            if (declaration == null) {
                throw new IllegalArgumentException("ID " + id + " 对应的记录不存在");
            }
            // 检查状态是否为草稿或退回
            if (!"0".equals(declaration.getStatus()) && !"3".equals(declaration.getStatus())) {
                throw new IllegalArgumentException("只有草稿或退回的声明才能提交审批");
            }
            declaration.setStatus("1");
            declaration.setSubmitter(LoginHelper.getUserId());
            declaration.setSubmitTime(new Date());
            baseMapper.updateById(declaration);
            materialNumbers.add(declaration.getMaterialNumber());
        }
        // 只发一封邮件，内容为所有料号
        if (!emails.isEmpty() && !materialNumbers.isEmpty()) {
            String subject = "备案信息提交通知";
            String html = buildBatchSubmitMailHtml(submitter, materialNumbers);
            MailUtils.sendHtml(emails, subject, html);
        }
        return allSuccess;
    }

    // 批量归档
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean archive(Collection<Long> ids) {
        boolean allSuccess = true;
        // 先分组：提交人邮箱 -> 料号列表
        Map<String, List<String>> submitterMaterialMap = new HashMap<>();
        for (Long id : ids) {
            GoodsDeclaration declaration = baseMapper.selectById(id);
            if (declaration == null || !"1".equals(declaration.getStatus())) {
                allSuccess = false;
                continue;
            }
            declaration.setStatus("2");
            declaration.setApprover(LoginHelper.getUserId());
            declaration.setApprovalTime(new Date());
            baseMapper.updateById(declaration);
            String submitterEmail = getEmailByUserId(declaration.getSubmitter());
            if (StringUtils.isNotBlank(submitterEmail)) {
                submitterMaterialMap.computeIfAbsent(submitterEmail, k -> new ArrayList<>())
                    .add(declaration.getMaterialNumber());
            }
        }
        // 统一发邮件，每个提交人只发一封，内容为所有料号
        for (Map.Entry<String, List<String>> entry : submitterMaterialMap.entrySet()) {
            String email = entry.getKey();
            List<String> materialNumbers = entry.getValue();
            String subject = "备案信息归档通知";
            String html = buildBatchArchiveMailHtml(materialNumbers);
            MailUtils.sendHtml(email, subject, html);
        }
        return allSuccess;
    }

    // 批量退回
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reject(Collection<Long> ids, String reason) {
        boolean allSuccess = true;
        // 先分组：提交人邮箱 -> 料号列表
        Map<String, List<String>> submitterMaterialMap = new HashMap<>();
        for (Long id : ids) {
            GoodsDeclaration declaration = baseMapper.selectById(id);
            if (declaration == null || !"1".equals(declaration.getStatus())) {
                allSuccess = false;
                continue;
            }
            declaration.setStatus("3");
            declaration.setApprovalOpinion(reason);
            declaration.setApprover(LoginHelper.getUserId());
            declaration.setApprovalTime(new Date());
            baseMapper.updateById(declaration);
            // 收集提交人和料号信息
            String submitterEmail = getEmailByUserId(declaration.getSubmitter());
            if (StringUtils.isNotBlank(submitterEmail)) {
                submitterMaterialMap.computeIfAbsent(submitterEmail, k -> new ArrayList<>())
                    .add(declaration.getMaterialNumber());
            }
        }
        // 统一发邮件，每个提交人只发一封，内容为所有料号
        for (Map.Entry<String, List<String>> entry : submitterMaterialMap.entrySet()) {
            String email = entry.getKey();
            List<String> materialNumbers = entry.getValue();
            String subject = "备案信息退回通知";
            String html = buildBatchRejectMailHtml(materialNumbers, reason);
            MailUtils.sendHtml(email, subject, html);
        }
        return allSuccess;
    }

    /**
     * 查询待审批列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 待审批列表
     */
    @Override
    public TableDataInfo<GoodsDeclarationVo> queryPendingApprovalList(GoodsDeclarationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<GoodsDeclaration> lqw = buildQueryWrapper(bo);
        // 只查询已提交状态的记录
        lqw.eq(GoodsDeclaration::getStatus, "1");
        Page<GoodsDeclarationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 审批修改申报信息
     *
     * @param bo 备件备案信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByDeclaration(GoodsDeclarationBo bo) {
        Long id = bo.getId();
        if (id == null) {
            throw new IllegalArgumentException("ID不能为空");
        }
        GoodsDeclaration update = baseMapper.selectById(id);
        update.setDeclarationElements(bo.getDeclarationElements());
        update.setHsCode(bo.getHsCode());
        update.setDeclarationName(bo.getDeclarationName());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 将图片数据转换为Base64字符串
     *
     * @param imageData 图片数据
     * @return Base64字符串
     */
    private String convertToBase64(byte[] imageData) {
        if (imageData == null || imageData.length == 0) {
            return null;
        }
        return "data:image/jpeg;base64," + Base64.getEncoder().encodeToString(imageData);
    }

    // 批量提交审批邮件模板
    private String buildBatchSubmitMailHtml(String submitter, List<String> materialNumbers) {
        StringBuilder materialList = new StringBuilder();
        for (String materialNumber : materialNumbers) {
            materialList.append("<li style=\"margin:4px 0;color:#222;\">").append(materialNumber).append("</li>");
        }
        return "<div style=\"max-width:600px;margin:0 auto;font-family:'Segoe UI',Arial,sans-serif;background:#fff;border-radius:8px;box-shadow:0 2px 8px #f0f1f2;padding:32px 32px 24px 32px;\">"
            + "<div style=\"border-bottom:1px solid #e6e6e6;padding-bottom:16px;margin-bottom:24px;\">"
            + "<h2 style=\"color:#1890ff;margin:0;font-size:22px;letter-spacing:1px;\">备案信息提交通知</h2>"
            + "</div>"
            + "<div style=\"font-size:16px;color:#333;line-height:1.8;\">"
            + "<p><span style=\"color:#888;\">提交人：</span>"
            + "<b style=\"color:#222;\">" + submitter + "</b>，已提交"
            + "<b style=\"color:#fa541c;font-size:18px;\">" + materialNumbers.size() + "</b>项备案信息：</p>"
            + "<ul style=\"margin:16px 0;padding-left:20px;color:#333;\">"
            + materialList.toString()
            + "</ul>"
            + "<p style=\"margin:8px 0 0 0;\"><span style=\"color:#1890ff;\">请及时核对并完善相关备案信息，谢谢！</span></p>"
            + "</div>"
            + "<div style=\"margin-top:32px;padding-top:16px;border-top:1px solid #f0f0f0;\">"
            + "<p style=\"color:#aaa;font-size:12px;margin:0;\">本邮件为系统自动发送，请勿回复。</p>"
            + "</div>"
            + "</div>";
    }
}
