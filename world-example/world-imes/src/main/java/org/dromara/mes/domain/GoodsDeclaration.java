package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 备件备案信息对象 goods_declaration
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("goods_declaration")
public class GoodsDeclaration extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * MO
     */
    private String mo;

    /**
     * 单台净重
     */
    private Double netWeight;

    /**
     * 货物图片
     */
    private String goodsPicture;

    /**
     * 型号图片
     */
    private String modelPicture;

    /**
     * 标签_名牌图片（nameplate picture）
     */
    private String nameplatePicture;

    /**
     * 备注
     */
    private String remarks;

    /**
     * 申报品名
     */
    private String declarationName;

    /**
     * HS CODE
     */
    private String hsCode;

    /**
     * 申报要素
     */
    private String declarationElements;


    /**
     * 料号
     */
    private String materialNumber;

    /**
     * 零件号
     */
    private String partNumber;

    /**
     * 中文品名 Chinese Name
     */
    private String chineseName;

    /**
     * 英文品名 English Name
     */
    private String englishName;

    /**
     * 生产原厂 manufacture
     */
    private String manufacturer;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 型号
     */
    private String model;

    /**
     * 序列
     */
    private String serialNumber;

    /**
     * 设备情况 （旧/新）
     */
    private String equipmentCondition;

    /**
     * 原产地 Origin
     */
    private String origin;

    /**
     * 生产/购买年月 Date
     */
    private String productionDate;

    /**
     * 数量
     */
    private Long quantity;

    /**
     * 单位
     */
    private String unit;

    /**
     * 单价
     */
    private Double unitPrice;

    /**
     * 材质(非设备)
     */
    private String material;

    /**
     * 尺寸
     */
    private String dimension;

    /**
     * 工作原理（设备） working principle
     */
    private String workingPrinciple;

    /**
     * 用途
     */
    private String functionEn;

    /**
     * 功能
     */
    private String functionality;

    /**
     * 功率（如有） Power
     */
    private String power;

    /**
     * 电压（如有） Voltage
     */
    private String voltage;

    /**
     * 加工方法
     */
    private String processingMethod;

    /**
     * 是否有接头 （针对线材类）
     */
    private String hasConnector;

    /**
     * 结构类型
     */
    private String structureType;

    /**
     * 总净重/KG
     */
    private Double totalNetWeight;

    /**
     * 胶管类的需确认以下5项
     */
    private String hydraulicHose;

    /**
     * 橡胶类的需确认以下5项
     */
    private String rubberMaterial;

    /**
     * 审批状态 0草稿，1已提交，2归档，3退回，默认为0
     */
    private String status;

    /**
     * 审批意见
     */
    private String approvalOpinion;

    /**
     * 审批人
     */
    private Long approver;

    /**
     * 审批时间
     */
    private Date approvalTime;

    /**
     * 提交人
     */
    private Long submitter;

    /**
     * 提交时间
     */
    private Date submitTime;

    /**
     * 总价（数量*单价）
     */
    @TableField(exist = false)
    private Double totalAmount;
}
