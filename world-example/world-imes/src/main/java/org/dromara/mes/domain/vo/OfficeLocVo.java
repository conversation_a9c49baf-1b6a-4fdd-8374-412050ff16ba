package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.mes.domain.OfficeLoc;

import java.io.Serial;
import java.io.Serializable;



/**
 * 工作地点组视图对象 office_loc
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OfficeLoc.class)
public class OfficeLocVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private String id;

    /**
     * 办事处代码
     */
    @ExcelProperty(value = "办事处代码")
    private String officeCode;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String officeDesc;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号")
    private String workNum;

    /**
     * 主管
     */
    @ExcelProperty(value = "主管")
    private String officeSupervisor;

    /**
     * 工作中心
     */
    @ExcelProperty(value = "工作中心")
    private String workCenter;

    /**
     * 办事处员工数
     */
    @ExcelProperty(value = "办事处员工数")
    private Integer totalUser;


}
