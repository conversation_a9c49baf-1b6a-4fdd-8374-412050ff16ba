package org.dromara.mes.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 工作中心对象 work_center
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("work_center")
public class WorkCenter extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 代号
     */
    private String code;

    /**
     * 说明
     */
    private String description;

    /**
     * 类型
     */
    private String type;

    /**
     * 公司代码
     */
    private String companyCode;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 人工费用
     */
    private Double laborCost;

    /**
     * 制造费用
     */
    private Double manufacturingCost;

    /**
     * 是否委外
     */
    private String isOutsourced;

    /**
     * 单价
     */
    private Long unitPrice;

    /**
     * 部门
     */
    private String completionDepartment;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    @TableLogic
    private String delFlag;

}
