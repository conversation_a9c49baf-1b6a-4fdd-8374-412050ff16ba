package org.dromara.mes.domain.bo;

import org.dromara.mes.domain.SupplierInfo;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 供应商信息业务对象 supplier_info
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = SupplierInfo.class, reverseConvertGenerate = false)
public class SupplierInfoBo extends BaseEntity {

    /**
     * 供应商ID
     */
    @NotNull(message = "供应商ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 供应商名称
     */
    @NotBlank(message = "供应商名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierName;

    /**
     * 供应商编号
     */
    @NotBlank(message = "供应商编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplierCode;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
