package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.service.IPdfBarcodeService;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * PDF条形码控制器
 * 前端访问路由地址为:/mes/pdfBarcode
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/pdfBarcode")
public class PdfBarcodeController extends BaseController {

    private final IPdfBarcodeService pdfBarcodeService;

    /**
     * 批量上传PDF文件并处理
     */
    @SaCheckPermission("mes:pdfBarcode:upload")
    @Log(title = "PDF条形码处理", businessType = BusinessType.INSERT)
    @PostMapping("/upload")
    public void uploadAndProcess(
        @RequestParam("files") MultipartFile[] files,
        @RequestParam(value = "barcodeHorizontal", defaultValue = "right") String barcodeHorizontal,
        @RequestParam(value = "barcodeX", defaultValue = "20") int barcodeX,
        @RequestParam(value = "barcodeY", defaultValue = "20") int barcodeY,
        @RequestParam(value = "barcodeWidth", defaultValue = "250") int barcodeWidth,
        @RequestParam(value = "barcodeHeight", defaultValue = "50") int barcodeHeight,
        HttpServletResponse response) {
        try {
            log.info("接收到PDF文件上传请求，文件数量: {}, 条形码位置: 水平={}, 边距={}, 上边距={}, 尺寸: {}x{}",
                files.length, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);

            // 处理PDF文件，传递条形码参数
            Map<String, Object> result = pdfBarcodeService.processPdfFiles(files, barcodeHorizontal, barcodeX, barcodeY, barcodeWidth, barcodeHeight);

            if ((Boolean) result.get("success")) {
                log.info("PDF文件处理成功，直接返回ZIP包");

                try {
                    // 设置响应头
                    response.setContentType("application/zip");
                    response.setHeader("Content-Disposition", "attachment; filename=\"pdf_barcode_" + System.currentTimeMillis() + ".zip\"");
                    response.setContentLength(((byte[]) result.get("zipBytes")).length);

                    // 写入ZIP包数据
                    byte[] zipData = (byte[]) result.get("zipBytes");
                    log.info("准备写入ZIP数据，大小: {} bytes", zipData.length);

                    response.getOutputStream().write(zipData);
                    response.getOutputStream().flush();
                    response.getOutputStream().close();

                    log.info("ZIP包数据写入完成");

                } catch (IOException e) {
                    log.error("写入ZIP包数据失败: {}", e.getMessage(), e);
                    throw e;
                }

            } else {
                log.error("PDF文件处理失败: {}", result.get("error"));
                response.setContentType("application/json");
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.getWriter().write("{\"success\":false,\"message\":\"" + result.get("error") + "\"}");
            }

        } catch (Exception e) {
            log.error("PDF文件处理异常: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.getWriter().write("{\"success\":false,\"message\":\"文件处理失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 下载处理结果ZIP包
     */
    @SaCheckPermission("mes:pdfBarcode:download")
    @Log(title = "PDF条形码下载", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void downloadResult(@RequestBody Map<String, Object> request, HttpServletResponse response) {
        try {
            String taskId = (String) request.get("taskId");
            log.info("接收到下载请求，任务ID: {}", taskId);

            // 重新处理文件（简化实现，实际应该从缓存或存储中获取）
            // 这里为了演示，我们返回一个错误提示
            response.setContentType(MediaType.TEXT_PLAIN_VALUE);
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.getWriter().write("请使用上传接口直接获取ZIP包下载");
            response.getWriter().flush();

        } catch (Exception e) {
            log.error("下载处理结果失败: {}", e.getMessage(), e);
            try {
                response.setContentType(MediaType.TEXT_PLAIN_VALUE);
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.getWriter().write("下载失败: " + e.getMessage());
                response.getWriter().flush();
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 查询处理状态
     */
    @SaCheckPermission("mes:pdfBarcode:query")
    @GetMapping("/status/{taskId}")
    public R<Map<String, Object>> getProcessStatus(@PathVariable String taskId) {
        try {
            log.info("查询处理状态，任务ID: {}", taskId);
            Map<String, Object> status = pdfBarcodeService.getProcessStatus(taskId);
            return R.ok(status);
        } catch (Exception e) {
            log.error("查询处理状态失败: {}", e.getMessage(), e);
            return R.fail("查询状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理临时文件
     */
    @SaCheckPermission("mes:pdfBarcode:cleanup")
    @Log(title = "PDF条形码清理", businessType = BusinessType.DELETE)
    @DeleteMapping("/cleanup/{taskId}")
    public R<Boolean> cleanupTempFiles(@PathVariable String taskId) {
        try {
            log.info("清理临时文件，任务ID: {}", taskId);
            boolean result = pdfBarcodeService.cleanupTempFiles(taskId);
            return R.ok(result);
        } catch (Exception e) {
            log.error("清理临时文件失败: {}", e.getMessage(), e);
            return R.fail("清理失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.ok("PDF条形码服务运行正常");
    }

    /**
     * 测试接口 - 返回简单的JSON响应
     */
    @PostMapping("/test")
    public R<Map<String, Object>> testUpload(@RequestParam("files") MultipartFile[] files) {
        try {
            log.info("测试接口接收到PDF文件上传请求，文件数量: {}", files.length);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("message", "测试成功，接收到 " + files.length + " 个文件");
            result.put("fileNames", Arrays.stream(files)
                .map(file -> file.getOriginalFilename())
                .collect(Collectors.toList()));

            return R.ok(result);
        } catch (Exception e) {
            log.error("测试接口异常: {}", e.getMessage(), e);
            return R.fail("测试失败: " + e.getMessage());
        }
    }

    /**
     * 简单测试接口 - 返回ZIP包
     */
    @PostMapping("/testZip")
    public void testZipResponse(HttpServletResponse response) {
        try {
            log.info("测试ZIP响应接口");

            // 创建一个简单的测试ZIP包
            byte[] testZipData = createTestZipData();

            // 设置响应头
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"test.zip\"");
            response.setContentLength(testZipData.length);

            // 写入测试ZIP数据
            response.getOutputStream().write(testZipData);
            response.getOutputStream().flush();
            response.getOutputStream().close();

            log.info("测试ZIP响应完成");

        } catch (Exception e) {
            log.error("测试ZIP响应失败: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding(StandardCharsets.UTF_8.name());
                response.getWriter().write("{\"success\":false,\"message\":\"测试ZIP响应失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("写入错误响应失败", ex);
            }
        }
    }

    /**
     * 创建测试ZIP数据
     */
    private byte[] createTestZipData() throws IOException {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(baos)) {

            // 添加一个测试文件
            ZipEntry entry = new ZipEntry("test.txt");
            zos.putNextEntry(entry);
            zos.write("这是一个测试文件".getBytes(StandardCharsets.UTF_8));
            zos.closeEntry();

            zos.finish();
            return baos.toByteArray();
        }
    }
}
