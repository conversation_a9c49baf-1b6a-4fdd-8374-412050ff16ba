package org.dromara.mes.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工序管理对象 pro_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_process")
public class ProProcess extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工序ID
     */
    @TableId(value = "process_id")
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 工艺要求
     */
    private String attention;

    /**
     * 工作中心
     */
    private String workCenter;

    /**
     * 工厂代码
     */
    private String factoryCode;

    /**
     * 是否启用
     */
    private String enableFlag;

    /**
     * 是否生成描述
     */
    private String descFlag;

    /**
     * 是否外托运
     */
    private String invoiceFlag;

    /**
     * 是否电镀
     */
    private String platingFlag;

    /**
     * 总机时是否为0
     */
    private String noYesHours;

    /**
     * 电镀类型
     */
    private String platingType;

    /**
     * 描述
     */
    private String description;

    /**
     * 急单等待时间
     */
    private Long urgWaitTime;

    /**
     * 急单等待时间
     */
    private Long waitTime;

    /**
     * 批量等待时间
     */
    private Long batchWaitTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
