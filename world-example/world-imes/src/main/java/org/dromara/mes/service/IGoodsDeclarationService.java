package org.dromara.mes.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.GoodsDeclaration;
import org.dromara.mes.domain.bo.GoodsDeclarationBo;
import org.dromara.mes.domain.vo.GoodsDeclarationVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 备件备案信息Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IGoodsDeclarationService {

    /**
     * 查询备件备案信息
     *
     * @param id 主键
     * @return 备件备案信息
     */
    GoodsDeclarationVo queryById(Long id);

    /**
     * 分页查询备件备案信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 备件备案信息分页列表
     */
    TableDataInfo<GoodsDeclarationVo> queryPageList(GoodsDeclarationBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的备件备案信息列表
     *
     * @param bo 查询条件
     * @return 备件备案信息列表
     */
    List<GoodsDeclarationVo> queryList(GoodsDeclarationBo bo);

    /**
     * 新增备件备案信息
     *
     * @param bo 备件备案信息
     * @return 是否新增成功
     */
    Boolean insertByBo(GoodsDeclarationBo bo);

    /**
     * 修改备件备案信息
     *
     * @param bo 备件备案信息
     * @return 是否修改成功
     */
    Boolean updateByBo(GoodsDeclarationBo bo);

    /**
     * 校验并批量删除备件备案信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量保存
     */
    void saveBatch(List<GoodsDeclaration> list);

    /**
     * 根据MO列表获取备件备案信息列表
     *
     * @param list 备件备案信息列表
     * @return 备件备案信息列表
     */
    List<GoodsDeclarationVo> getListByMo(List<GoodsDeclaration> list);

    /**
     * 处理Excel导入数据
     *
     * @param file   Excel文件
     * @param volist Excel数据列表
     * @return 处理结果
     */
    R<Void> processImportData(MultipartFile file, List<GoodsDeclarationVo> volist);

    /**
     * 导出备件备案信息数据（包含图片）
     *
     * @param bo 查询条件
     * @return 导出数据列表
     */
    List<GoodsDeclarationVo> exportData(GoodsDeclarationBo bo);

    /**
     * 提交审批
     *
     * @param ids 主键
     * @return 是否提交成功
     */
    Boolean submitForApproval(Collection<Long> ids);


    /**
     * 审批退回
     *
     * @param id              主键
     * @param approvalOpinion 审批意见
     * @return 是否退回成功
     */
    Boolean reject(Collection<Long> id, String approvalOpinion);

    /**
     * 归档
     *
     * @param ids 主键
     * @return 是否归档成功
     */
    Boolean archive(Collection<Long> ids);

    /**
     * 查询待审批列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 待审批列表
     */
    TableDataInfo<GoodsDeclarationVo> queryPendingApprovalList(GoodsDeclarationBo bo, PageQuery pageQuery);

    /**
     * 审批页面修改申报要素
     *
     * @param bo 备件备案信息
     * @return 是否修改成功
     */
    Boolean updateByDeclaration(GoodsDeclarationBo bo);
}
