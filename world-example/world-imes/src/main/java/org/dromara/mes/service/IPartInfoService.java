package org.dromara.mes.service;

import org.dromara.mes.domain.PartInfo;
import org.dromara.mes.domain.vo.PartInfoVo;
import org.dromara.mes.domain.bo.PartInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 物料档案信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface IPartInfoService {

    /**
     * 查询物料档案信息
     *
     * @param id 主键
     * @return 物料档案信息
     */
    PartInfoVo queryById(String id);

    /**
     * 新增物料档案信息
     *
     * @param bo 物料档案信息
     * @return 是否新增成功
     */
    Boolean insertByBo(PartInfoBo bo);

    /**
     * 修改物料档案信息
     *
     * @param bo 物料档案信息
     * @return 是否修改成功
     */
    Boolean updateByBo(PartInfoBo bo);

    /**
     * 校验并批量删除物料档案信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    /**
     * 分页查询物料档案信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 物料档案信息分页列表
     */
    TableDataInfo<PartInfoVo> selectPartInfoList(PartInfoBo bo, PageQuery pageQuery);

}
