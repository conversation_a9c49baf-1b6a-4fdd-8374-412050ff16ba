package org.dromara.mes.service.impl;

import cn.hutool.core.util.NumberUtil;
import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.sap.code.SapFunction;
import org.dromara.common.sap.util.SapUtils;
import org.dromara.mes.domain.WorkCenter;
import org.dromara.mes.domain.bo.WorkCenterBo;
import org.dromara.mes.domain.vo.WorkCenterVo;
import org.dromara.mes.mapper.WorkCenterMapper;
import org.dromara.mes.service.IWorkCenterService;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 工作中心Service业务层处理
 *
 * <AUTHOR>
 * {@code @date} 2025-04-11
 */
@Service
@Slf4j

public class WorkCenterServiceImpl implements IWorkCenterService {

    private final WorkCenterMapper baseMapper;
    private final RedissonClient redissonClient;

    @Autowired(required = false)
    public WorkCenterServiceImpl(WorkCenterMapper baseMapper, RedissonClient redissonClient) {
        this.baseMapper = baseMapper;
        this.redissonClient = redissonClient;
    }

    /**
     * 创建令牌桶限流器，每秒产生10个令牌
     */
    private static final RateLimiter RATE_LIMITER = RateLimiter.create(50.0);

    /**
     * 查询工作中心
     *
     * @param id 主键
     * @return 工作中心
     */
    @Override
    public WorkCenterVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询工作中心列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工作中心分页列表
     */
    @Override
    public TableDataInfo<WorkCenterVo> queryPageList(WorkCenterBo bo, PageQuery pageQuery) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkCenter> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), WorkCenter::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), WorkCenter::getDescription, bo.getDescription());
        if (StringUtils.isBlank(pageQuery.getOrderByColumn())) {
            lqw.orderByAsc(WorkCenter::getId);
        }
        lqw.between(params.get("beginTime") != null && params.get("endTime") != null,
            WorkCenter::getCreateTime, params.get("beginTime"), params.get("endTime"));
        Page<WorkCenterVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工作中心列表
     *
     * @param bo 查询条件
     * @return 工作中心列表
     */
    @Override
    public List<WorkCenterVo> queryList(WorkCenterBo bo) {
        LambdaQueryWrapper<WorkCenter> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<WorkCenter> buildQueryWrapper(WorkCenterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<WorkCenter> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(WorkCenter::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getCode()), WorkCenter::getCode, bo.getCode());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), WorkCenter::getDescription, bo.getDescription());

        return lqw;
    }

    /**
     * 新增工作中心
     *
     * @param bo 工作中心
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(WorkCenterBo bo) {
        WorkCenter add = MapstructUtils.convert(bo, WorkCenter.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改工作中心
     *
     * @param bo 工作中心
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(WorkCenterBo bo) {
        WorkCenter update = MapstructUtils.convert(bo, WorkCenter.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(WorkCenter entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除工作中心信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Boolean> syncSapToWorkCenterAsync(String tenantId) {
        return CompletableFuture.supplyAsync(() -> syncSapToWorkCenter(tenantId));
    }

    /**
     * 将SAP数据同步到工作中心（添加限流和分布式锁）
     *
     * @return 如果同步成功返回true，否则返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncSapToWorkCenter(String tenantId) {
        RLock lock = redissonClient.getLock(CacheConstants.SYNC_LOCK_KEY);
        try {
            if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                throw new ServiceException("同步操作正在进行中，请稍后再试");
            }

            // 检查和设置同步状态
            if (Boolean.TRUE.equals(RedisUtils.getCacheObject(CacheConstants.SYNC_STATUS_KEY))) {
                throw new ServiceException("已有同步任务在执行");
            }
            RedisUtils.setCacheObject(CacheConstants.SYNC_STATUS_KEY, true, Duration.ofMinutes(5));

            long startTime = System.currentTimeMillis();
            try {
                // 准备表格数据
                Object[] tableData = new Object[1];
                // 使用工具方法创建表格行 工厂数据
                tableData[0] = SapUtils.createTableRow(
                    "LOW", tenantId);
                // 调用带表格参数的SAP函数
                Map<String, Object> result = SapUtils.callFunctionWithTable(
                    SapFunction.ZPPM_MAST_GET_WKS,
                    "TXI_EKORG",
                    tableData);
                // 获取返回结果
                Object[] workCenterData = SapUtils.getTableData(result, "TXE_CRHD");
                if (workCenterData != null) {
                    // 处理工作中心数据
                    int total = workCenterData.length;
                    if (total == 0) {
                        return true;
                    }
                    // 优化的批处理大小
                    final int batchSize = 50;
                    List<WorkCenter> batchList = new ArrayList<>(batchSize);
                    // 处理计数器
                    int successCount = 0;
                    int errorCount = 0;

                    // 批量处理数据
                    for (int i = 0; i < total; i++) {
                        RATE_LIMITER.acquire();
                        try {
                            WorkCenter workCenter = getWorkCenter(workCenterData[i]);
                            // 添加到批处理列表
                            batchList.add(workCenter);
                            // 达到批处理大小时执行批量更新
                            if (batchList.size() >= batchSize || i == total - 1) {
                                // 批量查询现有记录
                                List<String> codes = batchList.stream()
                                    .map(WorkCenter::getCode)
                                    .collect(Collectors.toList());

                                LambdaQueryWrapper<WorkCenter> wrapper = Wrappers.lambdaQuery();
                                wrapper.in(WorkCenter::getCode, codes);
                                Map<String, WorkCenter> existingMap = baseMapper.selectList(wrapper)
                                    .stream()
                                    .collect(Collectors.toMap(
                                        WorkCenter::getCode,
                                        w -> w
                                    ));

                                List<WorkCenter> toInsert = new ArrayList<>();
                                List<WorkCenter> toUpdate = new ArrayList<>();

                                // 分类处理
                                for (WorkCenter wc : batchList) {
                                    if (existingMap.containsKey(wc.getCode())) {
                                        WorkCenter existing = existingMap.get(wc.getCode());
                                        wc.setId(existing.getId());
                                        toUpdate.add(wc);
                                    } else {
                                        toInsert.add(wc);
                                    }
                                }

                                // 执行批量操作
                                if (!toInsert.isEmpty()) {
                                    baseMapper.insertBatch(toInsert);
                                    successCount += toInsert.size();
                                }
                                if (!toUpdate.isEmpty()) {
                                    baseMapper.updateBatchById(toUpdate);
                                    successCount += toUpdate.size();
                                }
                                batchList.clear();
                            }
                        } catch (Exception e) {
                            errorCount++;
                            log.error("处理第{}条数据失败: {}", i + 1, e.getMessage());
                        }
                    }
                    long endTime = System.currentTimeMillis();
                    log.info("SAP工作中心数据同步完成，总数: {}，成功: {}，失败: {}，耗时: {}秒",
                        total, successCount, errorCount, (endTime - startTime) / 1000);
                    return successCount > 0;
                }
            } catch (Exception e) {
                throw new ServiceException("调用SAP函数或处理数据时发生异常: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException("同步SAP工作中心数据失败: " + e.getMessage());
        } finally {
            RedisUtils.deleteObject(CacheConstants.SYNC_STATUS_KEY);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return true;
    }

    @NotNull
    private static WorkCenter getWorkCenter(Object workCenterData) {
        Map<String, Object> item = (Map<String, Object>) workCenterData;
        String arbpl = (String) item.get("ARBPL");
        WorkCenter workCenter = new WorkCenter();
        workCenter.setCode(arbpl);
        workCenter.setDescription((String) item.get("KTEXT"));
        workCenter.setFactoryCode((String) item.get("WERKS"));
        workCenter.setType((String) item.get("VERWE"));
        BigDecimal zrgfy = (BigDecimal) item.get("ZRGFY");
        BigDecimal zZZFY = (BigDecimal) item.get("ZZZFY");
        workCenter.setLaborCost(NumberUtil.parseDouble(zrgfy.toString()));
        workCenter.setManufacturingCost(NumberUtil.parseDouble(zZZFY.toString()));
        String zsfww = (String) item.get("ZSFWW");
        workCenter.setIsOutsourced(zsfww);
        workCenter.setCompletionDepartment((String) item.get("VERAN"));
        return workCenter;
    }
}
