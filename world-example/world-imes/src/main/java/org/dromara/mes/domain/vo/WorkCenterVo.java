package org.dromara.mes.domain.vo;

import org.dromara.mes.domain.WorkCenter;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 工作中心视图对象 work_center
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WorkCenter.class)
public class WorkCenterVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 代号
     */
    @ExcelProperty(value = "代号")
    private String code;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    private String description;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 公司代码
     */
    @ExcelProperty(value = "公司代码")
    private String companyCode;

    /**
     * 工厂代码
     */
    @ExcelProperty(value = "工厂代码")
    private String factoryCode;

    /**
     * 人工费用
     */
    @ExcelProperty(value = "人工费用")
    private Long laborCost;

    /**
     * 制造费用
     */
    @ExcelProperty(value = "制造费用")
    private Long manufacturingCost;

    /**
     * 是否委外
     */
    @ExcelProperty(value = "是否委外")
    private String isOutsourced;

    /**
     * 单价
     */
    @ExcelProperty(value = "单价")
    private Long unitPrice;

    /**
     * 部门
     */
    @ExcelProperty(value = "部门")
    private String completionDepartment;

    /**
     * 备注信息
     */
    @ExcelProperty(value = "备注信息")
    private String remarks;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;


}
