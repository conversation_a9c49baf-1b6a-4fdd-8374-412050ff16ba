package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.OfficeUser;
import org.dromara.mes.domain.bo.OfficeUserBo;
import org.dromara.mes.domain.vo.OfficeUserVo;
import org.dromara.mes.service.IOfficeUserService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 报工用户
 * 前端访问路由地址为:/mes/OfficeUser
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/OfficeUser")
public class OfficeUserController extends BaseController {

    private final IOfficeUserService officeUserService;

    /**
     * 查询报工用户列表
     */
    @SaCheckPermission("mes:OfficeUser:list")
    @GetMapping("/list")
    public TableDataInfo<OfficeUserVo> list(OfficeUserBo bo, PageQuery pageQuery) {
        return officeUserService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报工用户列表
     */
    @SaCheckPermission("mes:OfficeUser:export")
    @Log(title = "报工用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OfficeUserBo bo, HttpServletResponse response) {
        List<OfficeUserVo> list = officeUserService.queryList(bo);
        ExcelUtil.exportExcel(list, "报工用户", OfficeUserVo.class, response);
    }

    /**
     * 导入报工用户列表
     */
    @SaCheckPermission("mes:OfficeUser:import")
    @Log(title = "报工用户", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public R<Void> importData(@RequestPart("file") MultipartFile file) throws Exception {
        // 导入方法
        List<OfficeUserVo> vList = ExcelUtil.importExcel(file.getInputStream(), OfficeUserVo.class);
        return toAjax(officeUserService.importData(MapstructUtils.convert(vList, OfficeUser.class)));
    }

    /**
     * 获取报工用户详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:OfficeUser:query")
    @GetMapping("/{id}")
    public R<OfficeUserVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") String id) {
        return R.ok(officeUserService.queryById(id));
    }

    /**
     * 新增报工用户
     */
    @SaCheckPermission("mes:OfficeUser:add")
    @Log(title = "报工用户", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OfficeUserBo bo) {
        return toAjax(officeUserService.insertByBo(bo));
    }

    /**
     * 修改报工用户
     */
    @SaCheckPermission("mes:OfficeUser:edit")
    @Log(title = "报工用户", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OfficeUserBo bo) {
        return toAjax(officeUserService.updateByBo(bo));
    }

    /**
     * 删除报工用户
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:OfficeUser:remove")
    @Log(title = "报工用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") String[] ids) {
        return toAjax(officeUserService.deleteWithValidByIds(List.of(ids), true));
    }
}
