package org.dromara.mes.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.ProRouteProcess;
import org.dromara.mes.domain.bo.ProRouteProcessBo;
import org.dromara.mes.domain.vo.ProRouteProcessVo;

import java.util.Collection;
import java.util.List;

/**
 * 工艺组成Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface IProRouteProcessService {

    /**
     * 查询工艺组成
     *
     * @param recordId 主键
     * @return 工艺组成
     */
    ProRouteProcessVo queryById(Long recordId);

    /**
     * 分页查询工艺组成列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工艺组成分页列表
     */
    TableDataInfo<ProRouteProcessVo> queryPageList(ProRouteProcessBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工艺组成列表
     *
     * @param bo 查询条件
     * @return 工艺组成列表
     */
    List<ProRouteProcessVo> queryList(ProRouteProcessBo bo);

    /**
     * 新增工艺组成
     *
     * @param bo 工艺组成
     * @return 是否新增成功
     */
    Boolean insertByBo(ProRouteProcessBo bo);

    /**
     * 修改工艺组成
     *
     * @param bo 工艺组成
     * @return 是否修改成功
     */
    Boolean updateByBo(ProRouteProcessBo bo);

    /**
     * 校验并批量删除工艺组成信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Long[] ids, Boolean isValid);

    /**
     * 根据流程ID数组查询对应的流程路由处理记录
     *
     * @param processIds 流程ID数组
     * @return 查询到的流程路由处理记录列表
     */
    List<ProRouteProcess> selectByProcessIds(Long[] processIds);

    /**
     * 根据路由ID删除路由
     *
     * @param routeId 路由ID
     * @return 删除的条数
     */
    int deleteByRouteId(Long routeId);

    /**
     * 检查序号是否已经存在
     *
     * @param proRouteProcess
     * @return
     */
    boolean checkOrderNumExists(ProRouteProcess proRouteProcess);

    /**
     * 检查工序是否已经存在
     *
     * @param proRouteProcess
     * @return
     */
    boolean checkProcessExists(ProRouteProcess proRouteProcess);

    /**
     * 检查当前工艺路线中是否已经有某个工序配置了update_flag=Y
     *
     * @param proRouteProcess
     * @return
     */
    boolean checkUpdateFlagUnique(ProRouteProcess proRouteProcess);

    /**
     * 查找上一个工序
     *
     * @param bo
     * @return
     */
    ProRouteProcess findPreProcess(ProRouteProcess bo);

    /**
     * 查找下一个工序
     *
     * @param proRouteProcess
     * @return
     */
    ProRouteProcess findNextProcess(ProRouteProcess proRouteProcess);


}
