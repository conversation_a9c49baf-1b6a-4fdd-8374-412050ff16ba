package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 报工用户对象 office_user
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("office_user")
public class OfficeUser extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private String id;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 所属公司
     */
    private String sysCompanyCode;

    /**
     * 工号
     */
    private String workNum;

    /**
     * 工作人员
     */
    private String userName;

    /**
     * 联系电话
     */
    private String userPhone;

    /**
     * 工作人员id
     */
    private String userId;

    /**
     * 办事处代码
     */
    private String officeLocId;

    /**
     * 办事处名称
     */
    private String officeLocName;

    /**
     * 负责人姓名
     */
    private String officeSupervisor;


}
