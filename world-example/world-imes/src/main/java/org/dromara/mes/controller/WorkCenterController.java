package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.WorkCenterBo;
import org.dromara.mes.domain.vo.WorkCenterVo;
import org.dromara.mes.service.IWorkCenterService;
import org.dromara.system.api.model.LoginUser;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 工作中心
 * 前端访问路由地址为:/mes/workcenter
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/workcenter")
public class WorkCenterController extends BaseController {

    private final IWorkCenterService workCenterService;

    /**
     * 查询工作中心列表
     */
    @SaCheckPermission("mes:workcenter:list")
    @GetMapping("/list")
    public TableDataInfo<WorkCenterVo> list(WorkCenterBo bo, PageQuery pageQuery) {
        return workCenterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工作中心列表
     */
    @SaCheckPermission("mes:workcenter:export")
    @Log(title = "工作中心", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WorkCenterBo bo, HttpServletResponse response) {
        List<WorkCenterVo> list = workCenterService.queryList(bo);
        ExcelUtil.exportExcel(list, "工作中心", WorkCenterVo.class, response);
    }

    /**
     * 获取工作中心详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:workcenter:query")
    @GetMapping("/{id}")
    public R<WorkCenterVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable("id") Long id) {
        return R.ok(workCenterService.queryById(id));
    }

    /**
     * 新增工作中心
     */
    @SaCheckPermission("mes:workcenter:add")
    @Log(title = "工作中心", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody WorkCenterBo bo) {
        return toAjax(workCenterService.insertByBo(bo));
    }

    /**
     * 修改工作中心
     */
    @SaCheckPermission("mes:workcenter:edit")
    @Log(title = "工作中心", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody WorkCenterBo bo) {
        return toAjax(workCenterService.updateByBo(bo));
    }

    /**
     * 删除工作中心
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:workcenter:remove")
    @Log(title = "工作中心", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(workCenterService.deleteWithValidByIds(List.of(ids), true));
    }


    /**
     * 同步SAP数据到工作中心
     *
     * @return com.alibaba.nacos.common.model.RestResult<java.lang.Boolean>
     * <AUTHOR>
     * @date 2025/4/11 16:54
     */
    @GetMapping( "/syncSapToWorkCenter")
    @Log(title = "工作中心", businessType = BusinessType.SYNC)
    @RepeatSubmit(interval = 2, timeUnit = TimeUnit.SECONDS, message = "{repeat.submit.message}")
    public R<Void> syncSapToWorkCenter() {
        String tenantId = TenantHelper.getTenantId();
        // 调用异步方法
        CompletableFuture<Boolean> future = workCenterService.syncSapToWorkCenterAsync(tenantId);
        Boolean result = false;
        try {
            result = future.get(1, TimeUnit.MINUTES);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            R.fail("等待同步结果时发生错误", e);
        }
        return toAjax(result);
    }
}
