package org.dromara.mes.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 供应商信息对象 supplier_info
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("supplier_info")
public class SupplierInfo extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 供应商ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 地址
     */
    private String address;

    /**
     * 状态
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
}
