package org.dromara.mes.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.shaded.com.google.common.util.concurrent.RateLimiter;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.redis.utils.CacheUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.sap.code.SapFunction;
import org.dromara.common.sap.util.SapUtils;
import org.dromara.mes.domain.SupplierInfo;
import org.dromara.mes.domain.bo.SupplierInfoBo;
import org.dromara.mes.domain.vo.SupplierInfoVo;
import org.dromara.mes.mapper.SupplierInfoMapper;
import org.dromara.mes.service.ISupplierInfoService;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 供应商信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-16
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SupplierInfoServiceImpl implements ISupplierInfoService {

    private final SupplierInfoMapper baseMapper;
    private final RedissonClient redissonClient;
    /**
     * 创建令牌桶限流器，每秒产生10个令牌
     */
    private static final RateLimiter RATE_LIMITER = RateLimiter.create(500.0);

    /**
     * 查询供应商信息
     *
     * @param id 主键
     * @return 供应商信息
     */
    @Override
    public SupplierInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询供应商信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 供应商信息分页列表
     */
    @Override
    public TableDataInfo<SupplierInfoVo> queryPageList(SupplierInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SupplierInfo> lqw = buildQueryWrapper(bo);
        Page<SupplierInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的供应商信息列表
     *
     * @param bo 查询条件
     * @return 供应商信息列表
     */
    @Override
    public List<SupplierInfoVo> queryList(SupplierInfoBo bo) {
        LambdaQueryWrapper<SupplierInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SupplierInfo> buildQueryWrapper(SupplierInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SupplierInfo> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SupplierInfo::getId);
        lqw.like(StringUtils.isNotBlank(bo.getSupplierName()), SupplierInfo::getSupplierName, bo.getSupplierName());
        lqw.like(StringUtils.isNotBlank(bo.getSupplierCode()), SupplierInfo::getSupplierCode, bo.getSupplierCode());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPerson()), SupplierInfo::getContactPerson, bo.getContactPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getContactEmail()), SupplierInfo::getContactEmail, bo.getContactEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getContactPhone()), SupplierInfo::getContactPhone, bo.getContactPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), SupplierInfo::getAddress, bo.getAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SupplierInfo::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 根据供应商编码查询供应商信息
     *
     * @param supplierCode 供应商code
     * @return 参数键值
     */
    @Cacheable(cacheNames = CacheNames.MES_SUPPLIER, key = "#supplierCode")
    @Override
    public String selectSupplierByCode(String supplierCode) {
        SupplierInfo supplierInfo = baseMapper.selectOne(new LambdaQueryWrapper<SupplierInfo>()
            .eq(SupplierInfo::getSupplierCode, supplierCode));
        return ObjectUtils.notNullGetter(supplierInfo, SupplierInfo::getSupplierCode, StringUtils.EMPTY);
    }

    /**
     * 新增供应商信息
     *
     * @param bo 供应商信息
     * @return 是否新增成功
     */
    @CachePut(cacheNames = CacheNames.MES_SUPPLIER, key = "#bo.supplierCode")
    @Override
    public Boolean insertByBo(SupplierInfoBo bo) {
        SupplierInfo add = MapstructUtils.convert(bo, SupplierInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改供应商信息
     *
     * @param bo 供应商信息
     * @return 是否修改成功
     */
    @CachePut(cacheNames = CacheNames.MES_SUPPLIER, key = "#bo.supplierCode")
    @Override
    public Boolean updateByBo(SupplierInfoBo bo) {
        int row = 0;
        SupplierInfo update = MapstructUtils.convert(bo, SupplierInfo.class);
        validEntityBeforeSave(update);
        if (update.getId() != null) {
            SupplierInfo temp = baseMapper.selectById(update.getId());
            if (!StringUtils.equals(temp.getSupplierCode(), update.getSupplierCode())) {
                CacheUtils.evict(CacheNames.MES_SUPPLIER, temp.getSupplierCode());
            }
            row = baseMapper.updateById(update);
        } else {
            CacheUtils.evict(CacheNames.MES_SUPPLIER, update.getSupplierCode());
            row = baseMapper.update(update, new LambdaQueryWrapper<SupplierInfo>()
                .eq(SupplierInfo::getSupplierCode, update.getSupplierCode()));
        }
        return row > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SupplierInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除供应商信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 校验供应商编码是否唯一
     *
     * @param supplierInfo 供应商信息
     * @return 结果
     */
    @Override
    public boolean checkSupplierCodeUnique(SupplierInfoBo supplierInfo) {
        long configId = ObjectUtils.notNull(supplierInfo.getId(), -1L);
        SupplierInfo info = baseMapper.selectOne(new LambdaQueryWrapper<SupplierInfo>().eq(SupplierInfo::getSupplierCode, supplierInfo.getSupplierCode()));
        if (ObjectUtil.isNotNull(info) && info.getId() != configId) {
            return false;
        }
        return true;
    }

    /**
     * 重置供应商缓存数据
     */
    @Override
    public void resetSupplierCache() {
        CacheUtils.clear(CacheNames.MES_SUPPLIER);
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Boolean> syncSapToSupplierAsync(String tenantId) {
        return CompletableFuture.supplyAsync(() -> syncSapToSupplier(tenantId));
    }

    /**
     * 将SAP数据同步到工作中心（添加限流和分布式锁）
     *
     * @return 如果同步成功返回true，否则返回false
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean syncSapToSupplier(String tenantId) {
        RLock lock = redissonClient.getLock(CacheConstants.SYNC_SUPPLIE_STATUS_KEY);
        try {
            if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
                throw new ServiceException("同步操作正在进行中，请稍后再试");
            }

            // 检查和设置同步状态
            if (Boolean.TRUE.equals(RedisUtils.getCacheObject(CacheConstants.SYNC_SUPPLIE_LOCK_KEY))) {
                throw new ServiceException("已有同步任务在执行");
            }
            RedisUtils.setCacheObject(CacheConstants.SYNC_SUPPLIE_LOCK_KEY, true, Duration.ofMinutes(5));
            long startTime = System.currentTimeMillis();
            try {
                // 准备参数
                Map<String, Object> params = new HashMap<>();
                //是否外发供应商X表示外发，N表示非外发，空为全部
                params.put("IM_WFVEN", "");
                //排除冻结供应商
                params.put("IM_SPERR", "X");
                //排除已删除供应商
                params.put("IM_LOEVM", "X");

                // 准备表格数据
                Object[] tableData = new Object[1];
                // 使用工具方法创建表格行
                tableData[0] = SapUtils.createTableRow(
                    "LOW", tenantId);

                Map<String, Object> result = SapUtils.callFunctionWithTable(
                    SapFunction.ZPPM_MAST_GET_VEN,
                    params,
                    "TXI_EKORG",
                    tableData);
                // 获取返回结果
                Object[] supplierInfoData = SapUtils.getTableData(result, "TXE_LFM1");
                if (supplierInfoData != null) {
                    // 处理供应商数据
                    int total = supplierInfoData.length;
                    if (total == 0) {
                        return true;
                    }
                    // 优化的批处理大小
                    final int batchSize = 500;
                    List<SupplierInfo> batchList = new ArrayList<>(batchSize);
                    // 处理计数器
                    int successCount = 0;
                    int errorCount = 0;
                    // 批量处理数据
                    for (int i = 0; i < total; i++) {
                        RATE_LIMITER.acquire();
                        try {
                            SupplierInfo supplierInfo = getSupplierInfo(supplierInfoData[i]);
                            // 添加到批处理列表
                            batchList.add(supplierInfo);
                            // 达到批处理大小时执行批量更新
                            if (batchList.size() >= batchSize || i == total - 1) {
                                // 批量查询现有记录
                                List<String> codes = batchList.stream()
                                    .map(SupplierInfo::getSupplierCode)
                                    .collect(Collectors.toList());

                                LambdaQueryWrapper<SupplierInfo> wrapper = Wrappers.lambdaQuery();
                                wrapper.in(SupplierInfo::getSupplierCode, codes);
                                Map<String, SupplierInfo> existingMap = baseMapper.selectList(wrapper)
                                    .stream()
                                    .collect(Collectors.toMap(
                                        SupplierInfo::getSupplierCode,
                                        w -> w
                                    ));

                                List<SupplierInfo> toInsert = new ArrayList<>();
                                List<SupplierInfo> toUpdate = new ArrayList<>();

                                // 分类处理
                                for (SupplierInfo sl : batchList) {
                                    if (existingMap.containsKey(sl.getSupplierCode())) {
                                        SupplierInfo existing = existingMap.get(sl.getSupplierCode());
                                        sl.setId(existing.getId());
                                        toUpdate.add(sl);
                                    } else {
                                        toInsert.add(sl);
                                    }
                                }

                                // 执行批量操作
                                if (!toInsert.isEmpty()) {
                                    baseMapper.insertBatch(toInsert);
                                    successCount += toInsert.size();
                                }
                                if (!toUpdate.isEmpty()) {
                                    baseMapper.updateBatchById(toUpdate);
                                    successCount += toUpdate.size();
                                }
                                batchList.clear();
                            }
                        } catch (Exception e) {
                            errorCount++;
                        }
                    }
                    long endTime = System.currentTimeMillis();
                    log.info("SAP供应商数据同步完成，总数: {}，成功: {}，失败: {}，耗时: {}秒",
                        total, successCount, errorCount, (endTime - startTime) / 1000);
                    return successCount > 0;
                }
            } catch (Exception e) {
                throw new ServiceException("调用SAP函数或处理数据时发生异常: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new ServiceException("同步SAP供应商数据失败: " + e.getMessage());
        } finally {
            RedisUtils.deleteObject(CacheConstants.SYNC_SUPPLIE_LOCK_KEY);
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return true;
    }

    @NotNull
    private static SupplierInfo getSupplierInfo(Object supplierInfoData) {
        Map<String, Object> item = (Map<String, Object>) supplierInfoData;
        String lifnr = (String) item.get("LIFNR");
        String sort = (String) item.get("SORT1");
        SupplierInfo supplierInfo = new SupplierInfo();
        supplierInfo.setSupplierCode(lifnr);
        supplierInfo.setSupplierName(sort);
        supplierInfo.setContactPerson((String) item.get("VERKF"));
        String sperm = (String) item.get("SPERM");
        if("X".equals(sperm)){
            supplierInfo.setStatus("1");
        }
        return supplierInfo;
    }

}
