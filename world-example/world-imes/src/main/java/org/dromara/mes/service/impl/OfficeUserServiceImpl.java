package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.OfficeLoc;
import org.dromara.mes.domain.OfficeUser;
import org.dromara.mes.domain.bo.OfficeUserBo;
import org.dromara.mes.domain.vo.OfficeUserVo;
import org.dromara.mes.mapper.OfficeLocMapper;
import org.dromara.mes.mapper.OfficeUserMapper;
import org.dromara.mes.service.IOfficeUserService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 报工用户Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RequiredArgsConstructor
@Service
public class OfficeUserServiceImpl extends ServiceImpl<OfficeUserMapper, OfficeUser> implements IOfficeUserService {

    private final OfficeUserMapper baseMapper;

    @Resource
    private OfficeLocMapper officeLocMapper;

    /**
     * 查询报工用户
     *
     * @param id 主键
     * @return 报工用户
     */
    @Override
    public OfficeUserVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询报工用户列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报工用户分页列表
     */
    @Override
    public TableDataInfo<OfficeUserVo> queryPageList(OfficeUserBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OfficeUser> lqw = buildQueryWrapper(bo);
        Page<OfficeUserVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的报工用户列表
     *
     * @param bo 查询条件
     * @return 报工用户列表
     */
    @Override
    public List<OfficeUserVo> queryList(OfficeUserBo bo) {
        LambdaQueryWrapper<OfficeUser> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OfficeUser> buildQueryWrapper(OfficeUserBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OfficeUser> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OfficeUser::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getWorkNum()), OfficeUser::getWorkNum, bo.getWorkNum());
        lqw.eq(StringUtils.isNotBlank(bo.getUserName()), OfficeUser::getUserName, bo.getUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getUserPhone()), OfficeUser::getUserPhone, bo.getUserPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeLocId()), OfficeUser::getOfficeLocId, bo.getOfficeLocId());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeLocName()), OfficeUser::getOfficeLocName, bo.getOfficeLocName());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeSupervisor()), OfficeUser::getOfficeSupervisor, bo.getOfficeSupervisor());
        return lqw;
    }

    /**
     * 新增报工用户
     *
     * @param bo 报工用户
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OfficeUserBo bo) {
        OfficeUser add = MapstructUtils.convert(bo, OfficeUser.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            // 根据办事处代码，更新 OfficeLoc 的 totalUser 字段
            String officeLocId = add.getOfficeLocId();
            if (StringUtils.isNotBlank(officeLocId)) {
                OfficeLoc officeLoc = officeLocMapper.selectOne(new LambdaQueryWrapper<OfficeLoc>().eq(OfficeLoc::getOfficeCode, officeLocId));
                if (officeLoc != null) {
                    officeLoc.setTotalUser(officeLoc.getTotalUser() + 1);
                    officeLocMapper.updateById(officeLoc);
                }
            }
        }
        return flag;
    }

    /**
     * 修改报工用户
     *
     * @param bo 报工用户
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OfficeUserBo bo) {
        OfficeUser update = MapstructUtils.convert(bo, OfficeUser.class);
        validEntityBeforeSave(update);
        OfficeUser original = baseMapper.selectById(update.getId());
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            // 根据办事处代码，更新 OfficeLoc 的 totalUser 字段
            String newOfficeLocId = update.getOfficeLocId();
            String oldOfficeLocId = original.getOfficeLocId();
            if (!Objects.equals(newOfficeLocId, oldOfficeLocId)) {
                if (StringUtils.isNotBlank(oldOfficeLocId)) {
                    OfficeLoc oldOfficeLoc = officeLocMapper.selectOne(new LambdaQueryWrapper<OfficeLoc>().eq(OfficeLoc::getOfficeCode, oldOfficeLocId));
                    if (oldOfficeLoc != null) {
                        oldOfficeLoc.setTotalUser(oldOfficeLoc.getTotalUser() - 1);
                        officeLocMapper.updateById(oldOfficeLoc);
                    }
                }
                if (StringUtils.isNotBlank(newOfficeLocId)) {
                    OfficeLoc newOfficeLoc = officeLocMapper.selectOne(new LambdaQueryWrapper<OfficeLoc>().eq(OfficeLoc::getOfficeCode, newOfficeLocId));
                    if (newOfficeLoc != null) {
                        newOfficeLoc.setTotalUser(newOfficeLoc.getTotalUser() + 1);
                        officeLocMapper.updateById(newOfficeLoc);
                    }
                }
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OfficeUser entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除报工用户信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        for (String id : ids) {
            OfficeUser user = baseMapper.selectById(id);
            if (user != null) {
                String officeLocId = user.getOfficeLocId();
                if (StringUtils.isNotBlank(officeLocId)) {
                    OfficeLoc officeLoc = officeLocMapper.selectOne(new LambdaQueryWrapper<OfficeLoc>().eq(OfficeLoc::getOfficeCode, officeLocId));
                    if (officeLoc != null) {
                        officeLoc.setTotalUser(officeLoc.getTotalUser() - 1);
                        officeLocMapper.updateById(officeLoc);
                    }
                }
            }
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public Boolean importData(List<OfficeUser> officeUsers) {
        boolean result = baseMapper.insertBatch(officeUsers);
        if (result) {
            for (OfficeUser user : officeUsers) {
                // 根据办事处代码，更新 OfficeLoc 的 totalUser 字段
                String officeLocId = user.getOfficeLocId();
                if (StringUtils.isNotBlank(officeLocId)) {
                    OfficeLoc officeLoc = officeLocMapper.selectOne(new LambdaQueryWrapper<OfficeLoc>().eq(OfficeLoc::getOfficeCode, officeLocId));
                    if (officeLoc != null) {
                        officeLoc.setTotalUser(officeLoc.getTotalUser() + 1);
                        officeLocMapper.updateById(officeLoc);
                    }
                }
            }
        }
        return result;
    }
}
