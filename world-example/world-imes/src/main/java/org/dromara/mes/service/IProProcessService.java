package org.dromara.mes.service;

import org.dromara.mes.domain.ProProcess;
import org.dromara.mes.domain.vo.ProProcessVo;
import org.dromara.mes.domain.bo.ProProcessBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 工序管理Service接口
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
public interface IProProcessService {

    /**
     * 查询工序管理
     *
     * @param processId 主键
     * @return 工序管理
     */
    ProProcessVo queryById(Long processId);

    /**
     * 分页查询工序管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工序管理分页列表
     */
    TableDataInfo<ProProcessVo> queryPageList(ProProcessBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的工序管理列表
     *
     * @param bo 查询条件
     * @return 工序管理列表
     */
    List<ProProcessVo> queryList(ProProcessBo bo);

    /**
     * 新增工序管理
     *
     * @param bo 工序管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ProProcessBo bo);

    /**
     * 修改工序管理
     *
     * @param bo 工序管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ProProcessBo bo);

    /**
     * 校验并批量删除工序管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Long[] ids, Boolean isValid);

    /**
     * 校验工序key是否唯一
     *
     * @param bo 信息数据
     * @return 结果
     */
    boolean checkProcessCodeUnique(ProProcess bo);
    /**
     * 校验工序名称是否唯一
     *
     * @param bo 信息数据
     * @return 结果
     */
    boolean checkProcessNameUnique(ProProcess bo);
}
