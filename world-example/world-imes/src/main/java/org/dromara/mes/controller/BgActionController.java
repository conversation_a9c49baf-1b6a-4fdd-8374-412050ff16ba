package org.dromara.mes.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.mes.domain.bo.BgActionBo;
import org.dromara.mes.domain.vo.BgActionVo;
import org.dromara.mes.service.IBgActionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 报工动作
 * 前端访问路由地址为:/mes/bgAction
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/bgAction")
public class BgActionController extends BaseController {

    private final IBgActionService bgActionService;

    /**
     * 查询报工动作列表
     */
    @SaCheckPermission("mes:bgAction:list")
    @GetMapping("/list")
    public TableDataInfo<BgActionVo> list(BgActionBo bo, PageQuery pageQuery) {
        return bgActionService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报工动作列表
     */
    @SaCheckPermission("mes:bgAction:export")
    @Log(title = "报工动作", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BgActionBo bo, HttpServletResponse response) {
        List<BgActionVo> list = bgActionService.queryList(bo);
        ExcelUtil.exportExcel(list, "报工动作", BgActionVo.class, response);
    }

    /**
     * 获取报工动作详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("mes:bgAction:query")
    @GetMapping("/{id}")
    public R<BgActionVo> getInfo(@NotNull(message = "主键不能为空")
                                 @PathVariable("id") String id) {
        return R.ok(bgActionService.queryById(id));
    }

    /**
     * 新增报工动作
     */
    @SaCheckPermission("mes:bgAction:add")
    @Log(title = "报工动作", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody BgActionBo bo) {
        return toAjax(bgActionService.insertByBo(bo));
    }

    /**
     * 修改报工动作
     */
    @SaCheckPermission("mes:bgAction:edit")
    @Log(title = "报工动作", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody BgActionBo bo) {
        return toAjax(bgActionService.updateByBo(bo));
    }

    /**
     * 删除报工动作
     *
     * @param ids 主键串
     */
    @SaCheckPermission("mes:bgAction:remove")
    @Log(title = "报工动作", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") String[] ids) {
        return toAjax(bgActionService.deleteWithValidByIds(List.of(ids), true));
    }
}
