package org.dromara.mes.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.bo.BgActionBo;
import org.dromara.mes.domain.vo.BgActionVo;

import java.util.Collection;
import java.util.List;

/**
 * 报工动作Service接口
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
public interface IBgActionService {

    /**
     * 查询报工动作
     *
     * @param id 主键
     * @return 报工动作
     */
    BgActionVo queryById(String id);

    /**
     * 分页查询报工动作列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报工动作分页列表
     */
    TableDataInfo<BgActionVo> queryPageList(BgActionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的报工动作列表
     *
     * @param bo 查询条件
     * @return 报工动作列表
     */
    List<BgActionVo> queryList(BgActionBo bo);

    /**
     * 新增报工动作
     *
     * @param bo 报工动作
     * @return 是否新增成功
     */
    Boolean insertByBo(BgActionBo bo);

    /**
     * 修改报工动作
     *
     * @param bo 报工动作
     * @return 是否修改成功
     */
    Boolean updateByBo(BgActionBo bo);

    /**
     * 校验并批量删除报工动作信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
