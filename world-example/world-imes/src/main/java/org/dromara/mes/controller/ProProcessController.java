package org.dromara.mes.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.mes.domain.ProProcess;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.mes.domain.vo.ProProcessVo;
import org.dromara.mes.domain.bo.ProProcessBo;
import org.dromara.mes.service.IProProcessService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 工序管理
 * 前端访问路由地址为:/mes/process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/process")
public class ProProcessController extends BaseController {

    private final IProProcessService proProcessService;

    /**
     * 查询工序管理列表
     */
    @SaCheckPermission("mes:process:list")
    @GetMapping("/list")
    public TableDataInfo<ProProcessVo> list(ProProcessBo bo, PageQuery pageQuery) {
        return proProcessService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出工序管理列表
     */
    @SaCheckPermission("mes:process:export")
    @Log(title = "工序管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ProProcessBo bo, HttpServletResponse response) {
        List<ProProcessVo> list = proProcessService.queryList(bo);
        ExcelUtil.exportExcel(list, "工序管理", ProProcessVo.class, response);
    }

    /**
     * 获取工序管理详细信息
     *
     * @param processId 主键
     */
    @SaCheckPermission("mes:process:query")
    @GetMapping("/{processId}")
    public R<ProProcessVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("processId") Long processId) {
        return R.ok(proProcessService.queryById(processId));
    }

    /**
     * 新增工序管理
     */
    @SaCheckPermission("mes:process:add")
    @Log(title = "工序管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ProProcessBo bo) {
        return toAjax(proProcessService.insertByBo(bo));
    }

    /**
     * 修改工序管理
     */
    @SaCheckPermission("mes:process:edit")
    @Log(title = "工序管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ProProcessBo bo) {
        return toAjax(proProcessService.updateByBo(bo));
    }

    /**
     * 删除工序管理
     *
     * @param processIds 主键串
     */
    @SaCheckPermission("mes:process:remove")
    @Log(title = "工序管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{processIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("processIds") Long[] processIds) {
        return toAjax(proProcessService.deleteWithValidByIds(processIds, true));
    }

    /**
     * 查询所有可用工序的清单
     * @return
     */
    @GetMapping("/listAll")
    public R listAll(){
        ProProcessBo process = new ProProcessBo();
        process.setEnableFlag("Y");
        List<ProProcessVo> list =proProcessService.queryList(process);
        return R.ok(list);
    }

}
