package org.dromara.mes.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.mes.domain.PartInfo;
import org.dromara.mes.domain.bo.PartInfoBo;
import org.dromara.mes.domain.vo.PartInfoVo;
import org.dromara.mes.mapper.PartInfoMapper;
import org.dromara.mes.service.IPartInfoService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.cache.annotation.CacheEvict;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 物料档案信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RequiredArgsConstructor
@Service
@DS("world-base")
public class PartInfoServiceImpl implements IPartInfoService {

    private final PartInfoMapper baseMapper;
    private static final Logger log = LoggerFactory.getLogger(PartInfoServiceImpl.class);

    /**
     * 查询物料档案信息
     *
     * @param id 主键
     * @return 物料档案信息
     */
    @Override
    public PartInfoVo queryById(String id) {
        return baseMapper.selectVoById(id);
    }

    private QueryWrapper<PartInfoBo> buildQueryWrapper(PartInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PartInfoBo> queryWrapper = Wrappers.query();
        // 查询条件增加in条件union字段不等于Z1\Z2\Z3\Z4
        // lqw.notIn(PartInfo::getUnable, "Z1", "Z2", "Z3", "Z4");
        queryWrapper.eq(StringUtils.isNotBlank(bo.getPartType()), "p.part_type", bo.getPartType());
        queryWrapper.between(params.get("beginCreateDate") != null && params.get("endCreateDate") != null,
            "p.create_date", params.get("beginCreateDate"), params.get("endCreateDate"));
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartId()), "p.part_id", bo.getPartId());
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartName()), "p.part_name", bo.getPartName());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNum()), "p.draw_num", bo.getDrawNum());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNumVersion()), "p.draw_num_version", bo.getDrawNumVersion());
        queryWrapper.isNull("p.unable");
        queryWrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "p.status", bo.getStatus());
        queryWrapper.notExists("p.part_group", "108001");
        // 工厂条件=租户ID
        if (StringUtils.isNotBlank(bo.getPlantCode())) {
            queryWrapper.eq("f.factory", bo.getPlantCode());
        }
        queryWrapper.orderByDesc("p.id");

        return queryWrapper;
    }

    private QueryWrapper<PartInfo> buildQueryWrapper1(PartInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PartInfo> queryWrapper = Wrappers.query();
        bo.setPlantCode(TenantHelper.getTenantId());
        // 添加查询条件
        queryWrapper.eq(StringUtils.isNotBlank(bo.getPartType()), "part_type", bo.getPartType());
        queryWrapper.between(params.get("beginCreateDate") != null && params.get("endCreateDate") != null,
            "p.create_date", params.get("beginCreateDate"), params.get("endCreateDate"));
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartId()), "p.part_id", bo.getPartId());
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartName()), "p.part_name", bo.getPartName());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNum()), "p.draw_num", bo.getDrawNum());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNumVersion()), "p.draw_num_version", bo.getDrawNumVersion());
        // queryWrapper.isNull("p.unable");
        queryWrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "p.status", bo.getStatus());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getPlantCode()), "f.factory", bo.getPlantCode());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getDrawNum()), "p.draw_num", bo.getDrawNum());
        queryWrapper.apply("p.part_group != '108001'");
        queryWrapper.apply(" (p.unable NOT IN ('Z1', 'Z2', 'Z3', 'Z4') OR p.unable IS NULL)");

        queryWrapper.orderByDesc("p.create_date");
        return queryWrapper;
    }

    private QueryWrapper<PartInfoBo> buildQueryWrapper2(PartInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        QueryWrapper<PartInfoBo> queryWrapper = Wrappers.query();

        // 添加查询条件
        queryWrapper.eq(StringUtils.isNotBlank(bo.getPartType()), "part_type", bo.getPartType());
        queryWrapper.between(params.get("beginCreateDate") != null && params.get("endCreateDate") != null,
            "create_date", params.get("beginCreateDate"), params.get("endCreateDate"));
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartId()), "part_id", bo.getPartId());
        queryWrapper.like(StringUtils.isNotBlank(bo.getPartName()), "part_name", bo.getPartName());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNum()), "draw_num", bo.getDrawNum());
        queryWrapper.like(StringUtils.isNotBlank(bo.getDrawNumVersion()), "draw_num_version", bo.getDrawNumVersion());
        queryWrapper.eq(StringUtils.isNotBlank(bo.getStatus()), "status", bo.getStatus());

        return queryWrapper;
    }

    /**
     * 新增物料档案信息
     *
     * @param bo 物料档案信息
     * @return 是否新增成功
     */
    @Override
    @CacheEvict(value = "partInfoList", allEntries = true)
    public Boolean insertByBo(PartInfoBo bo) {
        PartInfo add = MapstructUtils.convert(bo, PartInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改物料档案信息
     *
     * @param bo 物料档案信息
     * @return 是否修改成功
     */
    @Override
    @CacheEvict(value = "partInfoList", allEntries = true)
    public Boolean updateByBo(PartInfoBo bo) {
        PartInfo update = MapstructUtils.convert(bo, PartInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PartInfo entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除物料档案信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    @CacheEvict(value = "partInfoList", allEntries = true)
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 分页查询物料档案信息
     *
     * @param bo        物料档案信息
     * @param pageQuery 分页
     */
    @Override
    public TableDataInfo<PartInfoVo> selectPartInfoList(PartInfoBo bo, PageQuery pageQuery) {
        // 获取工厂代码
        String factory = bo.getPlantCode();
        if (StringUtils.isBlank(factory)) {
            factory = TenantHelper.getTenantId();
        }

        // 计算分页参数
        long offset = (pageQuery.getPageNum() - 1) * pageQuery.getPageSize();
        long pageSize = pageQuery.getPageSize();

        // 查询数据
        List<PartInfoVo> list = baseMapper.selectPartInfoList(
            factory,
            offset,
            pageSize,
            bo.getPartType(),
            bo.getPartId(),
            bo.getPartName(),
            bo.getDrawNum(),
            bo.getDrawNumVersion(),
            bo.getStatus());

        // 查询总数
        long total = baseMapper.selectPartInfoCount(
            factory,
            bo.getPartType(),
            bo.getPartId(),
            bo.getPartName(),
            bo.getDrawNum(),
            bo.getDrawNumVersion(),
            bo.getStatus());

        // 构建分页结果
        return TableDataInfo.build(list, total);
    }

}
