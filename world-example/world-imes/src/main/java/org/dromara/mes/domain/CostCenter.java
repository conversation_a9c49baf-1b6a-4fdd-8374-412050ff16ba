package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 售后成本中心对象 cost_center
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cost_center")
public class CostCenter extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private String id;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 所属公司
     */
    private String sysCompanyCode;

    /**
     * 成本中心代码
     */
    private String costCenterCode;

    /**
     * 成本中心名称
     */
    private String costCenterName;

    /**
     * 单价
     */
    private Double costCenterPrice;


}
