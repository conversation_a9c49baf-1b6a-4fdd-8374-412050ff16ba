package org.dromara.mes.domain.bo;

import org.dromara.mes.domain.PdfConvertRecord;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * PDF转换记录业务对象 pdf_convert_record
 *
 * <AUTHOR>
 * @date 2025-08-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PdfConvertRecord.class, reverseConvertGenerate = false)
public class PdfConvertRecordBo extends BaseEntity {

    /**
     * 主键
     */
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * MO号
     */
    @NotBlank(message = "MO号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String moNumber;

    /**
     * NO号
     */
    private String noNumber;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 转换后文件名
     */
    private String convertedFileName;

    /**
     * ZIP文件路径（MinIO）
     */
    private String zipFilePath;

    /**
     * ZIP文件URL
     */
    private String zipFileUrl;

    /**
     * 处理状态：0-处理中，1-成功，2-失败
     */
    private Long status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;


}
