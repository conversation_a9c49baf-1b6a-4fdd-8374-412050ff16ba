package org.dromara.mes.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.mes.domain.PartInfo;
import org.dromara.mes.domain.vo.PartInfoVo;

import java.util.List;

/**
 * 物料档案信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
public interface PartInfoMapper extends BaseMapperPlus<PartInfo, PartInfoVo> {


    /**
     * 分页查询物料信息
     */
    List<PartInfoVo> selectPartInfoList(
        @Param("factory") String factory,
        @Param("offset") long offset,
        @Param("pageSize") long pageSize,
        @Param("partType") String partType,
        @Param("partId") String partId,
        @Param("partName") String partName,
        @Param("drawNum") String drawNum,
        @Param("drawNumVersion") String drawNumVersion,
        @Param("status") String status
    );

    /**
     * 查询总数
     */
    long selectPartInfoCount(
        @Param("factory") String factory,
        @Param("partType") String partType,
        @Param("partId") String partId,
        @Param("partName") String partName,
        @Param("drawNum") String drawNum,
        @Param("drawNumVersion") String drawNumVersion,
        @Param("status") String status
    );

}
