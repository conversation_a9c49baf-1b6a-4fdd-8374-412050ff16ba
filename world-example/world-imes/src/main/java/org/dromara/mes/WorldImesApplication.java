package org.dromara.mes;


import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.dromara.common.sap.config.EnableSapService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * IMES模块
 *
 * <AUTHOR>
 */
@EnableDubbo
@SpringBootApplication
@MapperScan("org.dromara.mes.mapper")
@EnableSapService
public class WorldImesApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(WorldImesApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  IMes模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
