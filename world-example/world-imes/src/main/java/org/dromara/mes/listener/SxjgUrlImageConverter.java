package org.dromara.mes.listener;

import cn.idev.excel.converters.Converter;
import cn.idev.excel.metadata.GlobalConfiguration;
import cn.idev.excel.metadata.data.WriteCellData;
import cn.idev.excel.metadata.property.ExcelContentProperty;
import cn.idev.excel.util.IoUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * @Description 图片处理
 * <AUTHOR>
 * @Date 2023/3/30 15:04
 **/
@Slf4j
public class SxjgUrlImageConverter implements Converter<URL> {
    public static int urlConnectTimeout = 2000;
    public static int urlReadTimeout = 6000;

    @Override
    public Class<?> supportJavaTypeKey() {
        return URL.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(URL value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws IOException {
        InputStream inputStream = null;
        try {
            if (ObjectUtils.isEmpty(value)) {
                return new WriteCellData<>("图片链接为空");
            }

            URLConnection urlConnection = value.openConnection();
            urlConnection.setConnectTimeout(urlConnectTimeout);
            urlConnection.setReadTimeout(urlReadTimeout);
            inputStream = urlConnection.getInputStream();
            byte[] bytes = IoUtils.toByteArray(inputStream);

            // 创建图片数据
            WriteCellData<byte[]> writeCellData = new WriteCellData<>(bytes);
            return writeCellData;
        } catch (Exception e) {
            log.info("图片获取异常", e);
            return new WriteCellData<>("图片获取异常");
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }
}
