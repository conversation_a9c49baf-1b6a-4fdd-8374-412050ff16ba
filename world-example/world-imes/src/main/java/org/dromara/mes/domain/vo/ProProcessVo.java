package org.dromara.mes.domain.vo;

import org.dromara.mes.domain.ProProcess;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 工序管理视图对象 pro_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ProProcess.class)
public class ProProcessVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工序ID
     */
    @ExcelProperty(value = "工序ID")
    private Long processId;

    /**
     * 工序编码
     */
    @ExcelProperty(value = "工序编码")
    private String processCode;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String processName;

    /**
     * 工作中心
     */
    @ExcelProperty(value = "工作中心")
    private String workCenter;

    /**
     * 是否启用
     */
    @ExcelProperty(value = "是否启用", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String enableFlag;

    /**
     * 是否生成描述
     */
    @ExcelProperty(value = "是否生成描述", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String descFlag;

    /**
     * 是否外托运
     */
    @ExcelProperty(value = "是否外托运", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String invoiceFlag;

    /**
     * 是否电镀
     */
    @ExcelProperty(value = "是否电镀", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String platingFlag;

    /**
     * 总机时是否为0
     */
    @ExcelProperty(value = "总机时是否为0", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "sys_yes_no")
    private String noYesHours;

    /**
     * 电镀类型
     */
    @ExcelProperty(value = "电镀类型")
    private String platingType;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 急单等待时间
     */
    @ExcelProperty(value = "急单等待时间")
    private Long urgWaitTime;

    /**
     * 急单等待时间
     */
    @ExcelProperty(value = "急单等待时间")
    private Long waitTime;

    /**
     * 批量等待时间
     */
    @ExcelProperty(value = "批量等待时间")
    private Long batchWaitTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 工厂代码
     */
    @ExcelProperty(value = "工厂代码")
    private String tenantId;


}
