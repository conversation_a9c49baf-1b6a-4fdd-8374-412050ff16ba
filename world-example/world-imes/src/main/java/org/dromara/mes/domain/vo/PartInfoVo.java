package org.dromara.mes.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.mes.domain.PartInfo;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 物料档案信息视图对象 part_info
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PartInfo.class)
public class PartInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private String id;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 长度(mm)
     */
    @ExcelProperty(value = "长度(mm)")
    private Long length1;

    /**
     * 物料类型
     */
    @ExcelProperty(value = "物料类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "item_type")
    private String partType;

    /**
     * 规格描述
     */
    @ExcelProperty(value = "规格描述")
    private String spec;

    /**
     * 高度(mm)
     */
    @ExcelProperty(value = "高度(mm)")
    private Long high1;

    /**
     * 计量单位
     */
    @ExcelProperty(value = "计量单位")
    private String unit;

    /**
     * 物料组
     */
    @ExcelProperty(value = "物料组")
    private String partGroup;

    /**
     * 宽度(mm)
     */
    @ExcelProperty(value = "宽度(mm)")
    private Long width1;

    /**
     * 物料编码
     */
    @ExcelProperty(value = "物料编码")
    private String partId;

    /**
     * 物料名称
     */
    @ExcelProperty(value = "物料名称")
    private String partName;

    /**
     * 批次管理标识
     */
    @ExcelProperty(value = "批次管理标识")
    private String batchManag;

    /**
     * 生产组
     */
    @ExcelProperty(value = "生产组")
    private String prodGroup;

    /**
     * 工厂代码
     */
    @ExcelProperty(value = "工厂代码")
    private String plantCode;

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    private String brand;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String drawNum;

    /**
     * 供应商代码
     */
    @ExcelProperty(value = "供应商代码")
    private String compCode;

    /**
     * 是否禁用
     */
    @ExcelProperty(value = "是否禁用")
    private String unable;

    /**
     * 国产/进口
     */
    @ExcelProperty(value = "国产/进口")
    private String domesticOrImport;

    /**
     * 正品/替代品
     */
    @ExcelProperty(value = "正品/替代品")
    private String authenticAlterna;

    /**
     * 密度(g/cm³)
     */
    @ExcelProperty(value = "密度(g/cm³)")
    private Long density;

    /**
     * 品牌备注
     */
    @ExcelProperty(value = "品牌备注")
    private String brandRemarks;

    /**
     * 图纸版本说明
     */
    @ExcelProperty(value = "图纸版本说明")
    private String drawNumVersion;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String custCode;

    /**
     * 行业标准(Y/N)
     */
    @ExcelProperty(value = "行业标准(Y/N)")
    private String industryStd;

    /**
     * 状态(0禁用/1启用)
     */
    @ExcelProperty(value = "状态(0禁用/1启用)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "is_use")
    private String status;

    /**
     * 米重(kg/m)
     */
    @ExcelProperty(value = "米重(kg/m)")
    private Long meterWeight;

    /**
     * 图纸等级
     */
    @ExcelProperty(value = "图纸等级")
    private String drawingLevel;

    /**
     * 标准工艺
     */
    @ExcelProperty(value = "标准工艺")
    private String stdProcess;

    /**
     * 直径(mm)
     */
    @ExcelProperty(value = "直径(mm)")
    private Long diameter;

    /**
     * 内径(mm)
     */
    @ExcelProperty(value = "内径(mm)")
    private Long insideDiameter;

    /**
     * 外径(mm)
     */
    @ExcelProperty(value = "外径(mm)")
    private Long outerDiameter;

    /**
     * 包装尺寸
     */
    @ExcelProperty(value = "包装尺寸")
    private String boxSize;

    /**
     * SAP同步状态
     */
    @ExcelProperty(value = "SAP同步状态")
    private String sapSyncInfo;
    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

}
