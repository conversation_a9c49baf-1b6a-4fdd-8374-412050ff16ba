package org.dromara.mes.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.OfficeLoc;
import org.dromara.mes.domain.bo.OfficeLocBo;
import org.dromara.mes.domain.vo.OfficeLocVo;
import org.dromara.mes.mapper.OfficeLocMapper;
import org.dromara.mes.service.IOfficeLocService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 工作地点组Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RequiredArgsConstructor
@Service
public class OfficeLocServiceImpl extends ServiceImpl<OfficeLocMapper, OfficeLoc> implements IOfficeLocService {

    private final OfficeLocMapper baseMapper;

    /**
     * 查询工作地点组
     *
     * @param id 主键
     * @return 工作地点组
     */
    @Override
    public OfficeLocVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询工作地点组列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工作地点组分页列表
     */
    @Override
    public TableDataInfo<OfficeLocVo> queryPageList(OfficeLocBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OfficeLoc> lqw = buildQueryWrapper(bo);
        Page<OfficeLocVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工作地点组列表
     *
     * @param bo 查询条件
     * @return 工作地点组列表
     */
    @Override
    public List<OfficeLocVo> queryList(OfficeLocBo bo) {
        LambdaQueryWrapper<OfficeLoc> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OfficeLoc> buildQueryWrapper(OfficeLocBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<OfficeLoc> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OfficeLoc::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeCode()), OfficeLoc::getOfficeCode, bo.getOfficeCode());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeDesc()), OfficeLoc::getOfficeDesc, bo.getOfficeDesc());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkNum()), OfficeLoc::getWorkNum, bo.getWorkNum());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeSupervisor()), OfficeLoc::getOfficeSupervisor, bo.getOfficeSupervisor());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkCenter()), OfficeLoc::getWorkCenter, bo.getWorkCenter());
        lqw.eq(!ObjectUtils.isEmpty(bo.getTotalUser()), OfficeLoc::getTotalUser, bo.getTotalUser());
        return lqw;
    }

    /**
     * 新增工作地点组
     *
     * @param bo 工作地点组
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OfficeLocBo bo) {
        OfficeLoc add = MapstructUtils.convert(bo, OfficeLoc.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改工作地点组
     *
     * @param bo 工作地点组
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OfficeLocBo bo) {
        OfficeLoc update = MapstructUtils.convert(bo, OfficeLoc.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OfficeLoc entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除工作地点组信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
