package org.dromara.mes.domain;

import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 工艺组成对象 pro_route_process
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pro_route_process")
public class ProRouteProcess extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "record_id")
    private Long recordId;

    /**
     * 工艺路线ID
     */
    private Long routeId;

    /**
     * 工序ID
     */
    private Long processId;

    /**
     * 工序编码
     */
    private String processCode;

    /**
     * 工序名称
     */
    private String processName;

    /**
     * 序号
     */
    private Long orderNum;

    /**
     * 工序ID
     */
    private Long nextProcessId;

    /**
     * 工序编码
     */
    private String nextProcessCode;

    /**
     * 工序名称
     */
    private String nextProcessName;

    /**
     * 与下一道工序关系
     */
    private String linkType;

    /**
     * 批量等待时间
     */
    private Long defaultPreTime;

    /**
     * 等待时间
     */
    private Long defaultSufTime;

    /**
     * 急单时间
     */
    private Long urgWaitTime;

    /**
     * 甘特图显示颜色
     */
    private String colorCode;

    /**
     * 关键工序
     */
    private String keyFlag;

    /**
     * 是否检验
     */
    private String isCheck;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
