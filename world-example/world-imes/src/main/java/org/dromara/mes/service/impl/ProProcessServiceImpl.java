package org.dromara.mes.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.ObjectUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.mes.domain.ProProcess;
import org.dromara.mes.domain.ProRouteProcess;
import org.dromara.mes.domain.bo.ProProcessBo;
import org.dromara.mes.domain.vo.ProProcessVo;
import org.dromara.mes.mapper.ProProcessMapper;
import org.dromara.mes.mapper.ProRouteProcessMapper;
import org.dromara.mes.service.IProProcessService;
import org.dromara.mes.service.IProRouteProcessService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 工序管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-14
 */
@RequiredArgsConstructor
@Service
public class ProProcessServiceImpl implements IProProcessService {

    private final ProProcessMapper baseMapper;
    private final ProRouteProcessMapper proRouteProcessMapper;

    /**
     * 查询工序管理
     *
     * @param processId 主键
     * @return 工序管理
     */
    @Override
    public ProProcessVo queryById(Long processId) {
        return baseMapper.selectVoById(processId);
    }

    /**
     * 分页查询工序管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 工序管理分页列表
     */
    @Override
    public TableDataInfo<ProProcessVo> queryPageList(ProProcessBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ProProcess> lqw = buildQueryWrapper(bo);
        Page<ProProcessVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的工序管理列表
     *
     * @param bo 查询条件
     * @return 工序管理列表
     */
    @Override
    public List<ProProcessVo> queryList(ProProcessBo bo) {
        LambdaQueryWrapper<ProProcess> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProProcess> buildQueryWrapper(ProProcessBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProProcess> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ProProcess::getProcessId);
        lqw.like(StringUtils.isNotBlank(bo.getProcessCode()), ProProcess::getProcessCode, bo.getProcessCode());
        lqw.like(StringUtils.isNotBlank(bo.getProcessName()), ProProcess::getProcessName, bo.getProcessName());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkCenter()), ProProcess::getWorkCenter, bo.getWorkCenter());
        lqw.eq(StringUtils.isNotBlank(bo.getEnableFlag()), ProProcess::getEnableFlag, bo.getEnableFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getDescFlag()), ProProcess::getDescFlag, bo.getDescFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getInvoiceFlag()), ProProcess::getInvoiceFlag, bo.getInvoiceFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getPlatingFlag()), ProProcess::getPlatingFlag, bo.getPlatingFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getNoYesHours()), ProProcess::getNoYesHours, bo.getNoYesHours());
        return lqw;
    }

    /**
     * 新增工序管理
     *
     * @param bo 工序管理
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProProcessBo bo) {
        ProProcess add = MapstructUtils.convert(bo, ProProcess.class);
        validEntityBeforeSave(add);
        add.setFactoryCode(TenantHelper.getTenantId());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setProcessId(add.getProcessId());
        }
        return flag;
    }

    /**
     * 修改工序管理
     *
     * @param bo 工序管理
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProProcessBo bo) {
        ProProcess update = MapstructUtils.convert(bo, ProProcess.class);
        validEntityBeforeSave(update);
        update.setFactoryCode(TenantHelper.getTenantId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProProcess entity) {
        //TODO 做一些数据校验,如唯一约束
        if (!checkProcessCodeUnique(entity)) {
            throw new ServiceException("工序编码已存在！");
        }
        if (!checkProcessNameUnique(entity)) {
            throw new ServiceException("工序名称已存在！");
        }
    }

    /**
     * 校验并批量删除工序管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Long[] ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
            // 查询当前工序有无关联工序流程
            List<ProRouteProcess> list = proRouteProcessMapper.selectByProcessIds(ids);
            if (list != null && list.size() > 0) {
                throw new ServiceException("工序已经被使用，无法删除");
            }
        }
        return baseMapper.deleteByIds(List.of(ids)) > 0;
    }

    /**
     * 检查流程编码是否唯一
     *
     * @param bo 业务对象，包含流程编码（processCode）和流程ID（processId）
     * @return 如果流程编码唯一，则返回true；否则返回false
     */
    @Override
    public boolean checkProcessCodeUnique(ProProcess bo) {
        long processId = ObjectUtils.notNull(bo.getProcessId(), -1L);
        ProProcess info = baseMapper.selectOne(new LambdaQueryWrapper<ProProcess>().eq(ProProcess::getProcessCode, bo.getProcessCode()));
        if (ObjectUtil.isNotNull(info) && info.getProcessId() != processId) {
            return false;
        }
        return true;
    }

    /**
     * 检查进程名称是否唯一
     *
     * @param bo ProProcess 对象，包含要检查的进程名称和进程ID
     * @return 如果进程名称唯一，则返回 true；否则返回 false
     */
    @Override
    public boolean checkProcessNameUnique(ProProcess bo) {
        long processId = ObjectUtils.notNull(bo.getProcessId(), -1L);
        ProProcess info = baseMapper.selectOne(new LambdaQueryWrapper<ProProcess>().eq(ProProcess::getProcessName, bo.getProcessName()));
        if (ObjectUtil.isNotNull(info) && info.getProcessId() != processId) {
            return false;
        }
        return true;
    }

}
