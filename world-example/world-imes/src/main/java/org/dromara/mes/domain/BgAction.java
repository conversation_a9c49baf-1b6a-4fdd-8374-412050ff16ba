package org.dromara.mes.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;

/**
 * 报工动作对象 bg_action
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("bg_action")
public class BgAction extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private String id;

    /**
     * 所属部门
     */
    private String sysOrgCode;

    /**
     * 所属公司
     */
    private String sysCompanyCode;

    /**
     * 序号
     */
    private Long sortNum;

    /**
     * 动作代码
     */
    private String actionCode;

    /**
     * 动作名称
     */
    private String actionName;

    /**
     * 部门代码
     */
    private String deptCode;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 在用
     */
    private String isUse;

    /**
     * 需同步SAP
     */
    private String isSap;

    /**
     * 需同步Master
     */
    private String isMaster;

    /**
     * 需要机床
     */
    private String needMachin;

    /**
     * 需单独关单
     */
    private String needClose;

    /**
     * 外发
     */
    private String isOut;

    /**
     * 多次报工
     */
    private String isMult;


}
