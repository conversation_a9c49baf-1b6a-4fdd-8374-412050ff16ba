package org.dromara.mes.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itextpdf.kernel.colors.ColorConstants;
import com.itextpdf.kernel.geom.AffineTransform;
import com.itextpdf.kernel.geom.Rectangle;
import com.itextpdf.kernel.pdf.*;
import com.itextpdf.kernel.pdf.annot.PdfAnnotation;
import com.itextpdf.kernel.pdf.canvas.PdfCanvas;
import com.itextpdf.kernel.pdf.xobject.PdfFormXObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.mes.domain.PdfConvertRecord;
import org.dromara.mes.domain.bo.PdfConvertRecordBo;
import org.dromara.mes.domain.vo.PdfConvertRecordVo;
import org.dromara.mes.mapper.PdfConvertRecordMapper;
import org.dromara.mes.service.IPdfConvertRecordService;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class PdfConvertRecordServiceImpl implements IPdfConvertRecordService {

    private final PdfConvertRecordMapper baseMapper;


    /**
     * 查询PDF转换记录
     *
     * @param id 主键
     * @return PDF转换记录
     */
    @Override
    public PdfConvertRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询PDF转换记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return PDF转换记录分页列表
     */
    @Override
    public TableDataInfo<PdfConvertRecordVo> queryPageList(PdfConvertRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<PdfConvertRecord> lqw = buildQueryWrapper(bo);
        Page<PdfConvertRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的PDF转换记录列表
     *
     * @param bo 查询条件
     * @return PDF转换记录列表
     */
    @Override
    public List<PdfConvertRecordVo> queryList(PdfConvertRecordBo bo) {
        LambdaQueryWrapper<PdfConvertRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PdfConvertRecord> buildQueryWrapper(PdfConvertRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PdfConvertRecord> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PdfConvertRecord::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMoNumber()), PdfConvertRecord::getMoNumber, bo.getMoNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getNoNumber()), PdfConvertRecord::getNoNumber, bo.getNoNumber());
        return lqw;
    }

    /**
     * 新增PDF转换记录
     *
     * @param bo PDF转换记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PdfConvertRecordBo bo) {
        PdfConvertRecord add = MapstructUtils.convert(bo, PdfConvertRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改PDF转换记录
     *
     * @param bo PDF转换记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PdfConvertRecordBo bo) {
        PdfConvertRecord update = MapstructUtils.convert(bo, PdfConvertRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PdfConvertRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除PDF转换记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    /**
     * 缩放PDF文件
     *
     * @param inputPath  输入PDF文件路径
     * @param outputPath 输出PDF文件路径
     * @param scale      缩放比例
     * @param drawBorder 是否绘制边框
     * @return 处理是否成功
     */
    public boolean scalePdf(String inputPath, String outputPath, float scale, boolean drawBorder) {
        try {
            PdfReader reader = new PdfReader(inputPath);
            PdfWriter writer = new PdfWriter(outputPath);
            PdfDocument originalPdf = new PdfDocument(reader);
            PdfDocument scaledPdf = new PdfDocument(writer);

            int pageCount = originalPdf.getNumberOfPages();

            for (int i = 1; i <= pageCount; i++) {
                PdfPage originalPage = originalPdf.getPage(i);
                Rectangle originalMediaBox = originalPage.getMediaBox();

                // 获取原始页面尺寸
                float originalWidth = originalMediaBox.getWidth();
                float originalHeight = originalMediaBox.getHeight();

                // 计算缩放后的内容尺寸和居中偏移
                float scaledContentWidth = originalWidth * scale;
                float scaledContentHeight = originalHeight * scale;
                float offsetX = (originalWidth - scaledContentWidth) / 2;
                float offsetY = (originalHeight - scaledContentHeight) / 2;

                // 创建新页面，保持原始页面尺寸
                Rectangle scaledMediaBox = new Rectangle(originalWidth, originalHeight);
                PdfPage scaledPage = scaledPdf.addNewPage(new com.itextpdf.kernel.geom.PageSize(scaledMediaBox));

                // 创建画布
                PdfCanvas canvas = new PdfCanvas(scaledPage);

                // 将原始页面作为表单对象导入
                PdfFormXObject formXObject = originalPage.copyAsFormXObject(scaledPdf);

                // 应用变换：先平移到居中位置，再缩放
                AffineTransform transform = AffineTransform.getTranslateInstance(offsetX, offsetY);
                transform.concatenate(AffineTransform.getScaleInstance(scale, scale));
                canvas.concatMatrix(transform);

                // 绘制表单对象
                canvas.addXObjectAt(formXObject, 0, 0);

                // 如果需要绘制边框
                if (drawBorder) {
                    canvas.saveState();
                    canvas.setLineWidth(0.5f);
                    canvas.setStrokeColor(ColorConstants.BLACK);
                    canvas.rectangle(offsetX, offsetY, scaledContentWidth, scaledContentHeight);
                    canvas.stroke();
                    canvas.restoreState();
                }

                // 处理注释
                processAnnotationsIText(originalPage, scaledPage, scale, offsetX, offsetY);

                // 复制页面属性
                copyPagePropertiesIText(originalPage, scaledPage);
            }

            // 复制文档信息
            copyDocumentInfoIText(originalPdf, scaledPdf);

            originalPdf.close();
            scaledPdf.close();
            return true;

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    private void processAnnotationsIText(PdfPage originalPage, PdfPage scaledPage,
                                         float scale, float offsetX, float offsetY) {
        try {
            java.util.List<PdfAnnotation> annotations = originalPage.getAnnotations();
            if (annotations == null || annotations.isEmpty()) {
                return;
            }

            for (PdfAnnotation annotation : annotations) {
                try {
                    Rectangle originalRect = getAnnotationRectangle(annotation);
                    if (originalRect != null) {
                        // 计算新的注释位置和大小
                        float newX = originalRect.getX() * scale + offsetX;
                        float newY = originalRect.getY() * scale + offsetY;
                        float newWidth = originalRect.getWidth() * scale;
                        float newHeight = originalRect.getHeight() * scale;
                        Rectangle newRect = new Rectangle(newX, newY, newWidth, newHeight);

                        // 创建新注释
                        PdfAnnotation newAnnotation = PdfAnnotation.makeAnnotation(
                            annotation.getPdfObject().copyTo(scaledPage.getDocument()));

                        // 将Rectangle转换为PdfArray并设置
                        PdfArray rectArray = rectangleToPdfArray(newRect);
                        newAnnotation.getPdfObject().put(PdfName.Rect, rectArray);

                        // 添加到新页面
                        scaledPage.addAnnotation(newAnnotation);
                    }
                } catch (Exception e) {
                    System.err.println("处理注释时出错，跳过该注释: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            System.err.println("处理注释列表时出错: " + e.getMessage());
        }
    }

    private Rectangle getAnnotationRectangle(PdfAnnotation annotation) {
        try {
            // 方法2：从PdfArray转换为Rectangle
            PdfArray rectArray = annotation.getPdfObject().getAsArray(PdfName.Rect);
            if (rectArray != null && rectArray.size() >= 4) {
                float x = rectArray.getAsNumber(0).floatValue();
                float y = rectArray.getAsNumber(1).floatValue();
                float width = rectArray.getAsNumber(2).floatValue() - x;
                float height = rectArray.getAsNumber(3).floatValue() - y;
                return new Rectangle(x, y, width, height);
            }
        } catch (Exception e) {
            System.err.println("获取注释矩形时出错: " + e.getMessage());
        }
        return null;
    }

    /**
     * 将Rectangle转换为PdfArray
     */
    private PdfArray rectangleToPdfArray(Rectangle rect) {
        PdfArray array = new PdfArray();
        array.add(new PdfNumber(rect.getX()));
        array.add(new PdfNumber(rect.getY()));
        array.add(new PdfNumber(rect.getX() + rect.getWidth()));
        array.add(new PdfNumber(rect.getY() + rect.getHeight()));
        return array;
    }

    private void copyDocumentInfoIText(PdfDocument originalPdf, PdfDocument scaledPdf) {
        try {
            PdfDocumentInfo originalInfo = originalPdf.getDocumentInfo();
            PdfDocumentInfo scaledInfo = scaledPdf.getDocumentInfo();

            if (originalInfo.getTitle() != null) {
                scaledInfo.setTitle(originalInfo.getTitle());
            }
            if (originalInfo.getAuthor() != null) {
                scaledInfo.setAuthor(originalInfo.getAuthor());
            }
            if (originalInfo.getSubject() != null) {
                scaledInfo.setSubject(originalInfo.getSubject());
            }
            if (originalInfo.getKeywords() != null) {
                scaledInfo.setKeywords(originalInfo.getKeywords());
            }
            if (originalInfo.getCreator() != null) {
                scaledInfo.setCreator(originalInfo.getCreator());
            }
            if (originalInfo.getProducer() != null) {
                scaledInfo.setProducer(originalInfo.getProducer());
            }
        } catch (Exception e) {
            System.err.println("复制文档信息时出错: " + e.getMessage());
        }
    }

    private void copyPagePropertiesIText(PdfPage originalPage, PdfPage scaledPage) {
        try {
            // 复制页面旋转
            scaledPage.setRotation(originalPage.getRotation());

            // 复制其他页面属性
            if (originalPage.getPdfObject().get(PdfName.UserUnit) != null) {
                scaledPage.getPdfObject().put(PdfName.UserUnit,
                    originalPage.getPdfObject().get(PdfName.UserUnit));
            }
        } catch (Exception e) {
            System.err.println("复制页面属性时出错: " + e.getMessage());
        }
    }

    // ================= 新增：下载+缩放核心逻辑搬迁到服务层 =================

    // 远程API与账户配置（可视需要改为读取配置中心/环境变量）
    private static final String BASE_URL_DEV = "https://tplm.world-machining.com/world-web/api/view/BrowseFile";
    private static final String BASE_URL_PRO = "https://plm.world-machining.com/world-web/api/view/BrowseFile";
    private static final String DEFAULT_USERNAME = "WDPLM";
    private static final String ENV_USERNAME = "WDPLM"; // 占位，同控制器逻辑
    private static final String ENV_PASSWORD_DEV = "12345678";
    private static final String ENV_PASSWORD_PRO = "adm123456";
    private static final String ENV_RUN_ENV = "prod"; // 可为 dev / prod

    @Override
    public Map<String, Object> downloadAndScaleByMoOrNo(String moNumber, String noNumber, String outputPath) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 清理参数
            moNumber = cleanParam(moNumber);
            String no = cleanParam(noNumber);

            // 默认输出路径
            if (outputPath == null || outputPath.trim().isEmpty()) {
                outputPath = "D:/downloads";
            }

            // 确保输出目录存在
            File outputDir = new File(outputPath);
            if (!outputDir.exists()) {
                outputDir.mkdirs();
            }

            // 至少需要一个参数
            if ((moNumber == null || moNumber.isBlank()) && (no == null || no.isBlank())) {
                result.put("success", false);
                result.put("message", "参数错误：moNumber 和 no 至少提供一个");
                return result;
            }

            // 准备下载参数：优先使用 moNumber，若为空则用 no 替代 mo 字段
            String downloadMo = (moNumber != null && !moNumber.isBlank()) ? moNumber : no;

            // 下载文件
            DownloadResult downloadResult = downloadFileByMoNumberInternal(downloadMo, outputPath, no);
            if (!downloadResult.isSuccess()) {
                result.put("success", false);
                result.put("message", downloadResult.getErrorMessage());
                return result;
            }

            String downloadedFilePath = downloadResult.getFilePath();
            String downloadedFileName = downloadResult.getFileName();
            File downloadedFile = new File(downloadedFilePath);

            // 确保 /scaled 子目录存在
            File scaledDir = new File(outputPath, "scaled");
            if (!scaledDir.exists()) {
                scaledDir.mkdirs();
            }

            // 文件名：优先使用 no，否则用 moNumber；都没有则 unknown
            String candidateName = (no != null && !no.isBlank()) ? no : moNumber;
            if (candidateName == null || candidateName.isBlank()) {
                candidateName = "unknown";
            }
            candidateName = candidateName.trim().replace("/", "_").replace("\\", "_");

            String scaledOutputPath = new File(scaledDir, candidateName + ".pdf").getAbsolutePath();

            float scale = 0.94f;
            boolean drawBorder = false;

            boolean scaleSuccess = scalePdf(downloadedFilePath, scaledOutputPath, scale, drawBorder);
            if (!scaleSuccess) {
                result.put("success", false);
                result.put("message", "PDF缩放失败");
                result.put("downloadedFile", downloadedFilePath);
                return result;
            }

            result.put("success", true);
            result.put("message", "处理完成");
            result.put("downloadedFile", downloadedFilePath);
            result.put("finalPdf", scaledOutputPath);
            result.put("scaled", true);
            result.put("scale", scale);
            result.put("scaledFileName", candidateName + ".pdf");
            result.put("fileName", downloadedFileName);
            result.put("fileSize", downloadedFile.length());
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            result.put("success", false);
            result.put("message", "处理过程发生错误: " + e.getMessage());
            return result;
        }
    }

    // 下载结果
    @lombok.Data
    @lombok.AllArgsConstructor
    private static class DownloadResult {
        private boolean success;
        private String filePath;
        private String errorMessage;
        private String fileName;
        private long fileSize;
    }

    // 通过MO号从远程API下载文件（内部方法）
    private DownloadResult downloadFileByMoNumberInternal(String moNumber, String outputPath, String no) {
        try {
            // 再次防御性清理，避免上游遗漏
            moNumber = cleanParam(moNumber);
            no = cleanParam(no);

            // 选择环境
            boolean useProd = "prod".equalsIgnoreCase(ENV_RUN_ENV);
            String apiUrl = useProd ? BASE_URL_PRO : BASE_URL_DEV;
            String username = (ENV_USERNAME == null || ENV_USERNAME.isBlank()) ? DEFAULT_USERNAME : ENV_USERNAME;
            String password = useProd ? ENV_PASSWORD_PRO : ENV_PASSWORD_DEV;

            if (password == null || password.isBlank()) {
                String envName = useProd ? "PLM_PASSWORD_PRO" : "PLM_PASSWORD_DEV";
                String msg = "未配置环境变量 " + envName + "，请在系统环境变量中设置对应密码";
                System.err.println(msg);
                return new DownloadResult(false, null, msg, null, 0);
            }

            // 组装请求JSON，传入清理后的参数
            String requestJson = buildRequestJson(moNumber, no == null ? "" : no);

            // 发送POST JSON + Basic认证
            String response = HttpRequest.post(apiUrl)
                .basicAuth(username, password)
                .body(requestJson, "application/json;charset=UTF-8")
                .timeout(30000)
                .execute()
                .body();

            if (response == null || response.isBlank()) {
                return new DownloadResult(false, null, "API返回为空", null, 0);
            }

            if (!cn.hutool.json.JSONUtil.isTypeJSON(response)) {
                return new DownloadResult(false, null, "API返回非JSON: " + response, null, 0);
            }

            cn.hutool.json.JSONObject responseJson = cn.hutool.json.JSONUtil.parseObj(response);

            // 常见返回格式支持：优先从 msg/data 中取链接
            String downloadUrl = null;
            String msgUrl = responseJson.getStr("msg");
            String dataUrl = responseJson.getStr("data");
            if (msgUrl != null && msgUrl.trim().toLowerCase().startsWith("http")) {
                downloadUrl = msgUrl.trim();
            } else if (dataUrl != null && dataUrl.trim().toLowerCase().startsWith("http")) {
                downloadUrl = dataUrl.trim();
            }

            // 兼容可能的字段名
            if (downloadUrl == null) {
                String url1 = responseJson.getStr("downloadUrl");
                if (url1 != null && url1.trim().toLowerCase().startsWith("http")) {
                    downloadUrl = url1.trim();
                }
            }

            if (downloadUrl == null) {
                int code = responseJson.getInt("code", -1);
                String errMsg = responseJson.getStr("msg", "未获取到文件下载链接");
                System.err.println("API返回错误 code=" + code + ", msg=" + errMsg);
                return new DownloadResult(false, null, errMsg, null, 0);
            }

            // 清理下载URL（去除反引号等干扰符号）
            downloadUrl = downloadUrl.replace("`", "").trim();

            // 计算文件名
            String fileName = generateFileName(moNumber, no, downloadUrl);
            String localFilePath = new File(outputPath, fileName).getAbsolutePath();

            HttpUtil.downloadFile(downloadUrl, new File(localFilePath));

            File downloadedFile = new File(localFilePath);
            if (downloadedFile.exists() && downloadedFile.length() > 0) {
                return new DownloadResult(true, localFilePath, null, fileName, downloadedFile.length());
            } else {
                return new DownloadResult(false, null, "文件下载失败或文件为空", fileName, 0);
            }

        } catch (Exception e) {
            e.printStackTrace();
            return new DownloadResult(false, null, "下载过程发生异常: " + e.getMessage(), null, 0);
        }
    }

    // 生成合理的文件名
    private String generateFileName(String moNumber, String no, String downloadUrl) {
        try {
            // 尝试发送HEAD请求获取文件信息
            cn.hutool.http.HttpResponse response = cn.hutool.http.HttpRequest.head(downloadUrl)
                .timeout(10000)
                .execute();

            // 尝试从Content-Disposition头获取文件名
            String contentDisposition = response.header("Content-Disposition");
            if (contentDisposition != null) {
                String filename = extractFileNameFromContentDisposition(contentDisposition);
                if (filename != null && !filename.isBlank()) {
                    return filename;
                }
            }

            // 尝试从Content-Type推断文件扩展名
            String contentType = response.header("Content-Type");
            String extension = getExtensionFromContentType(contentType);

            // 构造文件名 - 修复：优先使用no，去掉NO_前缀
            String baseName;
            if (no != null && !no.isBlank()) {
                baseName = no.replace("/", "_").replace("\\", "_");
            } else if (moNumber != null && !moNumber.isBlank()) {
                baseName = moNumber.replace("/", "_").replace("\\", "_");
            } else {
                baseName = "unknown";
            }

            return baseName + extension;

        } catch (Exception e) {
            // 发生异常时使用默认命名 - 修复：优先使用no，去掉NO_前缀
            String baseName;
            if (no != null && !no.isBlank()) {
                baseName = no.replace("/", "_").replace("\\", "_");
            } else if (moNumber != null && !moNumber.isBlank()) {
                baseName = moNumber.replace("/", "_").replace("\\", "_");
            } else {
                baseName = "unknown";
            }
            return baseName + ".pdf"; // 默认为PDF
        }
    }

    // 从Content-Disposition头中提取文件名
    private String extractFileNameFromContentDisposition(String contentDisposition) {
        if (contentDisposition == null) return null;

        // 尝试匹配 filename="xxx" 或 filename*=UTF-8''xxx
        String[] patterns = {
            "filename\\*=UTF-8''([^;]+)",
            "filename=\"([^\"]+)\"",
            "filename=([^;\\s]+)"
        };

        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern, java.util.regex.Pattern.CASE_INSENSITIVE);
            java.util.regex.Matcher m = p.matcher(contentDisposition);
            if (m.find()) {
                String filename = m.group(1);
                try {
                    // 如果是URL编码的，尝试解码
                    return java.net.URLDecoder.decode(filename, "UTF-8");
                } catch (Exception e) {
                    return filename;
                }
            }
        }

        return null;
    }

    // 根据Content-Type推断文件扩展名
    private String getExtensionFromContentType(String contentType) {
        if (contentType == null) return ".pdf";

        contentType = contentType.toLowerCase();
        if (contentType.contains("pdf")) return ".pdf";
        if (contentType.contains("excel") || contentType.contains("spreadsheet")) return ".xlsx";
        if (contentType.contains("word") || contentType.contains("document")) return ".docx";
        if (contentType.contains("powerpoint") || contentType.contains("presentation")) return ".pptx";
        if (contentType.contains("zip")) return ".zip";
        if (contentType.contains("image/jpeg")) return ".jpg";
        if (contentType.contains("image/png")) return ".png";
        if (contentType.contains("text/plain")) return ".txt";

        // 默认为PDF
        return ".pdf";
    }

    // 组装请求JSON
    private static String buildRequestJson(String mo, String no) {
        StringBuilder sb = new StringBuilder();
        sb.append("{")
            .append("\"MO\":\"").append(mo == null ? "" : mo).append("\",")
            .append("\"KH\":\"\",")
            .append("\"GC\":\"1000\",")
            .append("\"XMH\":\"\",")
            .append("\"NO\":\"").append(no == null ? "" : no).append("\"")
            .append("}");
        return sb.toString();
    }

    @Override
    public String cleanParam(String param) {
        if (param == null) return null;
        String s = param.trim();
        while (s.startsWith(",")) {
            s = s.substring(1);
        }
        return s.isEmpty() ? null : s;
    }

    @Override
    public Map<String, Object> processFile(String identifier, String type, float scale) {
        Map<String, Object> result = new HashMap<>();
        java.io.File tempDir = null;

        try {
            // 参数验证
            identifier = cleanParam(identifier);
            if (identifier == null || identifier.isBlank()) {
                result.put("success", false);
                result.put("message", "标识符为空");
                result.put("identifier", identifier);
                return result;
            }

            // 类型验证
            if (!"MO".equals(type) && !"NO".equals(type)) {
                result.put("success", false);
                result.put("message", "无效的类型，必须是MO或NO");
                result.put("identifier", identifier);
                return result;
            }

            // 缩放比例验证
            if (scale <= 0 || scale > 1.0f) {
                result.put("success", false);
                result.put("message", "缩放比例必须在0到1.0之间");
                result.put("identifier", identifier);
                return result;
            }

            log.info("开始处理文件: identifier={}, type={}, scale={}", identifier, type, scale);

            // 创建临时目录用于文件处理
            tempDir = java.nio.file.Files.createTempDirectory("pdf_process_" + identifier + "_").toFile();
            log.debug("创建临时目录: {}", tempDir.getAbsolutePath());

            // 根据类型确定下载参数
            String moNumber = "MO".equals(type) ? identifier : null;
            String no = "NO".equals(type) ? identifier : null;

            // 下载文件
            Map<String, Object> downloadResult = downloadFileByMoNumber(
                moNumber != null ? moNumber : identifier,
                tempDir.getAbsolutePath(),
                no
            );

            if (!(Boolean) downloadResult.getOrDefault("success", false)) {
                result.put("success", false);
                result.put("message", "下载文件失败: " + downloadResult.get("message"));
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            String downloadedFilePath = (String) downloadResult.get("downloadedFile");
            if (downloadedFilePath == null || downloadedFilePath.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "下载文件路径为空");
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            // 验证下载的文件是否存在
            java.io.File downloadedFile = new java.io.File(downloadedFilePath);
            if (!downloadedFile.exists() || !downloadedFile.isFile()) {
                result.put("success", false);
                result.put("message", "下载的文件不存在: " + downloadedFilePath);
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            // 检查文件大小
            long fileSize = downloadedFile.length();
            if (fileSize == 0) {
                result.put("success", false);
                result.put("message", "下载的文件大小为0");
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            log.debug("下载文件成功: {} ({}KB)", downloadedFilePath, fileSize / 1024);

            // 创建缩放输出目录
            java.io.File scaledDir = new java.io.File(tempDir, "scaled");
            if (!scaledDir.mkdirs() && !scaledDir.exists()) {
                result.put("success", false);
                result.put("message", "创建缩放输出目录失败");
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            // 生成输出文件名 - 清理特殊字符
            String candidateName = identifier.trim()
                .replace("/", "_")
                .replace("\\", "_")
                .replace(":", "_")
                .replace("*", "_")
                .replace("?", "_")
                .replace("\"", "_")
                .replace("<", "_")
                .replace(">", "_")
                .replace("|", "_");

            String scaledOutputPath = new java.io.File(scaledDir, candidateName + ".pdf").getAbsolutePath();

            // 缩放PDF
            log.debug("开始缩放PDF: {} -> {}", downloadedFilePath, scaledOutputPath);
            boolean scaleSuccess = scalePdfWithIText(downloadedFilePath, scaledOutputPath, scale, false);
            if (!scaleSuccess) {
                result.put("success", false);
                result.put("message", "PDF缩放失败");
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            // 验证缩放后的文件
            java.io.File scaledFile = new java.io.File(scaledOutputPath);
            if (!scaledFile.exists() || scaledFile.length() == 0) {
                result.put("success", false);
                result.put("message", "缩放后的文件无效");
                result.put("identifier", identifier);
                result.put("tempDir", tempDir.getAbsolutePath());
                return result;
            }

            log.info("文件处理成功: {} -> {} ({}KB)", identifier, candidateName + ".pdf", scaledFile.length() / 1024);

            // 返回处理结果 - 修复：去掉type前缀，直接使用标识符作为文件名
            result.put("success", true);
            result.put("identifier", identifier);
            result.put("type", type);
            result.put("scaledFilePath", scaledOutputPath);
            result.put("zipEntryName", candidateName + ".pdf");
            result.put("tempDir", tempDir.getAbsolutePath());
            result.put("originalFileSize", fileSize);
            result.put("scaledFileSize", scaledFile.length());
            result.put("originalFileName", (String) downloadResult.get("fileName"));
            result.put("fileSize", scaledFile.length()); // 添加fileSize字段用于数据库保存

            return result;

         } catch (java.io.IOException e) {
             log.error("处理文件时发生IO错误: identifier={}, error={}", identifier, e.getMessage());
             result.put("success", false);
             result.put("message", "IO错误: " + e.getMessage());
             result.put("identifier", identifier);
             if (tempDir != null) {
                 result.put("tempDir", tempDir.getAbsolutePath());
             }
             return result;
         } catch (Exception e) {
             log.error("处理文件时发生未知错误: identifier={}", identifier, e);
             result.put("success", false);
             result.put("message", "处理失败: " + e.getMessage());
             result.put("identifier", identifier);
             if (tempDir != null) {
                 result.put("tempDir", tempDir.getAbsolutePath());
             }
             return result;
         }
     }

    @Override
    public Map<String, Object> downloadFileByMoNumber(String moNumber, String outputPath, String no) {
        Map<String, Object> result = new HashMap<>();
        try {
            DownloadResult downloadResult = downloadFileByMoNumberInternal(moNumber, outputPath, no);

            result.put("success", downloadResult.isSuccess());
            result.put("downloadedFile", downloadResult.getFilePath());
            result.put("fileName", downloadResult.getFileName());
            result.put("fileSize", downloadResult.getFileSize());
            result.put("message", downloadResult.getErrorMessage());

            return result;
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "下载文件时发生错误: " + e.getMessage());
            return result;
        }
    }

    @Override
    public boolean scalePdfWithIText(String inputPath, String outputPath, float scale, boolean drawBorder) {
        return scalePdf(inputPath, outputPath, scale, drawBorder);
    }

    @Override
    public void addFileToZip(Map<String, Object> fileResult, java.util.zip.ZipOutputStream zipOut, java.util.Set<String> addedFiles) {
        String scaledFilePath = null;
        String zipEntryName = null;
        String tempDirPath = null;

        try {
            scaledFilePath = (String) fileResult.get("scaledFilePath");
            zipEntryName = (String) fileResult.get("zipEntryName");
            tempDirPath = (String) fileResult.get("tempDir");
            String identifier = (String) fileResult.get("identifier");

            // 参数验证
            if (scaledFilePath == null || zipEntryName == null) {
                log.warn("文件路径或ZIP条目名为空，跳过添加: identifier={}, scaledFilePath={}, zipEntryName={}",
                    identifier, scaledFilePath, zipEntryName);
                return;
            }

            // 检查文件是否存在
            java.io.File scaledFile = new java.io.File(scaledFilePath);
            if (!scaledFile.exists() || !scaledFile.isFile()) {
                log.warn("缩放后的文件不存在或不是文件: {}", scaledFilePath);
                return;
            }

            // 检查文件大小
            long fileSize = scaledFile.length();
            if (fileSize == 0) {
                log.warn("文件大小为0，跳过添加: {}", scaledFilePath);
                return;
            }
            if (fileSize > 100 * 1024 * 1024) { // 100MB限制
                log.warn("文件过大({}MB)，跳过添加: {}", fileSize / (1024 * 1024), scaledFilePath);
                return;
            }

            // 防止重复添加
            if (addedFiles.contains(zipEntryName)) {
                log.debug("文件已存在于ZIP中，跳过: {}", zipEntryName);
                return;
            }

            // 添加文件到ZIP
            java.util.zip.ZipEntry zipEntry = new java.util.zip.ZipEntry(zipEntryName);
            zipEntry.setTime(scaledFile.lastModified());
            zipOut.putNextEntry(zipEntry);

            // 读取缩放后的文件并写入ZIP
            try (java.io.FileInputStream fis = new java.io.FileInputStream(scaledFilePath);
                 java.io.BufferedInputStream bis = new java.io.BufferedInputStream(fis)) {

                byte[] buffer = new byte[16384]; // 增大缓冲区
                int bytesRead;
                long totalBytesRead = 0;

                while ((bytesRead = bis.read(buffer)) != -1) {
                    zipOut.write(buffer, 0, bytesRead);
                    totalBytesRead += bytesRead;
                }

                log.debug("成功写入 {} 字节到ZIP: {}", totalBytesRead, zipEntryName);
            }

            zipOut.closeEntry();
            addedFiles.add(zipEntryName);
            log.info("成功添加文件到ZIP: {} ({}KB)", zipEntryName, fileSize / 1024);

        } catch (java.io.IOException e) {
            log.error("添加文件到ZIP时发生IO错误: file={}, entry={}, error={}",
                scaledFilePath, zipEntryName, e.getMessage());
        } catch (Exception e) {
            log.error("添加文件到ZIP时发生未知错误: file={}, entry={}",
                scaledFilePath, zipEntryName, e);
        } finally {
            // 清理临时目录
            if (tempDirPath != null) {
                try {
                    deleteDirectory(new java.io.File(tempDirPath));
                    log.debug("成功清理临时目录: {}", tempDirPath);
                } catch (Exception e) {
                    log.warn("清理临时目录失败: {}, error: {}", tempDirPath, e.getMessage());
                }
            }
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(java.io.File directory) {
        if (directory.exists()) {
            java.io.File[] files = directory.listFiles();
            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            directory.delete();
        }
    }
}
