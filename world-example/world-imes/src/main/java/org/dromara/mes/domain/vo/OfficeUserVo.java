package org.dromara.mes.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.mes.domain.OfficeUser;

import java.io.Serial;
import java.io.Serializable;



/**
 * 报工用户视图对象 office_user
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = OfficeUser.class)
public class OfficeUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号")
    private String workNum;

    /**
     * 工作人员
     */
    @ExcelProperty(value = "工作人员")
    private String userName;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String userPhone;

    /**
     * 办事处代码
     */
    @ExcelProperty(value = "办事处代码")
    private String officeLocId;

    /**
     * 办事处名称
     */
    @ExcelProperty(value = "办事处名称")
    private String officeLocName;

    /**
     * 办事处负责人
     */
    @ExcelProperty(value = "办事处负责人")
    private String officeSupervisor;


}
