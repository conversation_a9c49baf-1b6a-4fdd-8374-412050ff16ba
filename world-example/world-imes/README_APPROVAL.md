# 备件备案信息审批功能说明

## 功能概述

本系统为备件备案信息增加了完整的审批流程，支持草稿、提交、审批、归档等状态管理。

## 审批状态说明

- **0 - 草稿**：初始状态，用户可以编辑和修改
- **1 - 已提交**：用户提交审批后的状态，等待审批人审核
- **2 - 归档**：审批通过后的最终状态
- **3 - 退回**：审批不通过，退回给提交人

## API接口说明

### 1. 提交审批
- **接口**：`PUT /mes/declaration/submit/{id}`
- **权限**：`mes:declaration:submit`
- **说明**：将草稿状态的记录提交审批
- **参数**：`id` - 记录主键

### 2. 审批通过
- **接口**：`PUT /mes/declaration/approve/{id}`
- **权限**：`mes:declaration:approve`
- **说明**：审批人通过审批
- **参数**：
  - `id` - 记录主键
  - `approvalOpinion` - 审批意见（可选）

### 3. 审批退回
- **接口**：`PUT /mes/declaration/reject/{id}`
- **权限**：`mes:declaration:reject`
- **说明**：审批人不通过，退回给提交人
- **参数**：
  - `id` - 记录主键
  - `approvalOpinion` - 审批意见（可选）

### 4. 归档
- **接口**：`PUT /mes/declaration/archive/{id}`
- **权限**：`mes:declaration:archive`
- **说明**：将已提交的记录直接归档
- **参数**：`id` - 记录主键

### 5. 查询待审批列表
- **接口**：`GET /mes/declaration/pending`
- **权限**：`mes:declaration:pending`
- **说明**：查询所有待审批的记录（状态为已提交）
- **参数**：支持分页和条件查询

### 6. 查询列表（支持按状态过滤）
- **接口**：`GET /mes/declaration/list`
- **权限**：`mes:declaration:list`
- **说明**：查询所有记录，支持按审批状态过滤
- **参数**：`status` - 审批状态

## 使用流程

### 1. 创建记录
用户创建备件备案信息，状态默认为草稿（0）。

### 2. 提交审批
用户确认信息无误后，调用提交审批接口，状态变为已提交（1）。

### 3. 审批处理
审批人查看待审批列表，进行审批操作：
- 通过：状态变为归档（2）
- 退回：状态变为退回（3）

### 4. 重新提交（退回后）
如果记录被退回，用户可以修改后重新提交审批。

## 权限配置

需要在系统中配置以下权限：
- `mes:declaration:submit` - 提交审批权限
- `mes:declaration:approve` - 审批通过权限
- `mes:declaration:reject` - 审批退回权限
- `mes:declaration:archive` - 归档权限
- `mes:declaration:pending` - 查看待审批列表权限

## 数据库变更

执行 `script/sql/update/add_approval_fields.sql` 脚本添加审批相关字段：
- `status` - 审批状态
- `approval_opinion` - 审批意见
- `approver` - 审批人ID
- `approval_time` - 审批时间
- `submitter` - 提交人ID
- `submit_time` - 提交时间

## 注意事项

1. 只有草稿状态的记录才能提交审批
2. 只有已提交状态的记录才能进行审批操作
3. 审批人和提交人字段需要根据实际的用户认证系统进行配置
4. 建议在查询列表时添加状态过滤，方便用户查看不同状态的记录 