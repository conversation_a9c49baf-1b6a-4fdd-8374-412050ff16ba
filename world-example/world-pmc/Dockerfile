#FROM anapsix/alpine-java:8_server-jre_unlimited
FROM 192.168.123.47:8909/library/dragonwell:dragonwell-********.3.7_jdk-17.0.3-ga

MAINTAINER world.com
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN sed -i 's/TLSv1, TLSv1.1, //' /opt/java/openjdk/conf/security/java.security

ENV JAVA_OPTS=""
ENV LD_LIBRARY_PATH /usr/local/lib:$LD_LIBRARY_PATH
COPY ./src/main/resources/lib/libsapjco3.so /usr/lib/libsapjco3.so


RUN mkdir -p /world
WORKDIR /world

#ADD ./src/main/resources/jeecg ./config/jeecg

ADD ./target/world-pmc.jar .


EXPOSE 9403

#EXPOSE 8888

CMD ["java","-Dsun.zip.disableMemoryMapping = true","-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8888", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/logs/springboot/heapdump.hprof", "-jar", "world-pmc.jar"]
