<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.pmc.mapper.PmcMoReportMapper">

    <select id="selectWdzdhSbjdbList" resultType="org.dromara.pmc.domain.WdzdhSbjdb">
        <![CDATA[
        WITH BaseData AS (
            SELECT
                客户代码,
                客户PO号,
                接单日期,
                DR_NO,
                DR_ITM,
                PMC要求交期,
                状态,
                MO_NO,
                图号,
                描述,
                订单数量,
                客户要求交期,
                项目经理,
                下单员姓名,
                跟单人,
                BOM责任人
            FROM master
            WHERE
                接单日期 >= '2024-03-01 00:00'
              AND ISNULL(状态1, 0) NOT BETWEEN 2 AND 8
              AND 状态 <> 'SO单'
              AND CHARINDEX('P', SO_NO, 3) = 0
              AND CHARINDEX('N', SO_NO, 3) = 0
              AND ISNULL(MO_NO, '') <> ''
              AND ISNULL(零件分类, '') = 'P'
              AND 计划类型 = '4'
              AND ISNULL(项目类型, '') <> '40'
              AND 订单类别 NOT IN ('Z170', 'Z180')
              AND LEFT(品号, 1) IN ('8', '9')
              AND NOT EXISTS (
                SELECT 1
                FROM WDZDH_SBJDB WITH(NOLOCK)
                WHERE MO单号 = master.MO_NO
            )
        )
        SELECT
            客户代码 AS customerCode,
            客户PO号 AS customerPo,
            接单日期 AS orderDate,
            DR_NO AS drNo,
            DR_ITM AS drItem,
            PMC要求交期 AS pmcRequiredDate,
            状态 AS status,
            MO_NO AS moNo,
            图号 AS drawingNo,
            描述 AS description,
            订单数量 AS orderQty,
            客户要求交期 AS customerRequiredDate,
            项目经理 AS projectManager,
            下单员姓名 AS orderPersonName,
            跟单人 AS responsiblePerson,
            BOM责任人 AS bomResponsible,
            HOST_NAME() AS HOST,
            '实际' AS timeNode,
            2 AS sjjd
        FROM BaseData

        UNION ALL

        SELECT
            客户代码,
            客户PO号,
            接单日期,
            DR_NO,
            DR_ITM,
            PMC要求交期,
            状态,
            MO_NO,
            图号,
            描述,
            订单数量,
            客户要求交期,
            项目经理,
            下单员姓名,
            跟单人,
            BOM责任人,
            HOST_NAME(),
            '计划' AS timeNode,
            1 AS sjjd
        FROM BaseData;
]]>
    </select>

    <!-- 批量更新操作 -->
    <update id="batchUpdateWdzdhSbjdb">
        <![CDATA[
        /* 更新跟单负责人和状态 */
        UPDATE a
        SET a.跟单负责人 = b.跟单负责人,
            a.状态 = b.状态
            FROM WDZDH_SBJDB a
        INNER JOIN [master] b WITH(NOLOCK) ON a.MO单号 = b.mo_no
        WHERE ISNULL(a.跟单负责人, '') = ''
          AND ISNULL(b.跟单负责人, '') <> '';

        /* 根据主表状态更新标志 */
        UPDATE a
        SET a.flag = 'Y'
            FROM WDZDH_SBJDB a
        INNER JOIN [master] b ON a.MO单号 = b.mo_no
        WHERE ISNULL(a.flag, '') = ''
          AND b.状态 IN (
            '关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件仓',
            '进成品仓','进半成品仓','进多余半成品仓','进待利用仓','SO单',
            '报废','出货','领出','开单领出','转关单','虚拟入库发料',
            '包装','外发转内部返修','成品报废'
            );

        /* 直接根据状态更新标志 */
        UPDATE a
        SET a.flag = 'Y'
            FROM WDZDH_SBJDB a
        WHERE a.状态 IN (
            '关单','取消','删除','关闭','OK','D/N','多余件领出','进多余件仓',
            '进成品仓','进半成品仓','进多余半成品仓','进待利用仓','SO单',
            '报废','出货','领出','开单领出','转关单','虚拟入库发料',
            '包装','外发转内部返修','成品报废'
            );

        /* 更新计划类型和品号 */
        UPDATE a
        SET a.计划类型 = b.计划类型,
            a.品号 = b.品号
            FROM WDZDH_SBJDB a
        INNER JOIN [master] b ON a.MO单号 = b.mo_no
        WHERE ISNULL(a.flag, '') = '';

        /* 更新计划类型的时间节点 */
        UPDATE a
        SET a.SJJD = 1
            FROM WDZDH_SBJDB a
        WHERE ISNULL(a.flag, '') = ''
          AND a.时间节点 = '计划';

        /* 更新实际类型的时间节点 */
        UPDATE a
        SET a.SJJD = 2
            FROM WDZDH_SBJDB a
        WHERE ISNULL(a.flag, '') = ''
          AND a.时间节点 = '实际';
        ]]>
    </update>

    <select id="searchWdzdhSbjdbPageList" resultType="org.dromara.pmc.domain.WdzdhSbjdb" >
        <![CDATA[
        SELECT
            w.ID AS id,
            w.订单类别 AS orderType,
            w.出货地点 AS shipmentLocation,
            w.项目名称 AS projectName,
            w.客户代码 AS customerCode,
            w.客户PO号 AS customerPo,
            w.接单日期 AS orderDate,
            w.DR单号 AS drNo,
            w.DR项次 AS drItem,
            w.PMC要求交期 AS pmcRequiredDate,
            w.状态 AS status,
            w.MO单号 AS moNo,
            w.图号 AS drawingNo,
            w.描述 AS description,
            w.订单数量 AS orderQty,
            w.客户要求交期 AS customerRequiredDate,
            w.进度概况及备注 AS progressRemark,
            w.交货风险描述 AS deliveryRiskDesc,
            -- 自制欠数（保持原优化逻辑）
            CASE w.时间节点
                WHEN '计划' THEN COALESCE(m.自制欠数_计划, 0)
                WHEN '实际' THEN COALESCE(m.自制欠数_实际, 0)
                ELSE 0
                END AS selfMadeShortage,
            -- 钣金欠数（保持原优化逻辑）
            CASE w.时间节点
                WHEN '计划' THEN COALESCE(m.钣金欠数_计划, 0)
                WHEN '实际' THEN COALESCE(m.钣金欠数_实际, 0)
                ELSE 0
                END AS sheetMetalShortage,
            -- 外发欠数（保持原优化逻辑）
            CASE w.时间节点
                WHEN '计划' THEN COALESCE(m.外发欠数_计划, 0)
                WHEN '实际' THEN COALESCE(m.外发欠数_实际, 0)
                ELSE 0
                END AS outsourcingShortage,
            -- 标准件欠数（新融合逻辑）
            CASE w.时间节点
                WHEN '实际' THEN COALESCE(bzj.standardPartsActual, 0)  -- 仅实际使用新表
                ELSE w.标准件欠数  -- 计划/其他使用原表值
                END AS standardPartsShortage,
            w.时间节点 AS timeNode,
            w.下图日期 AS drawingReleaseDate,
            w.机架A AS frameA,
            w.机架B AS frameB,
            w.零件齐料时间 AS partsReadyTime,
            w.标准件齐料时间 AS standardPartsReadyTime,
            w.装配开始 AS assemblyStart,
            w.装配结束 AS assemblyEnd,
            w.调试开始 AS debugStart,
            w.调试结束 AS debugEnd,
            w.出货ETD AS shipmentETD,
            w.装配车间 AS assemblyWorkshop,
            w.项目经理 AS projectManager,
            w.下单员姓名 AS orderPersonName,
            w.跟单负责人 AS responsiblePerson,
            w.BOM责任人 AS bomResponsible,
            w.usr AS usr,
            w.us_name AS usName,
            w.sysdt AS sysdt,
            w.host AS host,
            w.flag AS flag,
            w.计划类型 AS planType,
            w.品号 AS partNo,
            w.SJJD AS sjjd
        FROM SAP_SCAN.dbo.WDZDH_SBJDB w
-- 预聚合自制/钣金/外发欠数（原优化逻辑）
                 LEFT JOIN (
            SELECT
                DR_NO,
                DR_ITM,
                COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 自制欠数_计划,
                COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 自制欠数_实际,
                COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 钣金欠数_计划,
                COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 钣金欠数_实际,
                COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 外发欠数_计划,
                COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 外发欠数_实际
            FROM [master]
            WHERE ISNULL(mo_no, '') <> ''
            GROUP BY DR_NO, DR_ITM
        ) m ON w.DR单号 = m.DR_NO AND w.DR项次 = m.DR_ITM
                 LEFT JOIN (
            SELECT
                DR单号,
                DR行号,
                COUNT(DISTINCT 物料编号) AS standardPartsActual
            FROM SAP_SCAN.dbo._ZNZB_BZJ_WJH
            WHERE ISNULL(未交数量, 0) > 0
            GROUP BY DR单号, DR行号
        ) bzj ON w.DR单号 = bzj.DR单号 AND w.DR项次 = bzj.DR行号
        ${ew.customSqlSegment}
        ORDER BY w.客户要求交期 ASC, w.MO单号 ASC, w.sjjd ASC
        OFFSET #{offset} ROWS FETCH NEXT #{pageSize} ROWS ONLY
        ]]>
    </select>

    <update id="updateWdzdhSbjdb">
        update WDZDH_SBJDB set
        进度概况及备注 = #{progressRemark},
        交货风险描述 = #{deliveryRiskDesc},
        下图日期 = #{drawingReleaseDate},
        机架A = #{frameA},
        机架B = #{frameB},
        零件齐料时间 = #{partsReadyTime},
        标准件齐料时间 = #{standardPartsReadyTime},
        装配开始 = #{assemblyStart},
        装配结束 = #{assemblyEnd},
        调试开始 = #{debugStart},
        调试结束 = #{debugEnd},
        出货ETD = #{shipmentETD},
        装配车间 = #{assemblyWorkshop},
        usr = #{usr},
        us_name = #{usName},
        sysdt = #{sysdt}
        where ID = #{id}
    </update>

    <insert id="insertBatchWdzdhSbjdb">
        INSERT INTO WDZDH_SBJDB (
        客户代码,
        客户PO号,
        接单日期,
        DR单号,
        DR项次,
        PMC要求交期,
        状态,
        MO单号,
        图号,
        描述,
        订单数量,
        客户要求交期,
        项目经理,
        下单员姓名,
        跟单负责人,
        BOM责任人,
        时间节点,
        HOST,
        SJJD,
        usr,
        us_name,
        sysdt
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.customerCode},
            #{item.customerPo},
            #{item.orderDate},
            #{item.drNo},
            #{item.drItem},
            #{item.pmcRequiredDate},
            #{item.status},
            #{item.moNo},
            #{item.drawingNo},
            #{item.description},
            #{item.orderQty},
            #{item.customerRequiredDate},
            #{item.projectManager},
            #{item.orderPersonName},
            #{item.responsiblePerson},
            #{item.bomResponsible},
            #{item.timeNode},
            #{item.host},
            #{item.sjjd},
            #{item.usr},
            #{item.usName},
            GETDATE()
            )
        </foreach>
    </insert>

    <select id="searchWdzdhSbjdbList" resultType="org.dromara.pmc.domain.WdzdhSbjdb">
        <![CDATA[
    SELECT
        w.ID AS id,
        w.订单类别 AS orderType,
        w.出货地点 AS shipmentLocation,
        w.项目名称 AS projectName,
        w.客户代码 AS customerCode,
        w.客户PO号 AS customerPo,
        w.接单日期 AS orderDate,
        w.DR单号 AS drNo,
        w.DR项次 AS drItem,
        w.PMC要求交期 AS pmcRequiredDate,
        w.状态 AS status,
        w.MO单号 AS moNo,
        w.图号 AS drawingNo,
        w.描述 AS description,
        w.订单数量 AS orderQty,
        w.客户要求交期 AS customerRequiredDate,
        w.进度概况及备注 AS progressRemark,
        w.交货风险描述 AS deliveryRiskDesc,
        -- 自制欠数（保持原优化逻辑）
        CASE w.时间节点
            WHEN '计划' THEN COALESCE(m.自制欠数_计划, 0)
            WHEN '实际' THEN COALESCE(m.自制欠数_实际, 0)
            ELSE 0
            END AS selfMadeShortage,
        -- 钣金欠数（保持原优化逻辑）
        CASE w.时间节点
            WHEN '计划' THEN COALESCE(m.钣金欠数_计划, 0)
            WHEN '实际' THEN COALESCE(m.钣金欠数_实际, 0)
            ELSE 0
            END AS sheetMetalShortage,
        -- 外发欠数（保持原优化逻辑）
        CASE w.时间节点
            WHEN '计划' THEN COALESCE(m.外发欠数_计划, 0)
            WHEN '实际' THEN COALESCE(m.外发欠数_实际, 0)
            ELSE 0
            END AS outsourcingShortage,
        -- 标准件欠数（新融合逻辑）
        CASE w.时间节点
            WHEN '实际' THEN COALESCE(bzj.standardPartsActual, 0)  -- 仅实际使用新表
            ELSE w.标准件欠数  -- 计划/其他使用原表值
            END AS standardPartsShortage,
        w.时间节点 AS timeNode,
        w.下图日期 AS drawingReleaseDate,
        w.机架A AS frameA,
        w.机架B AS frameB,
        w.零件齐料时间 AS partsReadyTime,
        w.标准件齐料时间 AS standardPartsReadyTime,
        w.装配开始 AS assemblyStart,
        w.装配结束 AS assemblyEnd,
        w.调试开始 AS debugStart,
        w.调试结束 AS debugEnd,
        w.出货ETD AS shipmentETD,
        w.装配车间 AS assemblyWorkshop,
        w.项目经理 AS projectManager,
        w.下单员姓名 AS orderPersonName,
        w.跟单负责人 AS responsiblePerson,
        w.BOM责任人 AS bomResponsible,
        w.usr AS usr,
        w.us_name AS usName,
        w.sysdt AS sysdt,
        w.host AS host,
        w.flag AS flag,
        w.计划类型 AS planType,
        w.品号 AS partNo,
        w.SJJD AS sjjd
    FROM SAP_SCAN.dbo.WDZDH_SBJDB w
    -- 预聚合自制/钣金/外发欠数（原优化逻辑）
    LEFT JOIN (
        SELECT
            DR_NO,
            DR_ITM,
            COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 自制欠数_计划,
            COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F'
                AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                           THEN 1 END) AS 自制欠数_实际,
            COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 钣金欠数_计划,
            COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F'
                AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                           THEN 1 END) AS 钣金欠数_实际,
            COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 外发欠数_计划,
            COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F'
                AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                           THEN 1 END) AS 外发欠数_实际
        FROM [master]
        WHERE ISNULL(mo_no, '') <> ''
    ]]>
        <!-- 关键：添加DR条件到子查询 -->
        <if test="drPairs != null and !drPairs.isEmpty()">
            AND (
            <foreach collection="drPairs" item="pair" separator=" OR ">
                (DR_NO = #{pair.drNo} AND DR_ITM = #{pair.drItem})
            </foreach>
            )
        </if>
        <![CDATA[
        GROUP BY DR_NO, DR_ITM
    ) m ON w.DR单号 = m.DR_NO AND w.DR项次 = m.DR_ITM
    -- 新增：标准件欠数预聚合（仅实际）
    LEFT JOIN (
        SELECT
            DR单号,
            DR行号,
            COUNT(DISTINCT 物料编号) AS standardPartsActual  -- 去重计数（关键！）
        FROM SAP_SCAN.dbo._ZNZB_BZJ_WJH
        WHERE ISNULL(未交数量, 0) > 0  -- 核心条件：未交数量>0
    ]]>
        <!-- 关键：添加DR条件到子查询 -->
        <if test="drPairs != null and !drPairs.isEmpty()">
            AND (
            <foreach collection="drPairs" item="pair" separator=" OR ">
                (DR单号 = #{pair.drNo} AND DR行号 = #{pair.drItem})
            </foreach>
            )
        </if>
        <![CDATA[
        GROUP BY DR单号, DR行号
    ) bzj ON w.DR单号 = bzj.DR单号 AND w.DR项次 = bzj.DR行号
    ${ew.customSqlSegment}
    ]]>
    </select>

    <select id="countWdzdhSbjdbList" resultType="java.lang.Long" >
        <![CDATA[
        SELECT COUNT(*) FROM (
        SELECT
            w.ID AS id
        FROM SAP_SCAN.dbo.WDZDH_SBJDB w
                 LEFT JOIN (
            SELECT
                DR_NO,
                DR_ITM,
                COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 自制欠数_计划,
                COUNT(CASE WHEN 计划类型 IN ('2','Y') AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 自制欠数_实际,
                COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 钣金欠数_计划,
                COUNT(CASE WHEN 计划类型 = '3' AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 钣金欠数_实际,
                COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F' THEN 1 END) AS 外发欠数_计划,
                COUNT(CASE WHEN 计划类型 IN ('1','5','6','7','U') AND ISNULL(零件分类,'') = 'F'
                    AND 状态 NOT IN ('包装','进成品仓','进半成品仓','领出','开单领出')
                               THEN 1 END) AS 外发欠数_实际
            FROM [master]
            WHERE ISNULL(mo_no, '') <> ''
            GROUP BY DR_NO, DR_ITM
        ) m ON w.DR单号 = m.DR_NO AND w.DR项次 = m.DR_ITM
                 LEFT JOIN (
            SELECT
                DR单号,
                DR行号,
                COUNT(DISTINCT 物料编号) AS standardPartsActual
            FROM SAP_SCAN.dbo._ZNZB_BZJ_WJH
            WHERE ISNULL(未交数量, 0) > 0
            GROUP BY DR单号, DR行号
        ) bzj ON w.DR单号 = bzj.DR单号 AND w.DR项次 = bzj.DR行号
            ${ew.customSqlSegment}) AS total
        ]]>
    </select>

</mapper>
