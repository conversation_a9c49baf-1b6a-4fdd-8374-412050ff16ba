<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.pmc.mapper.ProcessStatisticsMapper">

    <select id="listProcessStatistics" resultType="org.dromara.pmc.domain.vo.ProcessStatisticsVo">

        <choose>
            <when test='param.closed == "N"'>
                SELECT
                利润中心 as profitCenter,
                跟单负责人 AS pmcManager,
                COUNT(*) AS drawingCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1 ELSE 0 END) AS onTimeCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ > ]]> 0 THEN 1 ELSE 0 END) AS delayCount,
                SUM(CASE WHEN MO状态 LIKE '%待退供应商' OR MO状态 LIKE '%退供应商' OR MO状态 = '外发退货' THEN 1 ELSE 0 END) AS qualityReturnCount,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS achievementRate
                FROM _WFGJB_DGX_PMC_V2 WITH (NOLOCK)
                WHERE
                (ISNULL(PO欠数, 0) > 0
                OR PO数量 IS NULL
                OR PO数量 = 0
                OR MO状态 = '外发退货'
                OR MO状态 LIKE '%退供应商'
                OR MO状态 LIKE '%待退供应商')
                <if test="param.vendorName != null and param.vendorName != ''">
                    AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                GROUP BY 利润中心, 跟单负责人
                ORDER BY 跟单负责人, 利润中心
            </when>
            <when test='param.closed == "Y"'>
                SELECT
                利润中心 as profitCenter,
                跟单负责人 AS pmcManager,
                COUNT(*) AS drawingCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1 ELSE 0 END) AS onTimeCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ > ]]> 0 THEN 1 ELSE 0 END) AS delayCount,
                SUM(CASE WHEN MO状态 LIKE '%待退供应商' OR MO状态 LIKE '%退供应商' OR MO状态 = '外发退货' THEN 1 ELSE 0 END) AS qualityReturnCount,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS achievementRate
                FROM _WFGJB_DGX_ALL_V2 WITH (NOLOCK)
                WHERE
                (ISNULL(PO欠数, 0) = 0 AND ISNULL(PO数量, 0) > 0)
                <if test="param.vendorName != null and param.vendorName != ''">
                    AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                GROUP BY 利润中心, 跟单负责人
                ORDER BY 跟单负责人, 利润中心

            </when>
        </choose>
    </select>



    <select id="listProcessDetail" resultType="org.dromara.pmc.domain.vo.ProcessDetailVo">

        <choose>
            <when test='param.closed == "N"'>
                SELECT
                MO状态 AS moStatus,
                供应商简称 AS vendorShortName,
                MO号 AS moNo,
                图号 AS drawingNo,
                客户代码 AS customerCode,
                PR数量 AS prQuantity,
                PO数量 AS poQuantity,
                PO欠数 AS poShortage,
                外发时间 AS outsourceDate,
                PMC要求交期 AS pmcRequestDate,
                跟单负责人 AS pmcManager,
                外发跟单员 AS outsourceManager,
                末工序时间 AS lastProcessTime,
                客户要求交期 AS customerDueDate,
                是否供料 AS materialProvided,
                CASE WHEN ISNULL(PO号,'')='' THEN '' ELSE 'Y' END AS poIssued,
                PO号 AS poNo,
                PO项次 AS poItem,
                PO审核 AS poReview,
                PO创建日期 AS poCreateDate,
                利润中心 AS profitCenter
                FROM _WFGJB_DGX_PMC_V2 WITH (NOLOCK)
                WHERE (
                ISNULL(PO欠数,0) > 0
                OR ISNULL(PO数量,0) = 0
                OR MO状态 = '外发退货'
                OR MO状态 LIKE '%退供应商'
                OR MO状态 LIKE '%待退供应商'
                ) <if test="param.vendorName != null and param.vendorName != ''">
                AND 厂商代号 = #{param.vendorName}
            </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                ORDER BY MO号
            </when>
            <when test='param.closed == "Y"'>
                SELECT
                MO状态 AS moStatus,
                供应商简称 AS vendorShortName,
                MO号 AS moNo,
                图号 AS drawingNo,
                客户代码 AS customerCode,
                PR数量 AS prQuantity,
                PO数量 AS poQuantity,
                PO欠数 AS poShortage,
                外发时间 AS outsourceDate,
                PMC要求交期 AS pmcRequestDate,
                跟单负责人 AS pmcManager,
                外发跟单员 AS outsourceManager,
                末工序时间 AS lastProcessTime,
                客户要求交期 AS customerDueDate,
                是否供料 AS materialProvided,
                CASE WHEN ISNULL(PO号,'')='' THEN '' ELSE 'Y' END AS poIssued,
                PO号 AS poNo,
                PO项次 AS poItem,
                PO审核 AS poReview,
                PO创建日期 AS poCreateDate,
                利润中心 AS profitCenter
                FROM _WFGJB_DGX_ALL_V2 WITH (NOLOCK)
                WHERE 1=1
                <if test="param.vendorName != null and param.vendorName != ''">
                AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                ORDER BY MO号

            </when>
        </choose>
    </select>

    <select id="listEntireProcessStatistics" resultType="org.dromara.pmc.domain.vo.ProcessStatisticsVo">

        <choose>
            <when test='param.closed == "N"'>
                SELECT
                利润中心 as profitCenter,
                跟单负责人 AS pmcManager,
                COUNT(*) AS drawingCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1 ELSE 0 END) AS onTimeCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ > ]]> 0 THEN 1 ELSE 0 END) AS delayCount,
                SUM(CASE WHEN MO状态 LIKE '%待退供应商' OR MO状态 LIKE '%退供应商' OR MO状态 = '外发退货' THEN 1 ELSE 0 END) AS qualityReturnCount,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS achievementRate,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 外发延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS actualAchievementRate
                FROM _WFGJB_QGX_V2 WITH (NOLOCK)
                WHERE
                (ISNULL(PO欠数, 0) > 0
                OR PO数量 IS NULL
                OR PO数量 = 0
                OR MO状态 = '外发退货'
                OR MO状态 LIKE '%退供应商'
                OR MO状态 LIKE '%待退供应商')
                <if test="param.vendorName != null and param.vendorName != ''">
                    AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                GROUP BY 利润中心, 跟单负责人
                ORDER BY 跟单负责人, 利润中心
            </when>
            <when test='param.closed == "Y"'>
                SELECT
                利润中心 as profitCenter,
                跟单负责人 AS pmcManager,
                COUNT(*) AS drawingCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1 ELSE 0 END) AS onTimeCount,
                SUM(CASE WHEN 延误天数 <![CDATA[ > ]]> 0 THEN 1 ELSE 0 END) AS delayCount,
                SUM(CASE WHEN MO状态 LIKE '%待退供应商' OR MO状态 LIKE '%退供应商' OR MO状态 = '外发退货' THEN 1 ELSE 0 END) AS qualityReturnCount,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS achievementRate,
                CONVERT(VARCHAR(10), ROUND(SUM(CASE WHEN 外发延误天数 <![CDATA[ <= ]]> 0 THEN 1.0 ELSE 0 END) * 100.0 / COUNT(*), 2)) + '%' AS actualAchievementRate
                FROM _WFGJB_QGX_ALL_V2 WITH (NOLOCK)
                WHERE
                (ISNULL(PO欠数, 0) = 0 AND ISNULL(PO数量, 0) > 0)
                <if test="param.vendorName != null and param.vendorName != ''">
                    AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                GROUP BY 利润中心, 跟单负责人
                ORDER BY 跟单负责人, 利润中心

            </when>
        </choose>
    </select>



    <select id="listEntireProcessDetail" resultType="org.dromara.pmc.domain.vo.ProcessDetailVo">

        <choose>
            <when test='param.closed == "N"'>
                SELECT
                MO状态 AS moStatus,
                供应商简称 AS vendorShortName,
                MO号 AS moNo,
                图号 AS drawingNo,
                客户代码 AS customerCode,
                PR数量 AS prQuantity,
                PO数量 AS poQuantity,
                PO欠数 AS poShortage,
                外发时间 AS outsourceDate,
                PMC要求交期 AS pmcRequestDate,
                跟单负责人 AS pmcManager,
                外发跟单员 AS outsourceManager,
                末工序时间 AS lastProcessTime,
                客户要求交期 AS customerDueDate,
                是否供料 AS materialProvided,
                CASE WHEN ISNULL(PO号,'')='' THEN '' ELSE 'Y' END AS poIssued,
                PO号 AS poNo,
                PO项次 AS poItem,
                PO审核 AS poReview,
                PO创建日期 AS poCreateDate,
                利润中心 AS profitCenter
                FROM _WFGJB_QGX_V2 WITH (NOLOCK)
                WHERE (
                ISNULL(PO欠数,0) > 0
                OR ISNULL(PO数量,0) = 0
                OR MO状态 = '外发退货'
                OR MO状态 LIKE '%退供应商'
                OR MO状态 LIKE '%待退供应商'
                ) <if test="param.vendorName != null and param.vendorName != ''">
                AND 厂商代号 = #{param.vendorName}
            </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                ORDER BY MO号
            </when>
            <when test='param.closed == "Y"'>
                SELECT
                MO状态 AS moStatus,
                供应商简称 AS vendorShortName,
                MO号 AS moNo,
                图号 AS drawingNo,
                客户代码 AS customerCode,
                PR数量 AS prQuantity,
                PO数量 AS poQuantity,
                PO欠数 AS poShortage,
                外发时间 AS outsourceDate,
                PMC要求交期 AS pmcRequestDate,
                跟单负责人 AS pmcManager,
                外发跟单员 AS outsourceManager,
                末工序时间 AS lastProcessTime,
                客户要求交期 AS customerDueDate,
                是否供料 AS materialProvided,
                CASE WHEN ISNULL(PO号,'')='' THEN '' ELSE 'Y' END AS poIssued,
                PO号 AS poNo,
                PO项次 AS poItem,
                PO审核 AS poReview,
                PO创建日期 AS poCreateDate,
                利润中心 AS profitCenter
                FROM _WFGJB_QGX_ALL_V2 WITH (NOLOCK)
                WHERE 1=1
                <if test="param.vendorName != null and param.vendorName != ''">
                    AND 厂商代号 = #{param.vendorName}
                </if>
                <if test="param.moNo != null and param.moNo != ''">
                    AND MO号 = #{param.moNo}
                </if>
                <if test="param.customerCode != null and param.customerCode != ''">
                    AND 客户代码 = #{param.customerCode}
                </if>
                <if test="param.pmcManager != null and param.pmcManager != ''">
                    AND 跟单负责人 = #{param.pmcManager}
                </if>
                <if test="param.supplier != null and param.supplier != ''">
                    AND 供应商简称 = #{param.supplier}
                </if>
                <if test="param.processType != null and param.processType != ''">
                    AND PR类型 = #{param.processType}
                </if>
                ORDER BY MO号

            </when>
        </choose>
    </select>




</mapper>
