package org.dromara.pmc.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.PmcProQueryDTO;
import org.dromara.pmc.domain.WdzdhSbjdb;
import org.dromara.pmc.domain.vo.*;

import java.util.List;

public interface PmcMoReportService {

	/**
	 * 获取所有责任负责人列表
	 * @return 负责人列表
	 */
	List<ResponsiblePerson> getResponsiblePersonList();

    /**
     * 获取所有在制订单列表
     * @return 在制订单列表
     */
    List<WdzdhSbjdb> getWdzdhSbjdbList();

    /**
     * 批量更新在制订单
     * @return 影响行数
     */
    void batchUpdateWdzdhSbjdb();

    /**
     * 更新在制订单
     */
    Boolean update(WdzdhSbjdb wdzdhSbjdb);

    /**
     * 批量保存
     */
    void batchInsert(List<WdzdhSbjdb> dataList);

    /**
     * 搜索
     */
    TableDataInfo<WdzdhSbjdbVo> searchWdzdhSbjdbList(PmcProQueryDTO pmcProQueryDTO, PageQuery pageQuery);
}
