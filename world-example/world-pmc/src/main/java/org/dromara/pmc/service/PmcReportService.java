package org.dromara.pmc.service;

import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.Statis3Vo;
import org.dromara.pmc.domain.vo.Statis4Vo;
import org.dromara.pmc.domain.vo.Statis5Vo;
import org.dromara.pmc.domain.vo.Statis5Vo2;
import org.dromara.pmc.domain.vo.Statis5Vo3;
import org.dromara.pmc.domain.vo.Statis7Vo;
import org.dromara.pmc.domain.vo.Statis8Vo;

public interface PmcReportService {
	List<Statis3Vo> statis3Search(PmcSearchForm searchForm);

	List<Statis4Vo> statis4Search(PmcSearchForm searchForm);

	List<Statis5Vo> statis5Search(PmcSearchForm searchForm);

	List<Statis5Vo2> statis5Search2(PmcSearchForm searchForm);

	/**
	 * 根据MO号查询扫描记录
	 * @param searchForm
	 * @return
	 */
	List<Statis5Vo3> statis5Search3(PmcSearchForm searchForm);

	R statis5Search4(PmcSearchForm searchForm);

	R getPicUrl(PmcSearchForm searchForm);

	List<Statis5Vo2> statis5Search5(PmcSearchForm searchForm);

    List<Statis7Vo> statis7Search(PmcSearchForm searchForm);

    List<Statis8Vo> statis8Search(PmcSearchForm searchForm);
}
