package org.dromara.pmc.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.DeliveredRemainsMaterialVo;
import org.dromara.pmc.service.DeliveredRemainsMaterialService;
import org.dromara.pmc.service.MerchandiserService;
import org.dromara.pmc.utils.SearchFormUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

/**
 * 项目出货完成未领料零件清单
 */
@RestController
@RequestMapping("/delivered_remains")
@AllArgsConstructor
public class DeliveredRemainsMaterialController {
    private final DeliveredRemainsMaterialService service;
    private final MerchandiserService merchandiserService;

    @GetMapping("/merchandiser")
    public R<List<String>> getMerchandiser() throws SQLException {
        return R.ok(merchandiserService.getMerchandiser());
    }

    @GetMapping("/statistics")
    public R<TableDataInfo<DeliveredRemainsMaterialVo>> page(PmcSearchForm form, PageQuery pageQuery) {
        SearchFormUtil.setDefaultBargainDate(form, 30);
        SearchFormUtil.setDefaultPage(pageQuery);
        return R.ok(service.page(form, pageQuery));
    }

    @RequestMapping("/statistics/download")
    public void download(HttpServletResponse response, PmcSearchForm form) {
        var data = service.page(
            form, new PageQuery(10000, 1)
        ).getRows();
        ExcelUtil.exportExcel(data, "项目出货完成未领料零件清单", DeliveredRemainsMaterialVo.class, response);
    }
}
