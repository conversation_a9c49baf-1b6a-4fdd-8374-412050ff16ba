package org.dromara.pmc.controller;

import java.sql.SQLException;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.pmc.domain.MerchandiserDTO;
import org.dromara.pmc.domain.MerchandiserReqDTO;
import org.dromara.pmc.domain.SapScanMaster;
import org.dromara.pmc.service.MerchandiserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.bean.BeanUtil;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 前端访问路由地址为:/pmc/
 */
@RestController
@RequestMapping("/merchandiser")
public class MerchandiserController extends BaseController {

    @Autowired
    MerchandiserService merchandiserService;

    @GetMapping("/list")
    public R getMerchandiser() throws SQLException {
        return R.ok(merchandiserService.getMerchandiser());
    }

    @GetMapping("/data")
    public R getMerchandiserData(@ModelAttribute MerchandiserDTO dto) throws SQLException {
        return R.ok(merchandiserService.getMerchandiserData(dto));
    }

    @Log(title = "跟单人维护", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public R edit(@RequestBody MerchandiserReqDTO dto) throws SQLException {
        return merchandiserService.editMerchandiserData(dto);
    }

    /**
     * 下载报表
     *
     * @param response 请求
     * @param merchandiserDTO 参数
     */
    @PostMapping("/dl")
    public void downReport(HttpServletResponse response, @ModelAttribute MerchandiserDTO merchandiserDTO)
        throws SQLException {
        List<SapScanMaster> merchandiserData = merchandiserService.getMerchandiserData(merchandiserDTO);
        List<MerchandiserDTO> dtoList = merchandiserData.stream().map(en -> {
            MerchandiserDTO dto = new MerchandiserDTO();
            BeanUtil.copyProperties(en, dto);
            return dto;
        }).toList();
        ExcelUtil.exportExcel(dtoList, "跟单人维护", MerchandiserDTO.class, response);
    }
}
