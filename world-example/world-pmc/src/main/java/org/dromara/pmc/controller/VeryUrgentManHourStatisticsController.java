package org.dromara.pmc.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.VeryUrgentManHourStatisticsVo;
import org.dromara.pmc.service.MerchandiserService;
import org.dromara.pmc.service.VeryUrgentManHourStatisticsService;
import org.dromara.pmc.utils.SearchFormUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;

/**
 * 特急件工时统计
 */
@RestController
@RequestMapping("/very-urgent")
@AllArgsConstructor
public class VeryUrgentManHourStatisticsController {
    private final VeryUrgentManHourStatisticsService veryUrgentManHourStatisticsService;
    private final MerchandiserService merchandiserService;

    @GetMapping("/merchandiser")
    public R<List<String>> getMerchandiser() throws SQLException {
        return R.ok(merchandiserService.getMerchandiser());
    }

    @GetMapping("/man-hour-statistics")
    public R<TableDataInfo<VeryUrgentManHourStatisticsVo>> page(PmcSearchForm form, PageQuery pageQuery) {
        SearchFormUtil.setDefaultBargainDate(form, 7);
        SearchFormUtil.setDefaultPage(pageQuery);
        return R.ok(veryUrgentManHourStatisticsService.page(form, pageQuery));
    }

    @RequestMapping("/man-hour-statistics/download")
    public void download(HttpServletResponse response, PmcSearchForm form) {
        var data = veryUrgentManHourStatisticsService.page(
            form, new PageQuery(10000, 1)
        ).getRows();
        ExcelUtil.exportExcel(data, "特急件工时统计", VeryUrgentManHourStatisticsVo.class, response);
    }
}
