package org.dromara.pmc.controller;

import java.io.IOException;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.pmc.domain.bo.ZnzbDcBo;
import org.dromara.pmc.domain.vo.ZnzbDcVo;
import org.dromara.pmc.service.IZnzbDcService;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * 流程文件管理 前端访问路由地址为:/pmc/znzbDc
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/znzbDc")
public class ZnzbDcController extends BaseController {

    private final IZnzbDcService znzbDcService;

    /**
     * 查询流程文件管理列表
     */
    @SaCheckPermission("pmc:znzbDc:list")
    @GetMapping("/list")
    public TableDataInfo<ZnzbDcVo> list(ZnzbDcBo bo, PageQuery pageQuery) {
        return znzbDcService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出流程文件管理列表
     */
    @SaCheckPermission("pmc:znzbDc:export")
    @Log(title = "流程文件管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ZnzbDcBo bo, HttpServletResponse response) {
        List<ZnzbDcVo> list = znzbDcService.queryList(bo);
        ExcelUtil.exportExcel(list, "流程文件管理", ZnzbDcVo.class, response);
    }

    /**
     * 获取流程文件管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("pmc:znzbDc:query")
    @GetMapping("/{id}")
    public R<ZnzbDcVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(znzbDcService.queryById(id));
    }

    /**
     * 新增流程文件管理
     */
    @SaCheckPermission("pmc:znzbDc:add")
    @Log(title = "流程文件管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ZnzbDcBo bo) {
        return toAjax(znzbDcService.insertByBo(bo));
    }

    /**
     * 修改流程文件管理
     */
    @SaCheckPermission("pmc:znzbDc:edit")
    @Log(title = "流程文件管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ZnzbDcBo bo) {
        return toAjax(znzbDcService.updateByBo(bo));
    }

    /**
     * 删除流程文件管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("pmc:znzbDc:remove")
    @Log(title = "流程文件管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(znzbDcService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 处理上传文件
     *
     * @param file 文件
     * @return
     */
    @PostMapping("/upload")
    @Log(title = "管理文件导入", businessType = BusinessType.IMPORT)
    public R upload(@RequestParam("file") MultipartFile file, @RequestParam("dcType") String dcType)
        throws IOException {
        if (file.isEmpty() || ObjectUtils.isEmpty(dcType)) {
            return R.fail("param is empty.");
        }
        return znzbDcService.dealFile(file, dcType);
    }

    @SaCheckPermission("pmc:znzbDc:remove")
    @DeleteMapping("/remove/{ids}")
    @Log(title = "删除PMC文件", businessType = BusinessType.DELETE)
    public R delete(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return R.fail("param is empty.");
        }
        znzbDcService.deleteFile(List.of(ids));
        return R.ok();
    }

    @SaCheckPermission("pmc:znzbDc:audit")
    @PostMapping("/audit")
    @Log(title = "审核PMC文件", businessType = BusinessType.UPDATE)
    public R audit(@RequestBody List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return R.fail("param is empty.");
        }
        znzbDcService.audit(ids);
        return R.ok();
    }

    @Log(title = "流程文件下载", businessType = BusinessType.EXPORT)
    @PostMapping("/download")
    public void downloadFile(HttpServletResponse response, @RequestParam("fileName") String fileName) {
        znzbDcService.downloadFile(fileName, response);
    }
}
