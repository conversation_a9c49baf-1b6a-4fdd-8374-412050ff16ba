package org.dromara.pmc;

import org.dromara.common.sap.config.EnableSapService;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

@SpringBootApplication
@MapperScan("org.dromara.pmc.mapper")
@EnableSapService
public class WorldPmcApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(WorldPmcApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  PMC模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
