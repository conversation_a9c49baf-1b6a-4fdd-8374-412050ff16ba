package org.dromara.pmc.service;

import org.dromara.common.core.domain.R;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.MaterialRateVo;
import org.dromara.pmc.domain.vo.Statis1Vo;
import org.dromara.pmc.domain.vo.Statis2Vo;
import org.dromara.pmc.domain.vo.Statis6Vo;

import java.sql.SQLException;
import java.util.List;

public interface PmcMoManagerService {
    List<Statis1Vo> statis1Search(PmcSearchForm searchForm) throws SQLException;

	List<String> getProfitCenter() throws SQLException;

	List<Statis2Vo> statis2Search(PmcSearchForm searchForm) throws SQLException;

	R statis6LoadData();

	List<Statis6Vo> statis6Search(PmcSearchForm searchForm) throws SQLException;
	
	R editStatis6Data(Statis6Vo statis6Vo);

	List<MaterialRateVo> getMaterial(PmcSearchForm searchForm);
}