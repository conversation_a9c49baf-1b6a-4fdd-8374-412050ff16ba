package org.dromara.pmc.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.VeryUrgentDetailVo;
import org.dromara.pmc.domain.vo.VeryUrgentFollowVo;
import org.dromara.pmc.service.MerchandiserService;
import org.dromara.pmc.service.VeryUrgentFollowService;
import org.dromara.pmc.utils.SearchFormUtil;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;

/**
 * 特急件跟进表
 */
@RestController
@RequestMapping("/very-urgent-follow")
@AllArgsConstructor
public class VeryUrgentFollowController {
    private final VeryUrgentFollowService service;
    private final MerchandiserService merchandiserService;

    @GetMapping("/merchandiser")
    public R<List<String>> getMerchandiser() throws SQLException {
        return R.ok(merchandiserService.getMerchandiser());
    }

    @GetMapping("/statistics")
    public R<List<VeryUrgentFollowVo>> list(PmcSearchForm form) {
        SearchFormUtil.setDefaultBargainDate(form, 7);
        return R.ok(service.getStatistics(form));
    }

    /**
     * 获取moNo对应的详细信息
     *
     * @param moNos moNo列表
     * @return R
     */
    @PostMapping("/details")
    public R<List<VeryUrgentDetailVo>> getDetails(@RequestParam List<String> moNos) {
        return R.ok(service.getDetails(moNos));
    }

    @RequestMapping("/details/download")
    public void downloadDetails(HttpServletResponse response, @RequestParam List<String> moNos) {
        var data = getDetails(moNos).getData();
        ExcelUtil.exportExcel(data, "特急件跟进表详情", VeryUrgentDetailVo.class, response);
    }

    @RequestMapping("/statistics/download")
    public void download(HttpServletResponse response, PmcSearchForm form) {
        var data = list(form).getData();
        ExcelUtil.exportExcel(data, "特急件跟进表", VeryUrgentFollowVo.class, response);
    }
}
