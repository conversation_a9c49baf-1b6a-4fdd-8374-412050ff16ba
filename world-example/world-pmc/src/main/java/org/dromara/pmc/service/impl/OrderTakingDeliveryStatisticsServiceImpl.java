package org.dromara.pmc.service.impl;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.OrderTakingDeliveryStatisticsVo;
import org.dromara.pmc.service.OrderTakingDeliveryStatisticsService;
import org.dromara.pmc.utils.SqlserverJdbcUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;
import java.util.stream.IntStream;

import static org.dromara.pmc.utils.RowMapperUtil.getSemiFinishedDeliveryStatisticsVoRowMapper;

@Service
public class OrderTakingDeliveryStatisticsServiceImpl implements OrderTakingDeliveryStatisticsService {
    private static final String DateTimeFormat = "yyyy-MM-dd HH:mm:ss";
    private static final String DateFormat = "yyyy-MM-dd";

    @Override
    public List<OrderTakingDeliveryStatisticsVo> getStatistics(PmcSearchForm form) {
//        var tempDataTable = "#ZNZB_PMC_JDTJ_" + RandomUtil.randomInt(10000, 99999);
//        var tempProcessDataTable = "#ZNZB_PMC_odtjrpt_" + RandomUtil.randomInt(10000, 99999);
        var tempDataTable = "#ZNZB_PMC_JDTJ_temp";
        var tempProcessDataTable = "#ZNZB_PMC_odtjrpt_temp";
        var sql = String.join("\n",
            "begin",
            getDeleteTableSql(tempDataTable, tempProcessDataTable),
            getDataSql(form, tempDataTable),
            getDeleteUnusedDataSql(tempDataTable),
            getUpdateDataSql(tempDataTable),
            getCreateTempProcessDataTableSql(form, tempProcessDataTable),
            getDeclareVariableSql(),
            getCalculateDataSql(form, tempDataTable, tempProcessDataTable),
            getSelectSql(tempProcessDataTable),
            "end;",
            getDeleteTableSql(tempDataTable, tempProcessDataTable)
        );
//        System.out.println(sql);
        try {
            SqlserverJdbcUtils.switchDataSource("sqlserver1");
            return SqlserverJdbcUtils.executeQuery(
                sql, getSemiFinishedDeliveryStatisticsVoRowMapper(
                    LocalDate.parse(form.getBargainDate().get(0), DateTimeFormatter.ofPattern(DateTimeFormat))
                )
            );
        } catch (SQLException e) {
            throw new RuntimeException("查询失败", e);
        }
//        return List.of();
    }

    private String getSelectSql(String processDataTable) {
        return "select * from %s;".formatted(processDataTable);
    }

    private String getDeleteTableSql(String dataTable, String processDataTable) {
        return """
            IF OBJECT_ID('tempdb.._data_table_', 'U') IS NOT NULL DROP TABLE _data_table_;
            IF OBJECT_ID('tempdb.._process_table_', 'U') IS NOT NULL DROP TABLE _process_table_;
            """.replace("_data_table_", dataTable)
            .replace("_process_table_", processDataTable);
    }

    /**
     * 获取日期天数差
     *
     * @param date1 日期1
     * @param date2 日期2
     * @return 日期差
     */
    private int getDateDiff(String date1, String date2) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DateTimeFormat);

        // 解析字符串为 LocalDate
        var _date1 = LocalDate.parse(date1, formatter);
        var _date2 = LocalDate.parse(date2, formatter);

        // 计算天数差（date2 - date1）
        return (int) Math.abs(ChronoUnit.DAYS.between(_date1, _date2)) + 1;
    }

    private String getDataSql(PmcSearchForm form, String tempDataTable) {
        var sql1 = """
            select
                ID,状态,生产车间 + ' ' + 制造部门 as 制造部门,SO_NO,SO_ITM,MO_NO,图号,版本,客户代码,订单数量,生产数量,
                接单日期,客户要求交期,PMC要求交期,cast('' as smalldatetime) as 入仓日期,末工序时间,0 as 延误天数,客户PO号,备注,
                下单员姓名,跟单人 as 跟单负责人,分组时间,利润中心,零件分类,URGENT,订单类别,类别描述,计划类型,品号,内部订单号,上层订单号,DR_NO,DR_ITM,动作,生产车间
            INTO %s
            FROM [master]
            """.formatted(tempDataTable);
        var whereSql = """
            WHERE 接单日期 BETWEEN '%s' and '%s'
                and 接单日期 >= '2025-03-01 00:00:00'
                AND 状态 <> 'SO单'
                AND (
                    NOT 状态 in('关单','取消','删除','关闭','OK','SO单','报废','转关单','虚拟入库发料','成品报废')
                )
                and (
                    CHARINDEX('P', SO_NO, 3) = 0
                    AND CHARINDEX('N', SO_NO, 3) = 0
                )
                AND MO_NO is not NULL
                and MO_NO != ''
                and (ISNULL(项目类型, '') <> '40')
                AND (left(品号, 1) = '8')
            """.formatted(form.getBargainDate().get(0), form.getBargainDate().get(1));
        if (ObjectUtil.isNotEmpty(form.getCustomerCode())) {
            whereSql += " AND 客户代码 = '%s' ".formatted(form.getCustomerCode());
        }
        if (ObjectUtil.isNotEmpty(form.getDrNumber())) {
            whereSql += " AND DR_NO = '%s' ".formatted(form.getDrNumber());
        }
        if (ObjectUtil.isNotEmpty(form.getMerchandiser())) {
            whereSql += " AND 跟单负责人 = '%s' ".formatted(form.getMerchandiser());
        }
        if (Objects.equals(form.getIsUrgent(), true)) {
            whereSql += " AND URGENT is not NULL and URGENT <> '' ";
        }
        return sql1 + whereSql + ";";
    }

    private String getDeleteUnusedDataSql(String tempDataTable) {
        return """
            delete from _table_name_ where 状态 in('删除','关闭','OK','关单','取消','SO单','报废','转关单','成品报废');
            delete from _table_name_ where (备注 like '%标签%') or (图号 like '%标签%');
            delete from _table_name_ where isnull(生产车间,'')='BUA' or isnull(生产车间,'')='BUB';
            """.replace("_table_name_", tempDataTable);
    }

    private String getUpdateDataSql(String tempDataTable) {
        return """
            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A, PP_SCAN AS B WITH(NOLOCK)
            WHERE A.MO_NO=B.PJ号
                and b.动作 in('外发收货送检','关闭','进半成品仓','进成品仓','领出','外发退货','收料','加工','出货')
                and a.计划类型 in('1','U','5','6','7'); -- 外发

            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A,PP_SCAN AS B WITH(NOLOCK)
            WHERE A.MO_NO=B.PJ号
                and b.动作 in('QA收货MFG','关闭','进半成品仓','进成品仓','领出','外发退货','收料','加工','出货')
                and a.计划类型='2'; -- 自制

            update a
            set a.入仓日期 = b.开始时间
            FROM _table_name_ AS A,PP_SCAN AS B WITH(NOLOCK)
            WHERE A.MO_NO=B.PJ号
                and b.动作 in('钣金待QA检测','钣金转焊接','钣金QA检测','关闭','进半成品仓','进成品仓','领出','外发退货','收料','加工','出货')
                and a.计划类型='3'; -- 钣金

            update a
            set a.入仓日期 = null,
                延误天数 = 0
            FROM _table_name_ AS A where a.入仓日期='1900-01-01 00:00:00'; -- mo

            update a
            set a.入仓日期 = B.开始时间,
                a.延误天数 = DATEDIFF(DD, a.PMC要求交期, b.开始时间)
            FROM _table_name_ AS A,pp_scan as b with(nolock)
            where A.MO_NO=B.PJ号 and b.动作 in('关闭','进半成品仓','进成品仓','领出','外发退货','收料','加工','出货')
                and A.零件分类='P' and a.入仓日期 is null; -- 成品进仓

            update a
            set a.延误天数 = DATEDIFF(DD, A.PMC要求交期, A.入仓日期)
            from _table_name_ as a where a.入仓日期<>'' ;

            update a
            set a.延误天数 = DATEDIFF(DD, A.PMC要求交期, Format(GETDATE(), 'yyyy-MM-dd'))
            from _table_name_ as a where isnull(a.入仓日期,'')='';

            update a
            set a.制造部门 = LTRIM(制造部门)
            from _table_name_ as a where a.制造部门<>'';

            update a
            set a.制造部门 = RTRIM(LEFT(制造部门, LEN(制造部门) -1))
            from _table_name_ as a where right(a.制造部门,1) in('1','2','3','4','5','6','7','Y','0','U','V');
            """.replace("_table_name_", tempDataTable);
    }

    private String getCreateTempProcessDataTableSql(PmcSearchForm form, String tempProcessDataTable) {
        var tableColumns = String.join(",", IntStream
            .rangeClosed(1, getDateDiff(form.getBargainDate().get(0), form.getBargainDate().get(1)))
            .boxed().map("dt%d float"::formatted)
            .toList()
        );
        var startDate = LocalDate
            .parse(form.getBargainDate().get(0), DateTimeFormatter.ofPattern(DateTimeFormat))
            .format(DateTimeFormatter.ofPattern(DateFormat));
        return """
            create table _table_name_ (
                统计项 nvarchar(20),
                跟单负责人 nvarchar(20),
                加工类 nvarchar(20),
                日期 datetime,
                _date_columns_
            );
            insert into _table_name_ (统计项,加工类,日期)
            SELECT b.value as 统计项,
                TJ_TYPE as 加工类,
                '_start_date_' as 日期
            FROM ZNZB_PMC_TJTYP, (
                select * from (
                    values ('当日接单'),('当日应交'),('当日实际'),('当日达成率'),('延误交货'),('累计延误'),('累计达成率')
                ) as T(value)
            ) b
            WHERE REM = '加工类'
            order by ID;
            """
            .replace("_start_date_", startDate)
            .replace("_date_columns_", tableColumns)
            .replace("_table_name_", tempProcessDataTable);
    }

    private String getDeclareVariableSql() {
        return """
            DECLARE @qian_ljyw float;
            DECLARE @dt_sjyw float;
            DECLARE @qian_yjs float;
            DECLARE @qian_sjs float;
            DECLARE @dt_yw float;
            DECLARE @dt_drsj float;
            DECLARE @dt_drljyw float;
            DECLARE @dt_dryj float;
            """;
    }

    private String getCalculateDataSql(PmcSearchForm form, String tempDataTable, String tempProcessDataTable) {
        var days = getDateDiff(form.getBargainDate().get(0), form.getBargainDate().get(1));
        var beginDate = form.getBargainDate().get(0);
        var sql = new StringBuilder();
        for (int i = 0; i < days; i++) {
            var n = i;
            var k = i + 1;
            var startDate = LocalDate
                .parse(form.getBargainDate().get(0), DateTimeFormatter.ofPattern(DateTimeFormat))
                .plusDays(n).format(DateTimeFormatter.ofPattern(DateFormat));
            var endDate = LocalDate
                .parse(form.getBargainDate().get(0), DateTimeFormatter.ofPattern(DateTimeFormat))
                .plusDays(n + 1).format(DateTimeFormatter.ofPattern(DateFormat));

            var d_dt = " (接单日期>='%s') and (接单日期 <'%s')".formatted(beginDate, endDate);
            var str_JDdt = " (接单日期>='%s') and (接单日期 <'%s')".formatted(startDate, endDate);
            var str_YQdt = " (客户要求交期>='%s') and (客户要求交期 <'%s')".formatted(startDate, endDate);
            var str_SJdt = " (入仓日期>='%s') and (入仓日期 <'%s')".formatted(startDate, endDate);

            var dto = new Dto();
            dto.setBeginDate(beginDate);
            dto.setStartDate(startDate);
            dto.setEndDate(endDate);
            dto.setK(k);
            dto.setDt(d_dt);
            dto.setJDdt(str_JDdt);
            dto.setYQdt(str_YQdt);
            dto.setSJdt(str_SJdt);
            dto.setTempDataTable(tempDataTable);
            dto.setTempProcessDataTable(tempProcessDataTable);

            var sumDto = new Dto();
            BeanUtils.copyProperties(dto, sumDto);
            sumDto.setProcessType("合计");
            sql.append(getDataSpecificSql(sumDto));

            var productionDto = new Dto();
            BeanUtils.copyProperties(dto, productionDto);
            productionDto.setJDdt(" 计划类型='2' and " + str_JDdt);
            productionDto.setYQdt(" 计划类型='2' and " + str_YQdt);
            productionDto.setSJdt(" 计划类型='2' and " + str_SJdt);
            productionDto.setProcessType("生产");
            sql.append(getDataSpecificSql(productionDto));

            var deliveryDto = new Dto();
            BeanUtils.copyProperties(dto, deliveryDto);
            deliveryDto.setJDdt(" 计划类型 in('1','U','5','6','7') and " + str_JDdt);
            deliveryDto.setYQdt(" 计划类型 in('1','U','5','6','7') and " + str_YQdt);
            deliveryDto.setSJdt(" 计划类型 in('1','U','5','6','7') and " + str_SJdt);
            deliveryDto.setProcessType("外发");
            sql.append(getDataSpecificSql(deliveryDto));

            var sheetMetalDto = new Dto();
            BeanUtils.copyProperties(dto, sheetMetalDto);
            sheetMetalDto.setJDdt(" 计划类型='3' and " + str_JDdt);
            sheetMetalDto.setYQdt(" 计划类型='3' and " + str_YQdt);
            sheetMetalDto.setSJdt(" 计划类型='3' and " + str_SJdt);
            sheetMetalDto.setProcessType("钣金");
            sql.append(getDataSpecificSql(sheetMetalDto));

            var bu1Dto = new Dto();
            BeanUtils.copyProperties(dto, bu1Dto);
            bu1Dto.setJDdt(" ((利润中心 like 'AE1%') or (利润中心 like 'RD1%') or (利润中心 like 'BU100%')) and " + str_JDdt);
            bu1Dto.setYQdt(" ((利润中心 like 'AE1%') or (利润中心 like 'RD1%') or (利润中心 like 'BU100%')) and " + str_YQdt);
            bu1Dto.setSJdt(" ((利润中心 like 'AE1%') or (利润中心 like 'RD1%') or (利润中心 like 'BU100%')) and " + str_SJdt);
            bu1Dto.setProcessType("BU1");
            sql.append(getDataSpecificSql(bu1Dto));

            var bu2Dto = new Dto();
            BeanUtils.copyProperties(dto, bu2Dto);
            bu2Dto.setJDdt(" ((利润中心 like 'AE2%') or (利润中心 like 'AE3%') or (利润中心 like 'AE4%') or (利润中心 like 'AE5%') or (利润中心 like 'RD2%') or (利润中心 like 'BU2%')) and " + str_JDdt);
            bu2Dto.setYQdt(" ((利润中心 like 'AE2%') or (利润中心 like 'AE3%') or (利润中心 like 'AE4%') or (利润中心 like 'AE5%') or (利润中心 like 'RD2%') or (利润中心 like 'BU2%')) and " + str_YQdt);
            bu2Dto.setSJdt(" ((利润中心 like 'AE2%') or (利润中心 like 'AE3%') or (利润中心 like 'AE4%') or (利润中心 like 'AE5%') or (利润中心 like 'RD2%') or (利润中心 like 'BU2%')) and " + str_SJdt);
            bu2Dto.setProcessType("BU2");
            sql.append(getDataSpecificSql(bu2Dto));

            var bu3Dto = new Dto();
            BeanUtils.copyProperties(dto, bu3Dto);
            bu3Dto.setJDdt(" ((利润中心 like 'ME1%') or (利润中心 like 'TE1%') or (利润中心 like 'TE2%') or (利润中心 like 'RD3%') or (利润中心 like 'BU3%')) and " + str_JDdt);
            bu3Dto.setYQdt(" ((利润中心 like 'ME1%') or (利润中心 like 'TE1%') or (利润中心 like 'TE2%') or (利润中心 like 'RD3%') or (利润中心 like 'BU3%')) and " + str_YQdt);
            bu3Dto.setSJdt(" ((利润中心 like 'ME1%') or (利润中心 like 'TE1%') or (利润中心 like 'TE2%') or (利润中心 like 'RD3%') or (利润中心 like 'BU3%')) and " + str_SJdt);
            bu3Dto.setProcessType("BU3");
            sql.append(getDataSpecificSql(bu3Dto));

            var internalDrDto = new Dto();
            BeanUtils.copyProperties(dto, internalDrDto);
            internalDrDto.setJDdt(" (订单类别='Z170' OR 订单类别='Z180') and " + str_JDdt);
            internalDrDto.setYQdt(" (订单类别='Z170' OR 订单类别='Z180') and " + str_YQdt);
            internalDrDto.setSJdt(" (订单类别='Z170' OR 订单类别='Z180') and " + str_SJdt);
            internalDrDto.setProcessType("内部DR");
            sql.append(getDataSpecificSql(internalDrDto));
            if (i == 1) {
                break;
            }
        }
        return sql.toString();
    }

    private String getDataSpecificSql(Dto arg) {
        var k = arg.getK();
        var dt = arg.getDt();
        var str_JDdt = arg.getJDdt();
        var str_YQdt = arg.getYQdt();
        var str_SJdt = arg.getSJdt();
        var tempDataTable = arg.getTempDataTable();
        var tempProcessDataTable = arg.getTempProcessDataTable();

        var sql = new StringBuilder();
        sql.append("""
            set @qian_ljyw = 0;
            set @dt_sjyw = 0;
            set @qian_yjs = 0;
            set @qian_sjs = 0;
            set @dt_yw = 0;
            set @dt_drsj = 0;
            set @dt_drljyw = 0;
            set @dt_dryj = 0;
            """);
        // 当日接单
        sql.append(getTodayDataSql(tempDataTable, tempProcessDataTable, "dt" + k, str_JDdt, "当日接单", arg.getProcessType()));
        // 当日应交
        sql.append(getTodayDataSql(tempDataTable, tempProcessDataTable, "dt" + k, str_YQdt, "当日应交", arg.getProcessType()));
        // 当日实际
        sql.append(getTodayDataSql(tempDataTable, tempProcessDataTable, "dt" + k, str_SJdt, "当日实际", arg.getProcessType()));
        // 当日达成率
        sql.append("""
            update a
            set a._column_ = round(c.xx * 100 / d.tt,4)
            from _process_table_ as a
            left outer join (
                select dt1 as xx,加工类 from _process_table_
                where 加工类='_process_type_' and 统计项='当日实际'
            ) as c on a.加工类=c.加工类
            left outer join (
                select dt1 as tt,加工类 from _process_table_
                where 加工类='_process_type_' and 统计项='当日应交'
            ) as d on c.加工类=d.加工类
            where a.加工类='_process_type_' and a.统计项='当日达成率' and d.tt>0;
            """
            .replace("_column_", "dt" + k)
            .replace("_process_type_", arg.getProcessType())
            .replace("_process_table_", tempProcessDataTable));
        // 延误交货
        sql.append(getTodayDataSql(tempDataTable, tempProcessDataTable, "dt" + k, str_SJdt + " and isnull(延误天数,0)>0", "延误交货", arg.getProcessType()));
        // 累计延误
        if (k != 1) {
            sql.append(getSetVariableSql(tempProcessDataTable, "qian_ljyw", "dt" + (k - 1), arg.getProcessType(), "累计延误"));
        }
        sql.append("""
            select @dt_sjyw = isnull(d.tt-c.xx, 0)
            from _process_table_ as a
            left outer join (
                select dt1 as xx,加工类 from _process_table_
                where 加工类='_process_type_' and 统计项='当日实际'
            ) as c on a.加工类=c.加工类
            left outer join (
                select dt1 as tt,加工类 from _process_table_
                where 加工类='_process_type_' and 统计项='当日应交'
            ) as d on c.加工类=d.加工类
            where a.加工类='_process_type_' and a.统计项='累计延误';
            if @dt_sjyw < 0
                set @dt_sjyw = 0;
            """.replace("_process_table_", tempProcessDataTable)
            .replace("_process_type_", arg.getProcessType())
        );
        // 当天延误交货
        sql.append(getSetVariableSql(tempProcessDataTable, "dt_yw", "dt" + k, arg.getProcessType(), "延误交货"));
        sql.append("""
            update a set a._column_ = @qian_ljyw + @dt_sjyw - @dt_yw
            from _process_table_ as a
            where 统计项='累计延误' and 加工类='_process_type_';
            """.replace("_column_", "dt" + k)
            .replace("_process_type_", arg.getProcessType())
            .replace("_process_table_", tempProcessDataTable)
        );
        // 累计达成率
        sql.append(getSetVariableSql(tempProcessDataTable, "dt_drsj", "dt" + k, arg.getProcessType(), "当日实际"));
        sql.append(getSetVariableSql(tempProcessDataTable, "dt_drljyw", "dt" + k, arg.getProcessType(), "累计延误"));
        sql.append(getSetVariableSql(tempProcessDataTable, "dt_dryj", "dt" + k, arg.getProcessType(), "当日应交"));
        if (k != 1) {
            sql.append(getSetVariableSql(tempProcessDataTable, "qian_yjs", "dt" + (k - 1), arg.getProcessType(), "当日应交"));
            sql.append(getSetVariableSql(tempProcessDataTable, "qian_sjs", "dt" + (k - 1), arg.getProcessType(), "当日实际"));
        }
        sql.append("""
            if @dt_dryj + @qian_yjs - @qian_sjs > 0
                update a set a._column_ = (@dt_drljyw + @dt_drsj) / (@dt_dryj + @qian_yjs - @qian_sjs)
                from _process_table_ as a
                where a.统计项='累计达成率' and a.加工类='_process_type_';
            """.replace("_column_", "dt" + k)
            .replace("_process_type_", arg.getProcessType())
            .replace("_process_table_", tempProcessDataTable)
        );
        return sql.toString();
    }

    private String getTodayDataSql(
        String tempDataTable, String tempProcessDataTable,
        String column, String query, String statisticsItem, String processType
    ) {
        return """
            update a
            set a._column_ = b.tt
            from _process_table_ as a, (
                select count(mo_no) as tt
                from _date_table_ with(nolock) where _query_
            ) as b
            where a.统计项 = '_statistics_item_' and a.加工类 = '_process_item_';
            """
            .replace("_column_", column)
            .replace("_process_table_", tempProcessDataTable)
            .replace("_date_table_", tempDataTable)
            .replace("_query_", query)
            .replace("_statistics_item_", statisticsItem)
            .replace("_process_item_", processType);
    }

    private String getSetVariableSql(
        String processTable,
        String variable,
        String column,
        String processType,
        String statisticsItem
    ) {
        return """
            select @_variable_ = isnull(_column_, 0) from _process_table_
            where 加工类='_process_type_' and 统计项='_statistics_item_'
            """.replace("_column_", column)
            .replace("_process_table_", processTable)
            .replace("_variable_", variable)
            .replace("_statistics_item_", statisticsItem)
            .replace("_process_type_", processType);
    }

    @Data
    private static class Dto {
        private String beginDate;
        private String startDate;
        private String endDate;
        private int k;
        private String dt;
        private String JDdt;
        private String YQdt;
        private String SJdt;
        private String tempDataTable;
        private String tempProcessDataTable;
        private String processType;
    }
}
