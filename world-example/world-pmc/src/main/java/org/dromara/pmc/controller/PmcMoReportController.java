package org.dromara.pmc.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.PmcProQueryDTO;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.WdzdhSbjdb;
import org.dromara.pmc.domain.vo.ResponsiblePerson;
import org.dromara.pmc.domain.vo.WdzdhSbjdbVo;
import org.dromara.pmc.service.PmcMoReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.sql.SQLException;
import java.util.List;

@RestController
@RequestMapping("/mo/report")
public class PmcMoReportController {

    @Autowired
    PmcMoReportService pmcMoReportService;

    @GetMapping("/responsible-person/list")
    public R<List<ResponsiblePerson>> getResponsiblePersonList() {
        List<ResponsiblePerson> list = pmcMoReportService.getResponsiblePersonList();
        return R.ok(list);
    }

    @GetMapping("/load/data")
    public R loadData() {
        List<WdzdhSbjdb> list = pmcMoReportService.getWdzdhSbjdbList();
        if (list != null && !list.isEmpty()) {
            pmcMoReportService.batchInsert(list);
        }
        pmcMoReportService.batchUpdateWdzdhSbjdb();
        return R.ok();
    }

    @GetMapping("/search/list")
    public TableDataInfo search(PmcProQueryDTO pmcProQueryDTO, PageQuery pageQuery) {
        return  pmcMoReportService.searchWdzdhSbjdbList(pmcProQueryDTO, pageQuery);
    }

    //修改PMC订单的PMC要求日期
    @PutMapping("/update")
    public R update(@RequestBody WdzdhSbjdb wdzdhSbjdb) {
        Boolean update = pmcMoReportService.update(wdzdhSbjdb);
        if (update) {
            return R.ok();
        }
        return R.fail();
    }

}
