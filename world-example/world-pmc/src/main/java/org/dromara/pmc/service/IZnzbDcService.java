package org.dromara.pmc.service;

import java.io.IOException;
import java.util.Collection;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.bo.ZnzbDcBo;
import org.dromara.pmc.domain.vo.ZnzbDcVo;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;

/**
 * 流程文件管理Service接口
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
public interface IZnzbDcService {

    /**
     * 查询流程文件管理
     *
     * @param id 主键
     * @return 流程文件管理
     */
    ZnzbDcVo queryById(Long id);

    /**
     * 分页查询流程文件管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 流程文件管理分页列表
     */
    TableDataInfo<ZnzbDcVo> queryPageList(ZnzbDcBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的流程文件管理列表
     *
     * @param bo 查询条件
     * @return 流程文件管理列表
     */
    List<ZnzbDcVo> queryList(ZnzbDcBo bo);

    /**
     * 新增流程文件管理
     *
     * @param bo 流程文件管理
     * @return 是否新增成功
     */
    Boolean insertByBo(ZnzbDcBo bo);

    /**
     * 修改流程文件管理
     *
     * @param bo 流程文件管理
     * @return 是否修改成功
     */
    Boolean updateByBo(ZnzbDcBo bo);

    /**
     * 校验并批量删除流程文件管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    R dealFile(MultipartFile file, String dcType) throws IOException;

    void deleteFile(List<Long> ids);

    void audit(List<Long> ids);

    void downloadFile(String fileName, HttpServletResponse response);
}
