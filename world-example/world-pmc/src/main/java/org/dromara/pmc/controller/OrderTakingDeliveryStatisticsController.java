package org.dromara.pmc.controller;

import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.OrderTakingDeliveryStatisticsVo;
import org.dromara.pmc.service.MerchandiserService;
import org.dromara.pmc.service.OrderTakingDeliveryStatisticsService;
import org.dromara.pmc.utils.SearchFormUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 半成品接单及交货统计
 */
@RestController
@RequestMapping("/order_taking_delivery")
@AllArgsConstructor
public class OrderTakingDeliveryStatisticsController {
    private final OrderTakingDeliveryStatisticsService service;
    private final MerchandiserService merchandiserService;

    @GetMapping("/merchandiser")
    public R<List<String>> getMerchandiser() throws SQLException {
        return R.ok(merchandiserService.getMerchandiser());
    }

    @GetMapping("/statistics")
    public R<List<OrderTakingDeliveryStatisticsVo>> list(PmcSearchForm form) {
        SearchFormUtil.setDefaultBargainDate(form, 7);
        return R.ok(service.getStatistics(form));
    }

    @RequestMapping("/statistics/download")
    public void download(HttpServletResponse response, PmcSearchForm form) {
        var data = list(form).getData();
        if (data.isEmpty()) {
            return;
        }
        var columnNames = new ArrayList<String>();
        columnNames.add("统计项");
        columnNames.add("跟单负责人");
        columnNames.add("加工类");
        var dates = new ArrayList<>(data.get(0).getDataMap().keySet().stream().toList());
        dates.sort(LocalDate::compareTo);
        for (var date : dates) {
            columnNames.add(date.toString());
        }
        var excelData = new ArrayList<List<Object>>();
        for (var vo : data) {
            var row = new ArrayList<>();
            row.add(vo.getStatisticalItem());
            row.add(vo.getMerchandiser());
            row.add(vo.getProcessType());
            for (var date : dates) {
                var value = vo.getDataMap().get(date);
                row.add(Objects.requireNonNullElse(value, 0.0));
            }
            excelData.add(row);
        }
        ExcelUtil.exportExcel(excelData, columnNames, "sheet1", response);
    }
}
