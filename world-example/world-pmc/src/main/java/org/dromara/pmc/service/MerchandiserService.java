package org.dromara.pmc.service;

import java.sql.SQLException;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.pmc.domain.MerchandiserDTO;
import org.dromara.pmc.domain.MerchandiserReqDTO;
import org.dromara.pmc.domain.SapScanMaster;


public interface MerchandiserService {
    List<SapScanMaster> getMerchandiserData(MerchandiserDTO dto) throws SQLException;

    R editMerchandiserData(MerchandiserReqDTO dto) throws SQLException;

    List<String> getMerchandiser() throws SQLException;
}
