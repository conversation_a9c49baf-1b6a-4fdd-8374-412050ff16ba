package org.dromara.pmc.controller;

import java.sql.SQLException;
import java.util.List;

import javax.annotation.Resource;

import org.dromara.common.core.domain.R;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.Statis3Vo;
import org.dromara.pmc.domain.vo.Statis4Vo;
import org.dromara.pmc.domain.vo.Statis5Vo;
import org.dromara.pmc.domain.vo.Statis5Vo2;
import org.dromara.pmc.domain.vo.Statis5Vo3;
import org.dromara.pmc.domain.vo.Statis7Vo;
import org.dromara.pmc.domain.vo.Statis8Vo;
import org.dromara.pmc.service.PmcReportService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/report")
public class PmcReportController {

    @Resource
    private PmcReportService pmcReportService;

    @GetMapping("/picUrl")
    public R getPicUrl(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        return pmcReportService.getPicUrl(searchForm);
    }

    @GetMapping("/statis3/search")
    public R statis3Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis3Vo> statis3Vos = pmcReportService.statis3Search(searchForm);
        return R.ok(statis3Vos);
    }

    @GetMapping("/statis4/search")
    public R statis4Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis4Vo> statis4Vos = pmcReportService.statis4Search(searchForm);
        return R.ok(statis4Vos);
    }

    /**
     * 交叉工序按钮
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis5/search")
    public R statis5Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis5Vo> statis5Vos = pmcReportService.statis5Search(searchForm);
        return R.ok(statis5Vos);
    }

    /**
     * 统计按钮
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis5/search2")
    public R statis5Search2(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis5Vo2> statis5Vo2s = pmcReportService.statis5Search2(searchForm);
        return R.ok(statis5Vo2s);
    }

    /**
     * 根据MO号查询扫描记录
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis5/search3")
    public R statis5Search3(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis5Vo3> statis5Vo3s = pmcReportService.statis5Search3(searchForm);
        return R.ok(statis5Vo3s);
    }

    /**
     * 根据MO号查询工艺制程
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis5/search4")
    public R statis5Search4(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        return pmcReportService.statis5Search4(searchForm);
    }

    /**
     * 查询按钮
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis5/search5")
    public R statis5Search5(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis5Vo2> statis5Vo2s = pmcReportService.statis5Search5(searchForm);
        return R.ok(statis5Vo2s);
    }

    /**
     * 电镀热处理统计信息
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis7/search")
    public R statis7Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis7Vo> statis7Vos = pmcReportService.statis7Search(searchForm);
        return R.ok(statis7Vos);
    }

    /**
     * 全工序外发交货统计信息
     *
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis8/search")
    public R statis8Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis8Vo> statis8Vos = pmcReportService.statis8Search(searchForm);
        return R.ok(statis8Vos);
    }

}
