package org.dromara.pmc.controller;

import java.sql.SQLException;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.pmc.domain.PmcSearchForm;
import org.dromara.pmc.domain.vo.MaterialRateVo;
import org.dromara.pmc.domain.vo.Statis1Vo;
import org.dromara.pmc.domain.vo.Statis2Vo;
import org.dromara.pmc.domain.vo.Statis6Vo;
import org.dromara.pmc.service.PmcMoManagerService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/mo")
public class PmcMoManagerController {
    @Autowired
    private PmcMoManagerService pmcMoManagerService;

    @GetMapping("/profitCenter/list")
    public R getMerchandiser() throws SQLException {
        return R.ok(pmcMoManagerService.getProfitCenter());
    }

    /**
     * 自动化PMC订单交货达成统计
     * 
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis1/search")
    public R statis1Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis1Vo> statis1Vos = pmcMoManagerService.statis1Search(searchForm);
        return R.ok(statis1Vos);
    }

    @Log(title = "自动化PMC订单交货达成统计导出", businessType = BusinessType.EXPORT)
    @PostMapping("/statis1/export")
    public void statis2Search(HttpServletResponse response, PmcSearchForm searchForm) throws SQLException {
        List<Statis1Vo> statis1Vos = pmcMoManagerService.statis1Search(searchForm);
        ExcelUtil.exportExcel(statis1Vos, "自动化PMC订单交货达成统计", Statis1Vo.class, response);
    }

    /**
     * 成品在制订单状况一览表
     * 
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis2/search")
    public R statis2Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis2Vo> statis2Vos = pmcMoManagerService.statis2Search(searchForm);
        return R.ok(statis2Vos);
    }

    /**
     * 装配部治具装配状态一览表加载数据
     * 
     * @return
     */
    @GetMapping("/statis6/loadData")
    public R statis6LoadData() {
        return pmcMoManagerService.statis6LoadData();
    }

    /**
     * 装配部治具装配状态一览表
     * 
     * @param searchForm
     * @return
     * @throws SQLException
     */
    @GetMapping("/statis6/search")
    public R statis6Search(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<Statis6Vo> statis6Vos = pmcMoManagerService.statis6Search(searchForm);
        return R.ok(statis6Vos);
    }

    /**
     * 装配部治具装配状态编辑
     * 
     * @param statis6Vo
     * @return
     */
    @PostMapping("/statis6/edit")
    public R editStatis6Data(@RequestBody Statis6Vo statis6Vo) {
        return pmcMoManagerService.editStatis6Data(statis6Vo);
    }

    /**
     * MO号读取制令物料信息
     * 
     * @param searchForm
     * @return
     * @throws SQLException
     */

    @GetMapping("/statis6/material")
    public R getMaterial(PmcSearchForm searchForm) throws SQLException {
        System.out.println(searchForm);
        List<MaterialRateVo> result = pmcMoManagerService.getMaterial(searchForm);
        return R.ok(result);
    }
}
