package org.dromara.pmc.service;


import org.dromara.pmc.domain.ProcessStatisticsQueryDTO;
import org.dromara.pmc.domain.vo.ProcessDetailVo;
import org.dromara.pmc.domain.vo.ProcessStatisticsVo;

import java.util.List;

public interface ProcessStatisticsService {

    /**
     * 查询流程统计列表
     * @param param 查询条件
     * @return 流程统计列表
     */
    List<ProcessStatisticsVo> listProcessStatistics(ProcessStatisticsQueryDTO param);
    /**
     * 查询外发加工明细列表
     * @param param 查询条件
     * @return 外发加工明细列表
     */
    List<ProcessDetailVo> listProcessDetail(ProcessStatisticsQueryDTO param);

    /**
     * 查询全工序统计列表
     * @param param 查询条件
     * @return 全工序统计列表
     */
    List<ProcessStatisticsVo> listEntireProcessStatistics(ProcessStatisticsQueryDTO param);

    /**
     * 查询外发全工序加工明细列表
     * @param param 查询条件
     * @return 外发全工序加工明细列表
     */
    List<ProcessDetailVo> listEntireProcessDetail(ProcessStatisticsQueryDTO param);


}
