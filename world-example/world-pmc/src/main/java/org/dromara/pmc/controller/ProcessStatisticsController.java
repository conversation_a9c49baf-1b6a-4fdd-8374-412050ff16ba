package org.dromara.pmc.controller;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.PLMFileTool;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.pmc.domain.PmcProQueryDTO;
import org.dromara.pmc.domain.ProcessStatisticsQueryDTO;
import org.dromara.pmc.domain.WdzdhSbjdb;
import org.dromara.pmc.domain.vo.ProcessDetailVo;
import org.dromara.pmc.domain.vo.ProcessStatisticsVo;
import org.dromara.pmc.domain.vo.ResponsiblePerson;
import org.dromara.pmc.service.PmcMoReportService;
import org.dromara.pmc.service.ProcessStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/process/report")
public class ProcessStatisticsController {

    @Autowired
    private ProcessStatisticsService processStatisticsService;


    @GetMapping("/processStatistics/list")
    public R<TableDataInfo> processStatistics(ProcessStatisticsQueryDTO query) {
        List<ProcessStatisticsVo> list = processStatisticsService.listProcessStatistics(query);

        List<ProcessDetailVo> details = processStatisticsService.listProcessDetail(query);

        return R.ok(new TableDataInfo(list, list.size(), details, details.size()));
    }

    @GetMapping("/processStatistics/entire/list")
    public R<TableDataInfo> entireProcessStatistics(ProcessStatisticsQueryDTO query) {
        List<ProcessStatisticsVo> list = processStatisticsService.listEntireProcessStatistics(query);

        List<ProcessDetailVo> details = processStatisticsService.listEntireProcessDetail(query);

        return R.ok(new TableDataInfo(list, list.size(), details, details.size()));
    }

    @GetMapping("/processStatistics/download/draw")
    public R<String> downloadDraw(@RequestParam String moNo, @RequestParam String drawingNo) {
        return R.ok(PLMFileTool.getMoUrlResult(moNo, drawingNo));
    }

}
