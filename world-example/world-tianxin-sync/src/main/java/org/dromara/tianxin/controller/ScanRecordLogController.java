package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.service.IScanRecordLogService;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
 * 前端访问路由地址为:/system/recordLog
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/recordLog")
public class ScanRecordLogController extends BaseController {

    private final IScanRecordLogService scanRecordLogService;

    /**
     * 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     */
    @SaCheckPermission("system:recordLog:list")
    @GetMapping("/list")
    public TableDataInfo<ScanRecordLogVo> list(ScanRecordLogBo bo, PageQuery pageQuery) {
        return scanRecordLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     */
    @SaCheckPermission("system:recordLog:export")
    @Log(title = "扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ScanRecordLogBo bo, HttpServletResponse response) {
        List<ScanRecordLogVo> list = scanRecordLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息", ScanRecordLogVo.class, response);
    }

    /**
     * 获取扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:recordLog:query")
    @GetMapping("/{id}")
    public R<ScanRecordLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(scanRecordLogService.queryById(id));
    }

    /**
     * 新增扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     */
    @SaCheckPermission("system:recordLog:add")
    @Log(title = "扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ScanRecordLogBo bo) {
        return toAjax(scanRecordLogService.insertByBo(bo));
    }

    /**
     * 修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     */
    @SaCheckPermission("system:recordLog:edit")
    @Log(title = "扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ScanRecordLogBo bo) {
        return toAjax(scanRecordLogService.updateByBo(bo));
    }

    /**
     * 删除扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:recordLog:remove")
    @Log(title = "扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(scanRecordLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
