package org.dromara.tianxin.utils;

/**
 * 收货单号类型解析器
 * 根据单号首字母确定对应的业务动作
 */
public class ReceivingActionResolver {

    /**
     * 根据单号首字母获取对应的业务动作
     *
     * @param djNo 单号（如：S2023110001, F2023110002, T2023110003）
     * @return 对应的业务动作，若无法识别返回 null
     */
    public static String resolveAction(String djNo) {
        if (djNo == null || djNo.isEmpty()) {
            return null;
        }

        // 提取首字母（统一转为大写处理）
        char firstChar = Character.toUpperCase(djNo.charAt(0));

        switch (firstChar) {
            case 'S':
                return "外发收货送检";
            case 'F':
                return "外发";
            case 'T':
                return "退供应商";
            default:
                // 可选：记录未知单号类型日志
                // LoggerFactory.getLogger(ReceivingActionResolver.class)
                //     .warn("未知单号类型: {}", djNo);
                return null;
        }
    }

    /**
     * 根据单号判断是否为外发收货场景
     *
     * @param djNo 单号
     * @return 是否为外发收货
     */
    public static boolean isOutsourcingReceive(String djNo) {
        String action = resolveAction(djNo);
        return "外发收货送检".equals(action);
    }

    /**
     * 根据单号判断是否为退供应商场景
     *
     * @param djNo 单号
     * @return 是否为退供应商
     */
    public static boolean isReturnToVendor(String djNo) {
        String action = resolveAction(djNo);
        return "退供应商".equals(action);
    }

    /**
     * 获取状态字符串（用于master表sta字段）
     * 格式：工序号+工序名称+动作
     */
    public static String buildStatusString(String processNo, String processName, String djNo) {
        String action = resolveAction(djNo);
        if (action == null) {
            return null;
        }
        return processNo + processName + action;
    }

    // 私有构造方法防止实例化
    private ReceivingActionResolver() {
        throw new UnsupportedOperationException("工具类不能实例化");
    }
}
