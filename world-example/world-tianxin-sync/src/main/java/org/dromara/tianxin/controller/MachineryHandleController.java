package org.dromara.tianxin.controller;

import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.tianxin.annotation.HandleScanRecordMaster;
import org.dromara.tianxin.domain.bo.PurchaseInfoBo;
import org.dromara.tianxin.domain.bo.ReceiveInfoHeadBo;
import org.dromara.tianxin.domain.bo.ReceiveInfoDetailBo;
import org.dromara.tianxin.domain.dto.MachineryQRCodeResultDTO;
import org.dromara.tianxin.domain.dto.ReceiveInfoDTO;
import org.dromara.tianxin.domain.vo.PurchaseInfoVo;
import org.dromara.tianxin.service.IMachineryHandleService;
import org.dromara.tianxin.service.IPurchaseInfoService;
import org.dromara.tianxin.service.IReceiveInfoDetailService;
import org.dromara.tianxin.service.IReceiveInfoHeadService;
import org.dromara.tianxin.service.qrcodeparse.QRCodeDef;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/machinery")
public class MachineryHandleController {
    @DubboReference
    private RemoteUserService remoteUserService;

    private final IPurchaseInfoService purchaseInfoService;

    private final IReceiveInfoHeadService receiveInfoHeadService;

    private final IReceiveInfoDetailService receiveInfoDetailService;

    private final IMachineryHandleService machineryHandleService;


    /**
     * 获取用户信息
     *
     * @param username 用户ID
     * @return 用户信息
     */
    @GetMapping("/userInfo/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username) {
        String tenantId = LoginHelper.getTenantId();
        return R.ok(remoteUserService.getUserInfo(username, tenantId));
    }


    /**
     * 扫描条码
     * @return
     */
    @GetMapping("/scan/{code}")
    public R<PurchaseInfoVo> scanBarcode(@PathVariable("code") String code, HttpServletRequest request){
        MachineryQRCodeResultDTO resultDTO = QRCodeDef.matchQRCodeParsing("JJPIE",code);
        if(resultDTO == null){
            return R.fail("条码解析失败");
        }
        PurchaseInfoBo bo = new PurchaseInfoBo();
        bo.setMoNo(resultDTO.getMokey());
        if(resultDTO.getWorksetp() != null){
            bo.setZcNo(resultDTO.getWorksetp());
        }
        List<PurchaseInfoVo> list = purchaseInfoService.queryList(bo);
        if(list.isEmpty()){
            return R.fail("未查询到相关数据");
        }
        // 获取用户IP地址
        String userIp = getClientIpAddress(request);
        PurchaseInfoVo resultVo = list.get(0);
        resultVo.setHost(userIp);
        return R.ok(resultVo);
    }

    /**
     * 解析日期字符串，支持多种格式
     * @param dateStr 日期字符串
     * @return 解析后的日期
     * @throws ParseException 如果所有格式都无法解析
     */
    private Date parseDate(String dateStr) throws ParseException {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }

        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm"
        };

        for (String pattern : patterns) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(pattern);
                // 设置lenient为false，严格匹配日期格式
                sdf.setLenient(false);
                return sdf.parse(dateStr.trim());
            } catch (ParseException e) {
                // 继续尝试下一个格式
                continue;
            }
        }

        throw new ParseException("无法解析日期: " + dateStr, 0);
    }

    /**
     * 保存收货信息
     * @param receiveInfoDTO 收货信息
     * @return 操作结果
     */
    @PostMapping("/saveReceiveInfo")
    @Transactional(rollbackFor = Exception.class)
    @HandleScanRecordMaster
    public R<Void> saveReceiveInfo(@RequestBody ReceiveInfoDTO receiveInfoDTO) {
        try {
           // 构建并保存收货头信息
            ReceiveInfoHeadBo headBo = new ReceiveInfoHeadBo();
            headBo.setDjNo(receiveInfoDTO.getDjNo());
            headBo.setLpnNo(receiveInfoDTO.getLpnNo());
            headBo.setVendorCode(receiveInfoDTO.getVendorCode());
            headBo.setVendorSnm(receiveInfoDTO.getVendorSnm());
            headBo.setUsr(receiveInfoDTO.getUsr());
            headBo.setUsrName(receiveInfoDTO.getUsrName());
            headBo.setDjDate(new Date());
            headBo.setSysDate(new Date());
            headBo.setDjSta("9");

            receiveInfoHeadService.insertByBo(headBo);

            // 构建并保存收货明细信息
            List<ReceiveInfoDTO.ReceiveInfoDetailItem> items = receiveInfoDTO.getItems();
            for (ReceiveInfoDTO.ReceiveInfoDetailItem item : items) {
                machineryHandleService.checkBeforeReceive(item.getMoNo(), item.getZcNo(),item.getActualQty());
                ReceiveInfoDetailBo detailBo = new ReceiveInfoDetailBo();
                detailBo.setDjNo(item.getDjNo());
                detailBo.setDjItm(item.getDjItm());
                detailBo.setDjSta(item.getDjSta());
                detailBo.setPoType(item.getPoType());
                detailBo.setLpnNo(item.getLpnNo());
                detailBo.setMoNo(item.getMoNo());
                detailBo.setZcNo(item.getZcNo());
                detailBo.setZcName(item.getZcName());
                detailBo.setDjSta("9");
                // 处理日期字段
                if (item.getDjDate() != null && !item.getDjDate().isEmpty()) {
                    try {
                        detailBo.setDjDate(parseDate(item.getDjDate()));
                    } catch (ParseException e) {
                        return R.fail("DJ日期格式错误: " + e.getMessage());
                    }
                }

                detailBo.setVendorCode(item.getVendorCode());
                detailBo.setVendorSnm(item.getVendorSnm());
                detailBo.setPurchaseNum(item.getPurchaseNum());
                detailBo.setPurchaseItm(item.getPurchaseItm());
                detailBo.setPrdNo(item.getPrdNo());
                detailBo.setPrdName(item.getPrdName());
                detailBo.setPrdDesc(item.getPrdDesc());
                detailBo.setPoQty(item.getPoQty());
                detailBo.setActualQty(item.getActualQty());
                detailBo.setReturnQty(item.getReturnQty());
                detailBo.setOuterQty(item.getOuterQty());
                detailBo.setUt(item.getUt());
                detailBo.setIsProvideMaterials(item.getIsProvideMaterials());
                detailBo.setCustNo(item.getCustNo());
                detailBo.setSpc(item.getSpc());
                detailBo.setMark(item.getMark());
                detailBo.setStoreCw(item.getStoreCw());
                detailBo.setUsr(item.getUsr());
                detailBo.setUsrName(item.getUsrName());

                // 处理SYS_DATE字段
                if (item.getSysDate() != null && !item.getSysDate().isEmpty()) {
                    try {
                        detailBo.setSysDate(parseDate(item.getSysDate()));
                    } catch (ParseException e) {
                        return R.fail("系统日期格式错误: " + e.getMessage());
                    }
                }

                detailBo.setHost(item.getHost());
                detailBo.setRem(item.getRem());

                receiveInfoDetailService.insertByBo(detailBo);
            }

        } catch (Exception e) {
            throw new RuntimeException("保存失败: " + e.getMessage(), e);
        }

        return R.ok();
    }

    /**
     * 获取客户端真实IP地址
     * @param request HttpServletRequest对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
            // 多个IP时取第一个
            if (ip.contains(",")) {
                ip = ip.split(",")[0];
            }
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}
