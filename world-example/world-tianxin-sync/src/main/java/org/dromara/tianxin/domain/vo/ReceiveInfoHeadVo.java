package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.tianxin.domain.ReceiveInfoHead;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 【请填写功能名称】视图对象 receive_info_head
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ReceiveInfoHead.class)
public class ReceiveInfoHeadVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 单据状态
     */
    @ExcelProperty(value = "单据状态")
    private String djSta;

    /**
     * ASN单号
     */
    @ExcelProperty(value = "ASN单号")
    private String djNo;

    /**
     * 托盘号
     */
    @ExcelProperty(value = "托盘号")
    private String lpnNo;

    /**
     * 供应商简称
     */
    @ExcelProperty(value = "供应商简称")
    private String vendorSnm;

    /**
     * 供应商代号
     */
    @ExcelProperty(value = "供应商代号")
    private String vendorCode;

    /**
     * 单据日期
     */
    @ExcelProperty(value = "单据日期")
    private Date djDate;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String rem;

    /**
     * 操作员工号
     */
    @ExcelProperty(value = "操作员工号")
    private String usr;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String usrName;

    /**
     * 操作日期
     */
    @ExcelProperty(value = "操作日期")
    private Date sysDate;

    /**
     * 操作电脑名
     */
    @ExcelProperty(value = "操作电脑名")
    private String host;


}
