package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.tianxin.domain.bo.ReceiveInfoHeadBo;
import org.dromara.tianxin.domain.vo.ReceiveInfoHeadVo;
import org.dromara.tianxin.service.IReceiveInfoHeadService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 * 前端访问路由地址为:/system/infoHead
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/receipt")
public class ReceiveInfoHeadController extends BaseController {

    private final IReceiveInfoHeadService receiveInfoHeadService;

    @DubboReference
    private RemoteUserService remoteUserService;


    /**
     * 获取用户信息
     *
     * @param username 用户ID
     * @return 用户信息
     */
    @GetMapping("/userInfo/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username) {
        String tenantId = LoginHelper.getTenantId();
        return R.ok(remoteUserService.getUserInfo(username, tenantId));
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("system:infoHead:list")
    @GetMapping("/list")
    public TableDataInfo<ReceiveInfoHeadVo> list(ReceiveInfoHeadBo bo, PageQuery pageQuery) {
        return receiveInfoHeadService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("system:infoHead:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ReceiveInfoHeadBo bo, HttpServletResponse response) {
        List<ReceiveInfoHeadVo> list = receiveInfoHeadService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", ReceiveInfoHeadVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:infoHead:query")
    @GetMapping("/{id}")
    public R<ReceiveInfoHeadVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(receiveInfoHeadService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("system:infoHead:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ReceiveInfoHeadBo bo) {
        return toAjax(receiveInfoHeadService.insertByBo(bo));
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("system:infoHead:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ReceiveInfoHeadBo bo) {
        return toAjax(receiveInfoHeadService.updateByBo(bo));
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:infoHead:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(receiveInfoHeadService.deleteWithValidByIds(List.of(ids), true));
    }


}
