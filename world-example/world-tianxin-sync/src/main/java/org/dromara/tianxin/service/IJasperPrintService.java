package org.dromara.tianxin.service;

import java.util.List;
import java.util.Map;

/**
 * Jasper报表打印服务接口
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
public interface IJasperPrintService {

    /**
     * 生成PDF预览数据
     *
     * @param templateName 模板名称
     * @param parameters 报表参数
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    byte[] generatePdfPreview(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList);

    /**
     * 生成PDF预览数据（使用SQL查询）
     *
     * @param templateName 模板名称
     * @param parameters 报表参数
     * @param sqlWhere SQL WHERE条件
     * @return PDF字节数组
     */
    byte[] generatePdfPreviewWithSql(String templateName, Map<String, Object> parameters, String sqlWhere);

    /**
     * 获取可用的报表模板列表
     *
     * @return 模板名称列表
     */
    List<String> getAvailableTemplates();

    /**
     * 快速生成PDF - 高性能版本
     *
     * @param templateName 模板名称
     * @param parameters 报表参数
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    byte[] generatePdfFast(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList);

    /**
     * 快速生成PDF - 极简版本（无参数）
     *
     * @param templateName 模板名称
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    byte[] generatePdfFast(String templateName, List<Map<String, Object>> dataList);
}
