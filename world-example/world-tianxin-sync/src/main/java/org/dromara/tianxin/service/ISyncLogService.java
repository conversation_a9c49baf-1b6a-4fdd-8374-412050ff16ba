package org.dromara.tianxin.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.SyncLogBo;
import org.dromara.tianxin.domain.vo.SyncLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 天心天思数据同步日志Service接口
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface ISyncLogService {

    /**
     * 查询天心天思数据同步日志
     *
     * @param id 主键
     * @return 同步日志
     */
    SyncLogVo queryById(Long id);

    /**
     * 查询天心天思数据同步日志列表
     *
     * @param syncLog 查询条件
     * @return 同步日志列表
     */
    TableDataInfo<SyncLogVo> queryPageList(SyncLogBo syncLog, PageQuery pageQuery);

    /**
     * 查询天心天思数据同步日志列表
     *
     * @param syncLog 查询条件
     * @return 同步日志列表
     */
    List<SyncLogVo> queryList(SyncLogBo syncLog);

    /**
     * 新增天心天思数据同步日志
     *
     * @param bo 同步日志业务对象
     * @return 是否成功
     */
    Boolean insertByBo(SyncLogBo bo);

    /**
     * 修改天心天思数据同步日志
     *
     * @param bo 同步日志业务对象
     * @return 是否成功
     */
    Boolean updateByBo(SyncLogBo bo);

    /**
     * 校验并批量删除天心天思数据同步日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 是否成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 记录同步日志
     *
     * @param module       模块
     * @param dataType     数据类型
     * @param dataId       数据ID
     * @param syncStatus   同步状态
     * @param errorMessage 错误信息
     * @param requestData  请求数据
     * @param responseData 响应数据
     * @param responseTime 响应时间
     */
    void recordSyncLog(String module, String dataType, String dataId, Integer syncStatus,
                       String errorMessage, String requestData, String responseData, Long responseTime);

    /**
     * 查询同步统计信息
     *
     * @param module   模块
     * @param dataType 数据类型
     * @return 统计信息
     */
    Object getSyncStatistics(String module, String dataType);
}
