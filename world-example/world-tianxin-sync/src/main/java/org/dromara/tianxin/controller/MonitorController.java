package org.dromara.tianxin.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.service.ISyncService;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 天心天思数据同步监控控制器
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/tianxin/monitor")
public class MonitorController extends BaseController {

    private final ISyncService syncService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public R<Map<String, Object>> healthCheck() {
        log.info("执行天心天思API健康检查");

        Map<String, Object> health = new HashMap<>();

        try {
            // 检查同步状态
            Object syncStatus = syncService.checkSyncStatus();
            health.put("syncStatus", syncStatus);

            // 系统状态
            health.put("systemStatus", "UP");
            health.put("timestamp", System.currentTimeMillis());

            log.info("天心天思API健康检查完成");
            return R.ok(health);
        } catch (Exception e) {
            log.error("天心天思API健康检查失败", e);
            health.put("systemStatus", "DOWN");
            health.put("error", e.getMessage());
            health.put("timestamp", System.currentTimeMillis());
            return R.fail("健康检查失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统信息
     */
    @SaCheckPermission("tianxin:monitor:info")
    @GetMapping("/info")
    public R<Map<String, Object>> getSystemInfo() {
        Map<String, Object> info = new HashMap<>();

        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        info.put("jvm", Map.of(
                "totalMemory", runtime.totalMemory(),
                "freeMemory", runtime.freeMemory(),
                "maxMemory", runtime.maxMemory(),
                "usedMemory", runtime.totalMemory() - runtime.freeMemory()
        ));

        // 系统信息
        info.put("system", Map.of(
                "osName", System.getProperty("os.name"),
                "osVersion", System.getProperty("os.version"),
                "javaVersion", System.getProperty("java.version"),
                "userTimeZone", System.getProperty("user.timezone")
        ));

        // 应用信息
        info.put("application", Map.of(
                "name", "world-tianxin-sync",
                "version", "1.0.0",
                "description", "天心天思数据同步服务"
        ));

        return R.ok(info);
    }

    /**
     * 获取API状态
     */
    @SaCheckPermission("tianxin:monitor:api")
    @GetMapping("/api/status")
    public R<Object> getApiStatus() {
        return R.ok(syncService.checkSyncStatus());
    }

    /**
     * 获取同步统计
     */
    @SaCheckPermission("tianxin:monitor:statistics")
    @GetMapping("/statistics")
    public R<Object> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        // 获取各模块同步统计
        statistics.put("product", syncService.checkSyncStatus());
        statistics.put("customer", syncService.checkSyncStatus());
        statistics.put("supplier", syncService.checkSyncStatus());

        return R.ok(statistics);
    }
}
