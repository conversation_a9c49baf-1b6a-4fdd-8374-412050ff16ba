package org.dromara.tianxin.service.impl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.tianxin.domain.CusEmail;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.mapper.CusEmailMapper;
import org.dromara.tianxin.service.IEmailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮件服务实现类
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EmailServiceImpl implements IEmailService {

    @DubboReference
    private RemoteUserService remoteUserService;

    @Autowired
    private CusEmailMapper cusEmailMapper;

    @Value("${tianxin.email.default-recipients:<EMAIL>}")
    private String defaultRecipients;

    @Value("${tianxin.email.wfbq-default-subject:}")
    private String defaultSubject;

    @Value("${tianxin.email.temp-dir:${java.io.tmpdir}}")
    private String tempDir;

    @Value("${tianxin.email.wfbq-default-content:}")
    private String defaultContent;

    @Override
    @SuppressWarnings("unchecked")
    public void sendProcessLabEmail(MultipartFile[] files, String content, String tableData, String subject) {
        try {
            // 将tableData转OutsourceVo列表
            List<OutsourceVo> outsourceList = JSONUtil.toList(JSONUtil.parseArray(tableData), OutsourceVo.class);

            // OutsourceVo列表找出供应商简称
            List<String> vendorSnmList = outsourceList.stream().map(OutsourceVo::getVendorSnm).distinct().toList();
            // 根据供应商简称获取邮箱
            LambdaQueryWrapper<CusEmail> lqw = new LambdaQueryWrapper<>();
            lqw.eq(CusEmail::getCusSnm, vendorSnmList.get(0));
            CusEmail cusEmail = cusEmailMapper.selectOne(lqw);

            // 发送邮件
            String recipients = Objects.nonNull(cusEmail) && StrUtil.isNotBlank(cusEmail.getEmailAdd())
                ? cusEmail.getEmailAdd() : defaultRecipients;
            String emailSubject = StrUtil.isNotBlank(subject) ? subject : defaultSubject;
            String emailContent = StrUtil.isNotBlank(content) ? content : defaultContent;
            String cc = remoteUserService.selectEmailById(LoginHelper.getUserId());
            sendEmailWithAttachments(recipients, cc, emailSubject, emailContent, files);

            log.info("外发加工标签邮件发送成功，数据条数：{}", outsourceList.size());

        } catch (Exception e) {
            log.error("发送外发加工标签邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendSimpleEmail(String to, String subject, String content) {
        try {
            MailUtils.sendText(to, subject, content);
            log.info("简单邮件发送成功，收件人：{}", to);
        } catch (Exception e) {
            log.error("发送简单邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendHtmlEmail(String to, String subject, String content) {
        try {
            MailUtils.sendHtml(to, subject, content);
            log.info("HTML邮件发送成功，收件人：{}", to);
        } catch (Exception e) {
            log.error("发送HTML邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }

    @Override
    public void sendEmailWithAttachments(String to, String subject, String content, MultipartFile[] files) {
        sendEmailWithAttachments(to, null, subject, content, files);
    }

    @Override
    public void sendEmailWithAttachments(String to, String cc, String subject, String content, MultipartFile[] files) {
        try {
            List<File> attachmentFiles = new ArrayList<>();

            // 处理附件
            if (files != null) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        // 获取原始文件名
                        String originalFilename = file.getOriginalFilename();

                        // 创建临时目录路径
                        Path tempDirPath = Path.of(tempDir);

                        // 确保临时目录存在
                        if (!Files.exists(tempDirPath)) {
                            Files.createDirectories(tempDirPath);
                        }

                        // 创建与原始文件名完全一致的临时文件
                        Path finalTempFilePath;
                        if (originalFilename != null && !originalFilename.isEmpty()) {
                            finalTempFilePath = tempDirPath.resolve(originalFilename);

                            // 如果文件已存在，先删除
                            if (Files.exists(finalTempFilePath)) {
                                Files.delete(finalTempFilePath);
                            }
                        } else {
                            // 如果没有原始文件名，则创建一个默认的临时文件
                            finalTempFilePath = Files.createTempFile(tempDirPath, "attachment_", originalFilename);
                        }

                        // 复制文件内容到临时文件
                        Files.copy(file.getInputStream(), finalTempFilePath, StandardCopyOption.REPLACE_EXISTING);
                        attachmentFiles.add(finalTempFilePath.toFile());
                    }
                }
            }

            // 发送邮件
            if (attachmentFiles.isEmpty()) {
                if (cc == null || cc.isEmpty()) {
                    MailUtils.sendHtml(to, subject, content);
                } else {
                    MailUtils.send(to, cc, null, subject, content, true);
                }
            } else {
                if (cc == null || cc.isEmpty()) {
                    MailUtils.sendHtml(to, subject, content, attachmentFiles.toArray(new File[0]));
                } else {
                    MailUtils.send(to, cc, null, subject, content, true, attachmentFiles.toArray(new File[0]));
                }
            }

            // 清理临时文件
            for (File tempFile : attachmentFiles) {
                try {
                    Files.deleteIfExists(tempFile.toPath());
                } catch (IOException e) {
                    log.warn("删除临时文件失败：{}", tempFile.getAbsolutePath(), e);
                }
            }

            log.info("带附件邮件发送成功，收件人：{}，抄送人：{}，附件数量：{}", to, cc, files != null ? files.length : 0);
        } catch (Exception e) {
            log.error("发送带附件邮件失败", e);
            throw new RuntimeException("发送邮件失败：" + e.getMessage());
        }
    }
}
