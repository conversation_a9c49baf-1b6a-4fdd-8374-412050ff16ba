package org.dromara.tianxin.service.qrcodeparse;


import cn.hutool.extra.spring.SpringUtil;
import org.dromara.common.core.exception.ServiceException;

public enum QRCodeDef {
	//机加件收货条码解析规则
	JJPIE("JJPIE","jjPieQRCodeParsing"){
		@Override
		public <T, R> R parsing(T t) {
			return SpringUtil.getBean(QRCodeMethodFactory.class).run(this.getQRMethod(), t);
		}
	},
	//入库收货条码解析规则
	QAVERDICT("QAVERDICT","qaVerdictQRCodeParsing"){
		@Override
		public <T, R> R parsing(T t) {
			return SpringUtil.getBean(QRCodeMethodFactory.class).run(this.getQRMethod(), t);
		}
	},
	//QA转序外发/采购退解析条码规则
	QAZXORTC("QAZXORTC","qaZxorTcQRCodeParsing"){
		@Override
		public <T, R> R parsing(T t) {
			return SpringUtil.getBean(QRCodeMethodFactory.class).run(this.getQRMethod(), t);
		}
	},
	//标准间条码解析规则
	STANDARDPARTS("STANDARDPARTS","standardPartsQRCodeParsing"){
		@Override
		public <T, R> R parsing(T t) {
			return SpringUtil.getBean(QRCodeMethodFactory.class).run(this.getQRMethod(), t);
		}
	};

	private String QRType;

	private String QRMethod;
	public abstract <T,R> R parsing(T t);

	public static  <T,R> R matchQRCodeParsing(String qrType ,T t){
		for (QRCodeDef item : QRCodeDef.values()) {
			if (item.name().equals(qrType)) {
				return item.parsing(t);
			}
		}
		throw new ServiceException("未找到条码解析申明:"+ qrType);
	}

	QRCodeDef(String QRType, String qrmethod) {
		this.QRType = QRType;
		this.QRMethod = qrmethod;
	}

	public String getQRType() {
		return QRType;
	}

	public void setQRType(String QRType) {
		this.QRType = QRType;
	}

	public String getQRMethod() {
		return QRMethod;
	}

	public void setQRMethod(String QRMethod) {
		this.QRMethod = QRMethod;
	}
}
