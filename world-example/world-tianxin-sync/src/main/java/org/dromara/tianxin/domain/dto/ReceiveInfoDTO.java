package org.dromara.tianxin.domain.dto;

import lombok.Data;

import java.util.List;

/**
 * 收货信息DTO
 *
 * <AUTHOR>
 * @date 2025-09-18
 */
@Data
public class ReceiveInfoDTO {
    /**
     * 操作员工号
     */
    private String usr;

    /**
     * 姓名
     */
    private String usrName;

    /**
     * 托盘号
     */
    private String lpnNo;

    /**
     * MO号
     */
    private String moNumber;

    /**
     * 单号
     */
    private String djNo;

    /**
     * 供应商简称
     */
    private String vendorSnm;

    /**
     * 供应商代号
     */
    private String vendorCode;

    /**
     * 工序号
     */
    private String process;

    /**
     * PO数量
     */
    private Integer poQuantity;

    /**
     * 明细列表
     */
    private List<ReceiveInfoDetailItem> items;

    @Data
    public static class ReceiveInfoDetailItem {
        private String djNo;
        private String djItm;
        private String djSta;
        private String poType;
        private String lpnNo;
        private String moNo;
        private String zcNo;
        private String zcName;
        private String djDate;
        private String vendorCode;
        private String vendorSnm;
        private String purchaseNum;
        private String purchaseItm;
        private String prdNo;
        private String prdName;
        private String prdDesc;
        private Long poQty;
        private Long actualQty;
        private Long returnQty;
        private Long outerQty;
        private String ut;
        private String isProvideMaterials;
        private String partdrawno;
        private String custNo;
        private String spc;
        private String mark;
        private String storeCw;
        private String usr;
        private String usrName;
        private String sysDate;
        private String host;
        private String rem;
    }
}
