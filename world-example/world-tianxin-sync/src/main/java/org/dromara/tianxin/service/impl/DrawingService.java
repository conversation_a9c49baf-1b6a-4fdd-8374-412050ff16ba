package org.dromara.tianxin.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.PLMFileTool;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ip.AddressUtils;
import org.dromara.common.core.utils.smbj.SmbjConnect;
import org.dromara.tianxin.config.SmbConfig;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;

/**
 * 图纸服务类
 * 根据客户端IP地址选择不同的图纸获取方式
 * - 中国IP：使用PLM系统
 * - 非中国IP或越南IP：使用SMB共享文件夹
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrawingService {

    private final SmbConfig smbConfig;
    private final SimpleFileService simpleFileService;

    /**
     * 根据IP地址和订单信息获取图纸
     *
     * @param dwgNo 图号
     * @return 图纸URL或Base64编码的图纸数据
     */
    public String getDrawingByLocation(String dwgNo) {
        try {
            // 输入参数验证
            if (StringUtils.isBlank(dwgNo)) {
                throw new IllegalArgumentException("图号为空，无法获取图纸");
            }
            // 获取客户端IP地址
            String clientIP = ServletUtils.getClientIP();
            // 获取IP地址对应的地理位置
            String location = AddressUtils.getRealAddressByIP(clientIP);
            // 判断是否为非中国IP或越南IP
            if (isNonChinaOrVietnamIP(location) && smbConfig.isEnabled()) {
                return getDrawingFromSMB(dwgNo);
            } else {
                return getDrawingFromPLM(dwgNo);
            }
        } catch (Exception e) {
            log.error("获取图纸失败, 图号: {}", dwgNo, e);
            // 如果出现异常，默认使用PLM系统
            return getDrawingFromPLM(dwgNo);
        }
    }

    /**
     * 判断是否为非中国IP或越南IP
     *
     * @param location 地理位置信息
     * @return true表示非中国IP或越南IP
     */
    private boolean isNonChinaOrVietnamIP(String location) {
        if (StringUtils.isBlank(location)) {
            return true; // 未知位置，使用SMB
        }

        // 检查是否为中国IP
        boolean isChina = location.contains("中国") ||
            location.contains("China") ||
            location.contains("CN");

        // 检查是否为越南IP
        boolean isVietnam = location.contains("越南") ||
            location.contains("Vietnam") ||
            location.contains("VN");

        // 如果不是中国IP，或者是越南IP，则使用SMB
        return !isChina || isVietnam;
    }


    /**
     * 从PLM系统获取图纸
     *
     * @param dwgNo 图号
     * @return PLM图纸URL
     */
    private String getDrawingFromPLM(String dwgNo) {
        try {
            String plmUrl = PLMFileTool.getMoUrlResult("", dwgNo);
            if ("DATA_NOT_FOUND".equals(plmUrl)) {
                throw new RuntimeException("PLM系统中未找到对应的图纸, 图号: " + dwgNo);
            }
            return plmUrl;
        } catch (Exception e) {
            log.error("从PLM系统获取图纸失败, 图号: {}", dwgNo, e);
            throw new RuntimeException(e.getMessage());
        }
    }

    /**
     * 从SMB共享文件夹获取图纸（优化版本）
     *
     * @param dwgNo 图号
     * @return 图纸访问URL
     */
    private String getDrawingFromSMB(String dwgNo) {
        SmbjConnect smbjConnect = null;
        try {
            // 验证图号格式，防止路径注入攻击
            if (!isValidDrawingNumber(dwgNo)) {
                throw new IllegalArgumentException("图号格式无效: " + dwgNo);
            }
            // 直接创建新连接，避免连接池超时问题
            smbjConnect = new SmbjConnect(
                smbConfig.getHost(),
                smbConfig.getUsername(),
                smbConfig.getPassword()
            );
            String dwgFilePath = smbConfig.getBasePath() + dwgNo + ".pdf";
            // 直接尝试读取文件，如果文件不存在会抛出异常
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                smbjConnect.readSmallFile(smbConfig.getShareName(), dwgFilePath, outputStream);
                byte[] drawingData = outputStream.toByteArray();
                // 保存到临时文件并返回访问URL
                String fileUrl = simpleFileService.saveBinaryToTempFile(drawingData, dwgNo);
                return fileUrl;

            } catch (Exception e) {
                throw new RuntimeException("SMB共享文件夹中未找到对应的图纸文件, 图号: " + dwgNo);
            }

        } catch (Exception e) {
            throw new RuntimeException("从SMB共享文件夹获取图纸失败: " + e.getMessage());
        } finally {
            // 不归还连接到池中，直接关闭连接避免超时问题
            if (smbjConnect != null) {
                try {
                    smbjConnect.close();
                } catch (Exception e) {
                    log.warn("关闭SMB连接时发生异常", e);
                }
            }
        }
    }

    /**
     * 验证图号格式，防止路径注入攻击
     *
     * @param dwgNo 图号
     * @return true表示格式有效
     */
    private boolean isValidDrawingNumber(String dwgNo) {
        if (StringUtils.isBlank(dwgNo)) {
            return false;
        }
        // 只允许字母、数字、下划线和连字符
        return dwgNo.matches("^[a-zA-Z0-9_-]+$");
    }

    /**
     * 直接获取图纸文件流
     *
     * @param dwgNo 图号
     * @return 图纸文件字节数组
     */
    public byte[] getDrawingFileStream(String dwgNo) {
        try {
            // 输入参数验证
            if (StringUtils.isBlank(dwgNo)) {
                throw new IllegalArgumentException("图号为空，无法获取图纸");
            }
            // 获取客户端IP地址
            String clientIP = ServletUtils.getClientIP();
            // 获取IP地址对应的地理位置
            String location = AddressUtils.getRealAddressByIP(clientIP);
            // 判断是否为非中国IP或越南IP
            if (isNonChinaOrVietnamIP(location) && smbConfig.isEnabled()) {
                return getDrawingFileStreamFromSMB(dwgNo);
            } else {
                return getDrawingFileStreamFromPLM(dwgNo);
            }
        } catch (Exception e) {
            log.error("获取图纸文件流失败, 图号: {}", dwgNo, e);
            throw new RuntimeException("获取图纸文件流失败: " + e.getMessage());
        }
    }

    /**
     * 从SMB共享文件夹获取图纸文件流
     *
     * @param dwgNo 图号
     * @return 图纸文件字节数组
     */
    private byte[] getDrawingFileStreamFromSMB(String dwgNo) {
        SmbjConnect smbjConnect = null;
        try {
            // 验证图号格式，防止路径注入攻击
            if (!isValidDrawingNumber(dwgNo)) {
                throw new IllegalArgumentException("图号格式无效: " + dwgNo);
            }
            // 直接创建新连接，避免连接池超时问题
            smbjConnect = new SmbjConnect(
                smbConfig.getHost(),
                smbConfig.getUsername(),
                smbConfig.getPassword()
            );
            String dwgFilePath = smbConfig.getBasePath() + dwgNo + ".pdf";
            // 直接读取文件流
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                smbjConnect.readSmallFile(smbConfig.getShareName(), dwgFilePath, outputStream);
                byte[] drawingData = outputStream.toByteArray();
                return drawingData;

            } catch (Exception e) {
                if (e.getMessage() != null && (e.getMessage().contains("timeout") ||
                    e.getMessage().contains("Timeout") || e.getMessage().contains("Read timed out"))) {
                    try {
                        return getDrawingFileStreamFromPLM(dwgNo);
                    } catch (Exception plmException) {
                        throw new RuntimeException("SMB和PLM系统都无法获取图纸文件, 图号: " + dwgNo + ", SMB错误: " + e.getMessage() + ", PLM错误: " + plmException.getMessage());
                    }
                } else {
                    throw new RuntimeException("SMB共享文件夹中未找到对应的图纸文件, 图号: " + dwgNo);
                }
            }

        } catch (Exception e) {
            throw new RuntimeException("从SMB共享文件夹获取图纸文件流失败: " + e.getMessage());
        } finally {
            // 不归还连接到池中，直接关闭连接避免超时问题
            if (smbjConnect != null) {
                try {
                    smbjConnect.close();
                } catch (Exception e) {
                    log.warn("关闭SMB连接时发生异常", e);
                }
            }
        }
    }

    /**
     * 从PLM系统获取图纸文件流
     *
     * @param dwgNo 图号
     * @return 图纸文件字节数组
     */
    private byte[] getDrawingFileStreamFromPLM(String dwgNo) {
        try {
            // 获取PLM文件URL
            String plmUrl = PLMFileTool.getMoUrlResult("", dwgNo);
            if ("DATA_NOT_FOUND".equals(plmUrl)) {
                throw new RuntimeException("PLM系统中未找到对应的图纸, 图号: " + dwgNo);
            }
            // 使用HTTP客户端下载文件
            byte[] fileData = downloadFileFromUrl(plmUrl);
            if (fileData != null && fileData.length > 0) {
                return fileData;
            } else {
                throw new RuntimeException("PLM下载的文件为空，图号: " + dwgNo);
            }

        } catch (Exception e) {
            log.error("从PLM统获取图纸文件流失败, 图号: {}", dwgNo, e);
            throw new RuntimeException("从PLM系统获取图纸文件流失败: " + e.getMessage());
        }
    }

    /**
     * 从URL下载文件
     *
     * @param url 文件URL
     * @return 文件字节数组
     */
    private byte[] downloadFileFromUrl(String url) {
        try {
            byte[] fileData = HttpUtil.downloadBytes(url);

            if (ObjectUtil.isEmpty(fileData)) {
                throw new RuntimeException("下载的文件为空");
            }
            return fileData;
        } catch (Exception e) {
            throw new RuntimeException("文件下载失败: " + e.getMessage());
        }
    }

}
