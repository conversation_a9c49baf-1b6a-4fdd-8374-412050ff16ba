package org.dromara.tianxin.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 邮件服务接口
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
public interface IEmailService {

    /**
     * 发送外发加工标签邮件
     *
     * @param files 附件文件列表
     * @param content 邮件内容
     * @param tableData 表格数据（JSON字符串）
     * @param subject 邮件主题
     */
    void sendProcessLabEmail(MultipartFile[] files, String content, String tableData, String subject);

    /**
     * 发送简单文本邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     */
    void sendSimpleEmail(String to, String subject, String content);

    /**
     * 发送HTML邮件
     *
     * @param to 收件人
     * @param subject 主题
     * @param content HTML内容
     */
    void sendHtmlEmail(String to, String subject, String content);

    /**
     * 发送带附件的邮件
     *
     * @param to 收件人
     * @param cc 抄送人列表
     * @param subject 主题
     * @param content 内容
     * @param files 附件文件列表
     */
    void sendEmailWithAttachments(String to, String cc, String subject, String content, MultipartFile[] files);

    /**
     * 发送带附件的邮件(原有方法，保持兼容性)
     *
     * @param to 收件人
     * @param subject 主题
     * @param content 内容
     * @param files 附件文件列表
     */
    void sendEmailWithAttachments(String to, String subject, String content, MultipartFile[] files);
}
