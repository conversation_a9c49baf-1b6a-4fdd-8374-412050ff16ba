package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.MachineToolInfoBo;
import org.dromara.tianxin.domain.vo.MachineToolInfoVo;
import org.dromara.tianxin.service.IMachineToolInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
 * 前端访问路由地址为:/machineToolInfo/machineToolInfo
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/machineToolInfo")
public class MachineToolInfoController extends BaseController {

    private final IMachineToolInfoService machineToolInfoService;

    /**
     * 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     */
    @SaCheckPermission("tianxin:machineToolInfo:list")
    @GetMapping("/list")
    public TableDataInfo<MachineToolInfoVo> list(MachineToolInfoBo bo, PageQuery pageQuery) {
        return machineToolInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     */
    @SaCheckPermission("tianxin:machineToolInfo:export")
    @Log(title = "机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MachineToolInfoBo bo, HttpServletResponse response) {
        List<MachineToolInfoVo> list = machineToolInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等", MachineToolInfoVo.class, response);
    }

    /**
     * 获取机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:machineToolInfo:query")
    @GetMapping("/{id}")
    public R<MachineToolInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                        @PathVariable("id") Long id) {
        return R.ok(machineToolInfoService.queryById(id));
    }

    /**
     * 新增机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     */
    @SaCheckPermission("tianxin:machineToolInfo:add")
    @Log(title = "机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MachineToolInfoBo bo) {
        return toAjax(machineToolInfoService.insertByBo(bo));
    }

    /**
     * 修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     */
    @SaCheckPermission("tianxin:machineToolInfo:edit")
    @Log(title = "机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MachineToolInfoBo bo) {
        return toAjax(machineToolInfoService.updateByBo(bo));
    }

    /**
     * 删除机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:machineToolInfo:remove")
    @Log(title = "机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(machineToolInfoService.deleteWithValidByIds(List.of(ids), true));
    }
}
