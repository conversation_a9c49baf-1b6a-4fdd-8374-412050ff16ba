package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.tianxin.domain.vo.MasterVo;
import org.dromara.tianxin.service.IActionInfoService;
import org.dromara.tianxin.service.IMasterService;
import org.dromara.tianxin.service.IOutSourcePurchaseScanService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 外发采购扫描
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/outsource-scan")
public class OutSourcePurchaseScanController {
    private final IActionInfoService actionInfoService;
    private final IMasterService masterService;
    private final IOutSourcePurchaseScanService outSourcePurchaseScanService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询动作信息列表
     */
    @SaCheckPermission("tianxin:outsource-scan:list")
    @GetMapping("/action-info")
    public R<List<ActionInfoVo>> listActionInfo() {
        return R.ok(actionInfoService.getOrganizationActionsByCurrentUser().getActionList());
    }

    /**
     * 查询主数据列表
     *
     * @param moNoList moNoList
     * @return 主数据列表
     */
    @SaCheckPermission("tianxin:outsource-scan:list")
    @GetMapping("/master")
    public R<List<MasterVo>> listMaster(@RequestParam(value = "moNoList", required = false) List<String> moNoList) {
        if (ObjectUtil.isEmpty(moNoList)) {
            return R.ok(List.of());
        }
        return R.ok(masterService.queryListByMoNo(moNoList));
    }

    /**
     * 更新扫描结果
     *
     * @param boList 扫描结果
     * @return 是否保存成功
     */
    @SaCheckPermission("tianxin:outsource-scan:list")
    @PostMapping("/scan")
    public R<Void> updateScan(@RequestBody List<ScanRecordLogBo> boList) {
        outSourcePurchaseScanService.saveActionScan(boList);
        return R.ok();
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @SaCheckPermission("tianxin:outsource-scan:list")
    @GetMapping("/user-info")
    public R<LoginUser> getUserInfoByName(String userName) {
        return R.ok(remoteUserService.getUserInfo(userName, LoginHelper.getTenantId()));
    }
}
