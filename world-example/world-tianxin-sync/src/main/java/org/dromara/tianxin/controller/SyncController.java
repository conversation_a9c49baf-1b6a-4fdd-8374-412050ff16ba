package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.tianxin.domain.request.CustomerRequest;
import org.dromara.common.tianxin.domain.request.ProductRequest;
import org.dromara.common.tianxin.domain.request.SupplierRequest;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.SyncLogBo;
import org.dromara.tianxin.domain.vo.SyncLogVo;
import org.dromara.tianxin.service.ISyncLogService;
import org.dromara.tianxin.service.ISyncService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 天心天思数据同步控制器
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/tianxin/sync")
public class SyncController extends BaseController {

    private final ISyncService syncService;
    private final ISyncLogService syncLogService;

    // ==================== 基础资料同步 ====================

    /**
     * 同步商品数据
     */
    @SaCheckPermission("tianxin:sync:product")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/product")
    public R<Object> syncProduct(@RequestBody ProductRequest request) {
        return R.ok(syncService.syncProduct(request));
    }

    /**
     * 批量同步商品数据
     */
    @SaCheckPermission("tianxin:sync:product")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/product/batch")
    public R<Object> batchSyncProducts(@RequestBody List<ProductRequest> requests) {
        return R.ok(syncService.syncProducts(requests));
    }

    /**
     * 同步客户数据
     */
    @SaCheckPermission("tianxin:sync:customer")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/customer")
    public R<Object> syncCustomer(@RequestBody CustomerRequest request) {
        return R.ok(syncService.syncCustomer(request));
    }

    /**
     * 批量同步客户数据
     */
    @SaCheckPermission("tianxin:sync:customer")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/customer/batch")
    public R<Object> batchSyncCustomers(@RequestBody List<CustomerRequest> requests) {
        return R.ok(syncService.syncCustomers(requests));
    }

    /**
     * 同步供应商数据
     */
    @SaCheckPermission("tianxin:sync:supplier")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/supplier")
    public R<Object> syncSupplier(@RequestBody SupplierRequest request) {
        return R.ok(syncService.syncSupplier(request));
    }

    /**
     * 批量同步供应商数据
     */
    @SaCheckPermission("tianxin:sync:supplier")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/supplier/batch")
    public R<Object> batchSyncSuppliers(@RequestBody List<SupplierRequest> requests) {
        return R.ok(syncService.syncSuppliers(requests));
    }

    // ==================== 业务数据同步 ====================

    /**
     * 同步库存数据
     */
    @SaCheckPermission("tianxin:sync:inventory")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/inventory")
    public R<Object> syncInventory() {
        return R.ok(syncService.syncInventory());
    }

    /**
     * 同步销售订单数据
     */
    @SaCheckPermission("tianxin:sync:sales")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/sales/orders")
    public R<Object> syncSalesOrders() {
        return R.ok(syncService.syncSalesOrders());
    }

    /**
     * 同步采购订单数据
     */
    @SaCheckPermission("tianxin:sync:purchase")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/purchase/orders")
    public R<Object> syncPurchaseOrders() {
        return R.ok(syncService.syncPurchaseOrders());
    }

    /**
     * 同步生产计划数据
     */
    @SaCheckPermission("tianxin:sync:production")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/production/plans")
    public R<Object> syncProductionPlans() {
        return R.ok(syncService.syncProductionPlans());
    }

    /**
     * 同步生产订单数据
     */
    @SaCheckPermission("tianxin:sync:production")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/production/orders")
    public R<Object> syncProductionOrders() {
        return R.ok(syncService.syncProductionOrders());
    }

    // ==================== 通用同步方法 ====================

    /**
     * 执行全量数据同步
     */
    @SaCheckPermission("tianxin:sync:full")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/full")
    public R<Object> executeFullSync() {
        return R.ok(syncService.executeFullSync());
    }

    /**
     * 执行增量数据同步
     */
    @SaCheckPermission("tianxin:sync:incremental")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/incremental")
    public R<Object> executeIncrementalSync() {
        return R.ok(syncService.executeIncrementalSync());
    }

    /**
     * 检查同步状态
     */
    @SaCheckPermission("tianxin:sync:status")
    @GetMapping("/status")
    public R<Object> checkSyncStatus() {
        return R.ok(syncService.checkSyncStatus());
    }

    /**
     * 重试失败的同步任务
     */
    @SaCheckPermission("tianxin:sync:retry")
    @Log(title = "天心天思数据同步", businessType = BusinessType.OTHER)
    @PostMapping("/retry")
    public R<Object> retryFailedSync() {
        return R.ok(syncService.retryFailedSync());
    }

    // ==================== 同步日志管理 ====================

    /**
     * 查询同步日志列表
     */
    @SaCheckPermission("tianxin:sync:log")
    @GetMapping("/log/list")
    public R<Object> listSyncLogs(SyncLogBo syncLog, PageQuery pageQuery) {
        return R.ok(syncLogService.queryPageList(syncLog, pageQuery));
    }

    /**
     * 获取同步日志详细信息
     */
    @SaCheckPermission("tianxin:sync:log")
    @GetMapping("/log/{id}")
    public R<SyncLogVo> getSyncLog(@PathVariable Long id) {
        return R.ok(syncLogService.queryById(id));
    }

    /**
     * 删除同步日志
     */
    @SaCheckPermission("tianxin:sync:log")
    @Log(title = "天心天思数据同步日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/log/{ids}")
    public R<Void> removeSyncLogs(@PathVariable Long[] ids) {
        return toAjax(syncLogService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取同步统计信息
     */
    @SaCheckPermission("tianxin:sync:statistics")
    @GetMapping("/statistics")
    public R<Object> getSyncStatistics(@RequestParam(required = false) String module,
                                       @RequestParam(required = false) String dataType) {
        return R.ok(syncLogService.getSyncStatistics(module, dataType));
    }

    // ==================== 分页查询接口 ====================

    /**
     * 分页查询同步日志列表（通过服务层）
     */
    @SaCheckPermission("tianxin:sync:log")
    @GetMapping("/log/page")
    public TableDataInfo<SyncLogVo> list(SyncLogBo bo, PageQuery pageQuery) {
        return syncService.selectPageSyncLogList(bo, pageQuery);
    }
}
