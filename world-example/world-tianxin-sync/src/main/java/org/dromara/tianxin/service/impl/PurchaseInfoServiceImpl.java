package org.dromara.tianxin.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.domain.PurchaseInfo;
import org.dromara.tianxin.domain.bo.PurchaseInfoBo;
import org.dromara.tianxin.domain.vo.PurchaseInfoVo;
import org.dromara.tianxin.mapper.PurchaseInfoMapper;
import org.dromara.tianxin.service.IPurchaseInfoService;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseInfoServiceImpl implements IPurchaseInfoService {

    private final PurchaseInfoMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param serialkey 主键
     * @return 【请填写功能名称】
     */
    @Override
    public PurchaseInfoVo queryById(Long serialkey){
        return baseMapper.selectVoById(serialkey);
    }

        /**
         * 分页查询【请填写功能名称】列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 【请填写功能名称】分页列表
         */
        @Override
        public TableDataInfo<PurchaseInfoVo> queryPageList(PurchaseInfoBo bo, PageQuery pageQuery) {
            LambdaQueryWrapper<PurchaseInfo> lqw = buildQueryWrapper(bo);
            Page<PurchaseInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }

    /**
     * 查询符合条件的【请填写功能名称】列表
     *
     * @param bo 查询条件
     * @return 【请填写功能名称】列表
     */
    @Override
    public List<PurchaseInfoVo> queryList(PurchaseInfoBo bo) {
        LambdaQueryWrapper<PurchaseInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<PurchaseInfo> buildQueryWrapper(PurchaseInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<PurchaseInfo> lqw = Wrappers.lambdaQuery();
                lqw.orderByAsc(PurchaseInfo::getSerialkey);
                    lqw.eq(StringUtils.isNotBlank(bo.getExtdockey1()), PurchaseInfo::getExtdockey1, bo.getExtdockey1());
                    lqw.eq(StringUtils.isNotBlank(bo.getState()), PurchaseInfo::getState, bo.getState());
                    lqw.eq(StringUtils.isNotBlank(bo.getPoType()), PurchaseInfo::getPoType, bo.getPoType());
                    lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), PurchaseInfo::getMoNo, bo.getMoNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getParentOrderNo()), PurchaseInfo::getParentOrderNo, bo.getParentOrderNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getZcNo()), PurchaseInfo::getZcNo, bo.getZcNo());
                    lqw.like(StringUtils.isNotBlank(bo.getZcName()), PurchaseInfo::getZcName, bo.getZcName());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorCode()), PurchaseInfo::getVendorCode, bo.getVendorCode());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorSnm()), PurchaseInfo::getVendorSnm, bo.getVendorSnm());
                    lqw.eq(StringUtils.isNotBlank(bo.getPurchaseNum()), PurchaseInfo::getPurchaseNum, bo.getPurchaseNum());
                    lqw.eq(StringUtils.isNotBlank(bo.getPurchaseItm()), PurchaseInfo::getPurchaseItm, bo.getPurchaseItm());
                    lqw.eq(bo.getPurchaseDate() != null, PurchaseInfo::getPurchaseDate, bo.getPurchaseDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getSourceOrderNumber()), PurchaseInfo::getSourceOrderNumber, bo.getSourceOrderNumber());
                    lqw.eq(StringUtils.isNotBlank(bo.getPrdNo()), PurchaseInfo::getPrdNo, bo.getPrdNo());
                    lqw.like(StringUtils.isNotBlank(bo.getPrdName()), PurchaseInfo::getPrdName, bo.getPrdName());
                    lqw.eq(StringUtils.isNotBlank(bo.getPrdDesc()), PurchaseInfo::getPrdDesc, bo.getPrdDesc());
                    lqw.eq(StringUtils.isNotBlank(bo.getPartdrawno()), PurchaseInfo::getPartdrawno, bo.getPartdrawno());
                    lqw.eq(bo.getPoQty() != null, PurchaseInfo::getPoQty, bo.getPoQty());
                    lqw.eq(bo.getJhQty() != null, PurchaseInfo::getJhQty, bo.getJhQty());
                    lqw.eq(StringUtils.isNotBlank(bo.getUt()), PurchaseInfo::getUt, bo.getUt());
                    lqw.eq(StringUtils.isNotBlank(bo.getIsProvideMaterials()), PurchaseInfo::getIsProvideMaterials, bo.getIsProvideMaterials());
                    lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), PurchaseInfo::getCustNo, bo.getCustNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getSpc()), PurchaseInfo::getSpc, bo.getSpc());
                    lqw.eq(StringUtils.isNotBlank(bo.getMarkNo()), PurchaseInfo::getMarkNo, bo.getMarkNo());
                    lqw.like(StringUtils.isNotBlank(bo.getMarkName()), PurchaseInfo::getMarkName, bo.getMarkName());
                    lqw.eq(StringUtils.isNotBlank(bo.getWarehouse()), PurchaseInfo::getWarehouse, bo.getWarehouse());
                    lqw.eq(StringUtils.isNotBlank(bo.getStorageLocation()), PurchaseInfo::getStorageLocation, bo.getStorageLocation());
                    lqw.eq(StringUtils.isNotBlank(bo.getUsr()), PurchaseInfo::getUsr, bo.getUsr());
                    lqw.like(StringUtils.isNotBlank(bo.getUsrName()), PurchaseInfo::getUsrName, bo.getUsrName());
                    lqw.eq(bo.getSysDate() != null, PurchaseInfo::getSysDate, bo.getSysDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getHost()), PurchaseInfo::getHost, bo.getHost());
                    lqw.eq(StringUtils.isNotBlank(bo.getRem()), PurchaseInfo::getRem, bo.getRem());
                    lqw.eq(StringUtils.isNotBlank(bo.getChkUsr()), PurchaseInfo::getChkUsr, bo.getChkUsr());
                    lqw.like(StringUtils.isNotBlank(bo.getChkName()), PurchaseInfo::getChkName, bo.getChkName());
                    lqw.eq(bo.getChkDate() != null, PurchaseInfo::getChkDate, bo.getChkDate());
                    lqw.eq(bo.getCommitdate() != null, PurchaseInfo::getCommitdate, bo.getCommitdate());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(PurchaseInfoBo bo) {
        PurchaseInfo add = MapstructUtils.convert(bo, PurchaseInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSerialkey(add.getSerialkey());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(PurchaseInfoBo bo) {
        PurchaseInfo update = MapstructUtils.convert(bo, PurchaseInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(PurchaseInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
