package org.dromara.tianxin.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
    import org.dromara.common.mybatis.core.page.TableDataInfo;
    import org.dromara.common.mybatis.core.page.PageQuery;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.dromara.tianxin.domain.bo.ProductScheduleInfoBo;
import org.dromara.tianxin.domain.vo.ProductScheduleInfoVo;
import org.dromara.tianxin.domain.ProductScheduleInfo;
import org.dromara.tianxin.mapper.ProductScheduleInfoMapper;
import org.dromara.tianxin.service.IProductScheduleInfoService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ProductScheduleInfoServiceImpl implements IProductScheduleInfoService {

    private final ProductScheduleInfoMapper baseMapper;

    /**
     * 查询排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param id 主键
     * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     */
    @Override
    public ProductScheduleInfoVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

        /**
         * 分页查询排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息分页列表
         */
        @Override
        public TableDataInfo<ProductScheduleInfoVo> queryPageList(ProductScheduleInfoBo bo, PageQuery pageQuery) {
            LambdaQueryWrapper<ProductScheduleInfo> lqw = buildQueryWrapper(bo);
            Page<ProductScheduleInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }

    /**
     * 查询符合条件的排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
     *
     * @param bo 查询条件
     * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
     */
    @Override
    public List<ProductScheduleInfoVo> queryList(ProductScheduleInfoBo bo) {
        LambdaQueryWrapper<ProductScheduleInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ProductScheduleInfo> buildQueryWrapper(ProductScheduleInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ProductScheduleInfo> lqw = Wrappers.lambdaQuery();
                lqw.orderByAsc(ProductScheduleInfo::getId);
                    lqw.eq(StringUtils.isNotBlank(bo.getApsNo()), ProductScheduleInfo::getApsNo, bo.getApsNo());
                    lqw.eq(bo.getItm() != null, ProductScheduleInfo::getItm, bo.getItm());
                    lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), ProductScheduleInfo::getMoNo, bo.getMoNo());
                    lqw.eq(bo.getZcItm() != null, ProductScheduleInfo::getZcItm, bo.getZcItm());
                    lqw.eq(StringUtils.isNotBlank(bo.getZcNm()), ProductScheduleInfo::getZcNm, bo.getZcNm());
                    lqw.eq(StringUtils.isNotBlank(bo.getZcNo()), ProductScheduleInfo::getZcNo, bo.getZcNo());
                    lqw.eq(bo.getZgs() != null, ProductScheduleInfo::getZgs, bo.getZgs());
                    lqw.eq(StringUtils.isNotBlank(bo.getDep()), ProductScheduleInfo::getDep, bo.getDep());
                    lqw.eq(bo.getZgsErp() != null, ProductScheduleInfo::getZgsErp, bo.getZgsErp());
                    lqw.eq(bo.getWaittime() != null, ProductScheduleInfo::getWaittime, bo.getWaittime());
                    lqw.eq(bo.getKgDd() != null, ProductScheduleInfo::getKgDd, bo.getKgDd());
                    lqw.eq(bo.getBDd() != null, ProductScheduleInfo::getBDd, bo.getBDd());
                    lqw.eq(bo.getEDd() != null, ProductScheduleInfo::getEDd, bo.getEDd());
                    lqw.eq(bo.getJts() != null, ProductScheduleInfo::getJts, bo.getJts());
                    lqw.eq(StringUtils.isNotBlank(bo.getCusNo()), ProductScheduleInfo::getCusNo, bo.getCusNo());
        return lqw;
    }

    /**
     * 新增排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param bo 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ProductScheduleInfoBo bo) {
        ProductScheduleInfo add = MapstructUtils.convert(bo, ProductScheduleInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param bo 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ProductScheduleInfoBo bo) {
        ProductScheduleInfo update = MapstructUtils.convert(bo, ProductScheduleInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ProductScheduleInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
