package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.tianxin.domain.vo.OrganizationActionVo;
import org.dromara.tianxin.domain.bo.ActionInfoBo;
import org.dromara.tianxin.service.IActionInfoService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 动作信息
 * 前端访问路由地址为:/tianxin/actionInfo
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/actionInfo")
public class ActionInfoController extends BaseController {

    private final IActionInfoService actionInfoService;

    /**
     * 查询动作信息列表
     */
    @SaCheckPermission("tianxin:actionInfo:list")
    @GetMapping("/list")
    public TableDataInfo<ActionInfoVo> list(ActionInfoBo bo, PageQuery pageQuery) {
        return actionInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出动作信息列表
     */
    @SaCheckPermission("tianxin:actionInfo:export")
    @Log(title = "动作信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ActionInfoBo bo, HttpServletResponse response) {
        List<ActionInfoVo> list = actionInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "动作信息", ActionInfoVo.class, response);
    }

    /**
     * 获取动作信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:actionInfo:query")
    @GetMapping("/{id}")
    public R<ActionInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(actionInfoService.queryById(id));
    }

    /**
     * 新增动作信息
     */
    @SaCheckPermission("tianxin:actionInfo:add")
    @Log(title = "动作信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ActionInfoBo bo) {
        return toAjax(actionInfoService.insertByBo(bo));
    }

    /**
     * 修改动作信息
     */
    @SaCheckPermission("tianxin:actionInfo:edit")
    @Log(title = "动作信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ActionInfoBo bo) {
        return toAjax(actionInfoService.updateByBo(bo));
    }

    /**
     * 删除动作信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:actionInfo:remove")
    @Log(title = "动作信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(actionInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据当前用户组织ID获取组织及其下属动作名称
     */
    @SaCheckPermission("tianxin:actionInfo:list")
    @GetMapping("/organizationActions")
    public R<OrganizationActionVo> getOrganizationActions() {
        OrganizationActionVo result = actionInfoService.getOrganizationActionsByCurrentUser();
        return R.ok(result);
    }
}
