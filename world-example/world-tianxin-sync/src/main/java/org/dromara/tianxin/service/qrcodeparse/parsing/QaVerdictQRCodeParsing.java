package org.dromara.tianxin.service.qrcodeparse.parsing;


import org.dromara.tianxin.domain.dto.MachineryQRCodeResultDTO;
import org.dromara.tianxin.service.qrcodeparse.QRCodeAbstractMethod;
import org.springframework.stereotype.Service;

@Service
public class QaVerdictQRCodeParsing  implements QRCodeAbstractMethod<String, MachineryQRCodeResultDTO> {
	@Override
	public MachineryQRCodeResultDTO parsing(String code) {
		String[] codeArr = code.split(",");
		checkDigit(codeArr[0]);
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = parsing26(codeArr[0]);
		if(codeArr.length > 1){
			machineryQRCodeResultDTO.setYyOrder(codeArr[1]);
		}
		return machineryQRCodeResultDTO;
	}
}
