package org.dromara.tianxin.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
    import org.dromara.common.mybatis.core.page.TableDataInfo;
    import org.dromara.common.mybatis.core.page.PageQuery;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.domain.ReceiveInfoHead;
import org.dromara.tianxin.domain.bo.ReceiveInfoHeadBo;
import org.dromara.tianxin.domain.vo.ReceiveInfoHeadVo;
import org.dromara.tianxin.mapper.ReceiveInfoHeadMapper;
import org.dromara.tianxin.service.IReceiveInfoHeadService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReceiveInfoHeadServiceImpl implements IReceiveInfoHeadService {

    private final ReceiveInfoHeadMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 主键
     * @return 【请填写功能名称】
     */
    @Override
    public ReceiveInfoHeadVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

        /**
         * 分页查询【请填写功能名称】列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 【请填写功能名称】分页列表
         */
        @Override
        public TableDataInfo<ReceiveInfoHeadVo> queryPageList(ReceiveInfoHeadBo bo, PageQuery pageQuery) {
            LambdaQueryWrapper<ReceiveInfoHead> lqw = buildQueryWrapper(bo);
            Page<ReceiveInfoHeadVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }

    /**
     * 查询符合条件的【请填写功能名称】列表
     *
     * @param bo 查询条件
     * @return 【请填写功能名称】列表
     */
    @Override
    public List<ReceiveInfoHeadVo> queryList(ReceiveInfoHeadBo bo) {
        LambdaQueryWrapper<ReceiveInfoHead> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ReceiveInfoHead> buildQueryWrapper(ReceiveInfoHeadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReceiveInfoHead> lqw = Wrappers.lambdaQuery();
                lqw.orderByAsc(ReceiveInfoHead::getId);
                    lqw.eq(StringUtils.isNotBlank(bo.getDjSta()), ReceiveInfoHead::getDjSta, bo.getDjSta());
                    lqw.eq(StringUtils.isNotBlank(bo.getDjNo()), ReceiveInfoHead::getDjNo, bo.getDjNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getLpnNo()), ReceiveInfoHead::getLpnNo, bo.getLpnNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorSnm()), ReceiveInfoHead::getVendorSnm, bo.getVendorSnm());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorCode()), ReceiveInfoHead::getVendorCode, bo.getVendorCode());
                    lqw.eq(bo.getDjDate() != null, ReceiveInfoHead::getDjDate, bo.getDjDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getRem()), ReceiveInfoHead::getRem, bo.getRem());
                    lqw.eq(StringUtils.isNotBlank(bo.getUsr()), ReceiveInfoHead::getUsr, bo.getUsr());
                    lqw.like(StringUtils.isNotBlank(bo.getUsrName()), ReceiveInfoHead::getUsrName, bo.getUsrName());
                    lqw.eq(bo.getSysDate() != null, ReceiveInfoHead::getSysDate, bo.getSysDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getHost()), ReceiveInfoHead::getHost, bo.getHost());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ReceiveInfoHeadBo bo) {
        ReceiveInfoHead add = MapstructUtils.convert(bo, ReceiveInfoHead.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ReceiveInfoHeadBo bo) {
        ReceiveInfoHead update = MapstructUtils.convert(bo, ReceiveInfoHead.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ReceiveInfoHead entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
