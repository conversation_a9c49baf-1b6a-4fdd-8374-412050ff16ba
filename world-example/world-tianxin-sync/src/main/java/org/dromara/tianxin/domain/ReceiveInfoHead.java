package org.dromara.tianxin.domain;

import org.apache.ibatis.annotations.Options;
import org.dromara.common.tenant.core.TenantEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 【请填写功能名称】对象 receive_info_head
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("receive_info_head")
public class ReceiveInfoHead extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 单据状态
     */
    private String djSta;

    /**
     * ASN单号，不可重复，不可为空
     */
    private String djNo;

    /**
     * 托盘号，不可重复，不可为空
     */
    private String lpnNo;

    /**
     * 供应商简称
     */
    private String vendorSnm;

    /**
     * 供应商代号
     */
    private String vendorCode;

    /**
     * 单据日期
     */
    private Date djDate;

    /**
     * 备注
     */
    private String rem;

    /**
     * 操作员工号
     */
    private String usr;

    /**
     * 姓名
     */
    private String usrName;

    /**
     * 操作日期
     */
    private Date sysDate;

    /**
     * 操作电脑名
     */
    private String host;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;


}
