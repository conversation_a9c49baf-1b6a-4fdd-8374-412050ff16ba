package org.dromara.tianxin.service;

import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.tianxin.domain.vo.OrganizationActionVo;
import org.dromara.tianxin.domain.bo.ActionInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 动作信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface IActionInfoService {

    /**
     * 查询动作信息
     *
     * @param id 主键
     * @return 动作信息
     */
    ActionInfoVo queryById(Long id);

    /**
     * 分页查询动作信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 动作信息分页列表
     */
    TableDataInfo<ActionInfoVo> queryPageList(ActionInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的动作信息列表
     *
     * @param bo 查询条件
     * @return 动作信息列表
     */
    List<ActionInfoVo> queryList(ActionInfoBo bo);

    /**
     * 新增动作信息
     *
     * @param bo 动作信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ActionInfoBo bo);

    /**
     * 修改动作信息
     *
     * @param bo 动作信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ActionInfoBo bo);

    /**
     * 校验并批量删除动作信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据当前用户组织ID获取组织及其下属动作名称
     *
     * @return 组织动作信息
     */
    OrganizationActionVo getOrganizationActionsByCurrentUser();
}
