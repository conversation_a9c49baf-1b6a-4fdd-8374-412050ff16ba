package org.dromara.tianxin.domain.bo;


import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.PurchaseInfo;

/**
 * 【请填写功能名称】业务对象 purchase_info
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = PurchaseInfo.class, reverseConvertGenerate = false)
public class PurchaseInfoBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long serialkey;

    /**
     * 外部单号
     */
    private String extdockey1;

    /**
     * 订单状态
     */
    private String state;

    /**
     * PO类型
     */
    private String poType;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 上层工单
     */
    private String parentOrderNo;

    /**
     * 工序号
     */
    private String zcNo;

    /**
     * 工序名称
     */
    private String zcName;

    /**
     * 厂商代号
     */
    private String vendorCode;

    /**
     * 简称
     */
    private String vendorSnm;

    /**
     * 采购单号
     */
    private String purchaseNum;

    /**
     * 采购单行号
     */
    private String purchaseItm;

    /**
     * PO日期
     */
    private Date purchaseDate;

    /**
     * 来源单号
     */
    private String sourceOrderNumber;

    /**
     * 品号
     */
    private String prdNo;

    /**
     * 物料名称
     */
    private String prdName;

    /**
     * 描述
     */
    private String prdDesc;

    /**
     * 图号
     */
    private String partdrawno;

    /**
     * PO数量
     */
    private Long poQty;

    /**
     * 已交数量
     */
    private Long jhQty;

    /**
     * 单位
     */
    private String ut;

    /**
     * 是否供料
     */
    private String isProvideMaterials;

    /**
     * 客户代码
     */
    private String custNo;

    /**
     * 规格型号
     */
    private String spc;

    /**
     * 品牌代号
     */
    private String markNo;

    /**
     * 品牌名称
     */
    private String markName;

    /**
     * 仓库
     */
    private String warehouse;

    /**
     * 储位
     */
    private String storageLocation;

    /**
     * 采购工号
     */
    private String usr;

    /**
     * 采购员姓名
     */
    private String usrName;

    /**
     * 操作日期
     */
    private Date sysDate;

    /**
     * 操作电脑
     */
    private String host;

    /**
     * 备注
     */
    private String rem;

    /**
     * 审批工号
     */
    private String chkUsr;

    /**
     * 审批姓名
     */
    private String chkName;

    /**
     * 审批日期
     */
    private Date chkDate;

    /**
     * 承诺交期
     */
    private Date commitdate;


}
