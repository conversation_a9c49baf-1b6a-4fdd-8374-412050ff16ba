package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.tianxin.domain.ReceiveInfoDetail;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 【请填写功能名称】视图对象 receive_info_detail
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ReceiveInfoDetail.class)
public class ReceiveInfoDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long id;

    /**
     * 单号
     */
    @ExcelProperty(value = "单号")
    private String djNo;

    /**
     * 行号
     */
    @ExcelProperty(value = "行号")
    private String djItm;

    /**
     * 单据状态
     */
    @ExcelProperty(value = "单据状态")
    private String djSta;

    /**
     * PO类型
     */
    @ExcelProperty(value = "PO类型")
    private String poType;

    /**
     * 托盘号
     */
    @ExcelProperty(value = "托盘号")
    private String lpnNo;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 工序号
     */
    @ExcelProperty(value = "工序号")
    private String zcNo;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String zcName;

    /**
     * 单据日期
     */
    @ExcelProperty(value = "单据日期")
    private Date djDate;

    /**
     * 厂商代号
     */
    @ExcelProperty(value = "厂商代号")
    private String vendorCode;

    /**
     * 简称
     */
    @ExcelProperty(value = "简称")
    private String vendorSnm;

    /**
     * 采购单号
     */
    @ExcelProperty(value = "采购单号")
    private String purchaseNum;

    /**
     * 采购单行号
     */
    @ExcelProperty(value = "采购单行号")
    private String purchaseItm;

    /**
     * 品号
     */
    @ExcelProperty(value = "品号")
    private String prdNo;

    /**
     * 物料名称
     */
    @ExcelProperty(value = "物料名称")
    private String prdName;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String prdDesc;

    /**
     * PO数量
     */
    @ExcelProperty(value = "PO数量")
    private Long poQty;

    /**
     * 收货数量
     */
    @ExcelProperty(value = "收货数量")
    private Long actualQty;

    /**
     * 退货数量
     */
    @ExcelProperty(value = "退货数量")
    private Long returnQty;

    /**
     * 外发数量
     */
    @ExcelProperty(value = "外发数量")
    private Long outerQty;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String ut;

    /**
     * 是否供料
     */
    @ExcelProperty(value = "是否供料")
    private String isProvideMaterials;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String custNo;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String spc;

    /**
     * 品牌
     */
    @ExcelProperty(value = "品牌")
    private String mark;

    /**
     * 储位
     */
    @ExcelProperty(value = "储位")
    private String storeCw;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号")
    private String usr;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String usrName;

    /**
     * SYS_DATE
     */
    @ExcelProperty(value = "SYS_DATE")
    private Date sysDate;

    /**
     * host
     */
    @ExcelProperty(value = "host")
    private String host;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String rem;


}
