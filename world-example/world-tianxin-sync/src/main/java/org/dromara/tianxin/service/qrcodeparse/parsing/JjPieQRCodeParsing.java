package org.dromara.tianxin.service.qrcodeparse.parsing;



import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.tianxin.domain.dto.MachineryQRCodeResultDTO;
import org.dromara.tianxin.service.qrcodeparse.QRCodeAbstractMethod;
import org.springframework.stereotype.Service;

/**
 * 机加件条码解析逻辑
 */
@Service("jjPieQRCodeParsing")
public class JjPieQRCodeParsing implements QRCodeAbstractMethod<String, MachineryQRCodeResultDTO> {
	@Override
	public MachineryQRCodeResultDTO parsing(String code) {
		if(StringUtils.isBlank(code)){
			throw new ServiceException("条码输入空");
		}
		String[] codeArr = code.split(",");
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = null;
		int length = checkDigit(codeArr[0]);
		if(length == 16){
			machineryQRCodeResultDTO = parsing16(codeArr[0]);
		}else if(length == 12){
			machineryQRCodeResultDTO = parsing12(codeArr[0]);
		}else{
			machineryQRCodeResultDTO = parsing26(codeArr[0]);
		}
		if(codeArr.length > 1){
			machineryQRCodeResultDTO.setYyOrder(codeArr[1]);
		}
		return machineryQRCodeResultDTO;
	}
	public int checkDigit(String code){
		int length = code.length();
		if(length != 16 && length != 12){
			throw new ServiceException("条码错误,条码位数：" + length);
		}
		return length;
	}
}
