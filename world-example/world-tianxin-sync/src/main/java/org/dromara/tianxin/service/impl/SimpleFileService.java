package org.dromara.tianxin.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.ServletUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.util.Base64;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 简单文件服务
 * 将SMB文件下载到临时目录，通过HTTP提供下载
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SimpleFileService {

    @Value("${server.port:8080}")
    private String serverPort;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    /**
     * 临时文件目录
     */
    private static final String TEMP_DIR = System.getProperty("java.io.tmpdir") + File.separator + "tianxin-drawings";

    /**
     * 文件访问URL前缀
     */
    private static final String FILE_URL_PREFIX = "tianxin/master/download/";

    /**
     * 文件过期时间（小时）
     */
    private static final int FILE_EXPIRE_HOURS = 1;

    /**
     * 定时清理任务
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();

    /**
     * 将Base64数据保存为临时文件并返回下载URL
     *
     * @param base64Data Base64编码的数据
     * @param fileName   文件名
     * @return 文件下载URL
     */
    public String saveBase64ToTempFile(String base64Data, String fileName) {
        try {
            // 确保临时目录存在
            createTempDirectoryIfNotExists();

            // 生成唯一文件ID
            String fileId = generateUniqueFileId(fileName);
            String tempFilePath = TEMP_DIR + File.separator + fileId + ".pdf";

            // 解码Base64数据并保存到文件
            byte[] fileData = Base64.getDecoder().decode(base64Data);
            try (FileOutputStream fos = new FileOutputStream(tempFilePath)) {
                fos.write(fileData);
            }

            // 生成访问URL
            String fileUrl = generateFileUrl(fileId);

            log.info("Base64数据已保存到临时文件: path={}, 文件大小={} bytes, 访问URL={}",
                    tempFilePath, fileData.length, fileUrl);

            // 安排文件清理任务
            scheduleFileCleanup(fileId, FILE_EXPIRE_HOURS);

            return fileUrl;

        } catch (Exception e) {
            log.error("保存Base64数据到临时文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存文件失败: " + e.getMessage());
        }
    }

    /**
     * 直接保存二进制数据到临时文件（性能优化版本）
     *
     * @param fileData 二进制数据
     * @param fileName 文件名
     * @return 文件下载URL
     */
    public String saveBinaryToTempFile(byte[] fileData, String fileName) {
        try {
            // 确保临时目录存在
            createTempDirectoryIfNotExists();
            // 生成唯一文件ID
            String fileId = generateUniqueFileId(fileName);
            String tempFilePath = TEMP_DIR + File.separator + fileId + ".pdf";
            // 直接保存二进制数据到文件
            try (FileOutputStream fos = new FileOutputStream(tempFilePath)) {
                fos.write(fileData);
            }
            // 生成访问URL
            String fileUrl = generateFileUrl(fileId);
            // 清理临时文件任务
            scheduleFileCleanup(fileId, FILE_EXPIRE_HOURS);
            return fileUrl;

        } catch (Exception e) {
            log.error("保存二进制数据到临时文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取临时文件
     *
     * @param fileId 文件ID
     * @return 文件数据
     */
    public byte[] getTempFile(String fileId) {
        try {
            String tempFilePath = TEMP_DIR + File.separator + fileId + ".pdf";
            File file = new File(tempFilePath);
            if (!file.exists()) {
                return null;
            }
            // 读取文件数据
            byte[] fileData = Files.readAllBytes(file.toPath());
            // 获取后立即删除文件（一次性访问）
            Files.deleteIfExists(file.toPath());
            return fileData;
        } catch (Exception e) {
            log.error("获取临时文件失败: {}", e.getMessage(), e);
            throw new RuntimeException("获取文件失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     *
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    public boolean deleteFile(String fileId) {
        try {
            String tempFilePath = TEMP_DIR + File.separator + fileId + ".pdf";
            boolean deleted = Files.deleteIfExists(Paths.get(tempFilePath));
            return deleted;
        } catch (Exception e) {
            log.error("删除临时文件失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 创建临时目录（如果不存在）
     */
    private void createTempDirectoryIfNotExists() {
        try {
            Path tempDir = Paths.get(TEMP_DIR);
            if (!Files.exists(tempDir)) {
                Files.createDirectories(tempDir);
                log.info("创建临时目录: {}", TEMP_DIR);
            }
        } catch (Exception e) {
            log.error("创建临时目录失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建临时目录失败: " + e.getMessage());
        }
    }

    /**
     * 生成唯一文件ID
     *
     * @param originalFileName 原始文件名
     * @return 文件ID
     */
    private String generateUniqueFileId(String originalFileName) {
        String uuid = UUID.randomUUID().toString().substring(0, 8);
        return String.format("%s_%s", originalFileName, uuid);
    }

    /**
     * 生成文件访问URL
     *
     * @param fileId 文件ID
     * @return 文件访问URL
     */
    private String generateFileUrl(String fileId) {
        try {
            // 尝试获取当前请求的服务器地址
            HttpServletRequest request = ServletUtils.getRequest();
            if (request != null) {
                String fullUrl = contextPath + "/" + FILE_URL_PREFIX + fileId;
                log.info("生成文件访问URL: {}", fullUrl);
                return fullUrl;
            }
        } catch (Exception e) {
            log.warn("获取请求信息失败，使用默认配置: {}", e.getMessage());
        }
        // 如果无法获取请求信息，使用默认配置
        String fullUrl = "/" + FILE_URL_PREFIX + fileId;
        log.info("使用默认配置生成文件访问URL: {}", fullUrl);
        return fullUrl;
    }

    /**
     * 安排文件清理任务
     *
     * @param fileId 文件ID
     * @param hours  过期时间（小时）
     */
    private void scheduleFileCleanup(String fileId, int hours) {
        scheduler.schedule(() -> {
            try {
                deleteFile(fileId);
                log.info("定时清理临时文件: {}", fileId);
            } catch (Exception e) {
                log.warn("定时清理临时文件失败: {}", e.getMessage());
            }
        }, hours, TimeUnit.HOURS);
    }

    /**
     * 清理所有过期文件
     */
    public void cleanupExpiredFiles() {
        try {
            Path tempDir = Paths.get(TEMP_DIR);
            if (!Files.exists(tempDir)) {
                return;
            }

            Files.list(tempDir)
                .filter(Files::isRegularFile)
                .filter(path -> {
                    try {
                        // 检查文件是否超过1小时
                        long fileAge = System.currentTimeMillis() - Files.getLastModifiedTime(path).toMillis();
                        return fileAge > Duration.ofHours(FILE_EXPIRE_HOURS).toMillis();
                    } catch (Exception e) {
                        return true; // 如果无法获取文件时间，则删除
                    }
                })
                .forEach(path -> {
                    try {
                        Files.deleteIfExists(path);
                        log.info("清理过期临时文件: {}", path);
                    } catch (Exception e) {
                        log.warn("清理过期临时文件失败: {}", e.getMessage());
                    }
                });

        } catch (Exception e) {
            log.error("清理过期文件失败: {}", e.getMessage(), e);
        }
    }
}
