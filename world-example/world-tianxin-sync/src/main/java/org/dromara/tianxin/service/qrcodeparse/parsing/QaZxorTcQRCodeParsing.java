package org.dromara.tianxin.service.qrcodeparse.parsing;


import org.dromara.tianxin.domain.dto.MachineryQRCodeResultDTO;
import org.dromara.tianxin.service.qrcodeparse.QRCodeAbstractMethod;
import org.springframework.stereotype.Service;

@Service
public class QaZxorTcQRCodeParsing   implements QRCodeAbstractMethod<String, MachineryQRCodeResultDTO> {
	@Override
	public MachineryQRCodeResultDTO parsing(String code) {
		String[] codeArr = code.split(",");
		Integer length = checkDigit(codeArr[0], 12, 16);
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = new MachineryQRCodeResultDTO();
		if(length == 12){
			machineryQRCodeResultDTO.setLength(code.length());
			machineryQRCodeResultDTO.setMokey(codeArr[0].substring(0, 12));
		}else if(length == 16){
			machineryQRCodeResultDTO = parsing16(codeArr[0]);
		}
		if(codeArr.length > 1){
			machineryQRCodeResultDTO.setYyOrder(codeArr[1]);
		}
		return machineryQRCodeResultDTO;
	}
}
