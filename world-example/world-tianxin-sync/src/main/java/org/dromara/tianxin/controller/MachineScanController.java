package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.MachineScanBo;
import org.dromara.tianxin.domain.vo.MachineToolInfoVo;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;
import org.dromara.tianxin.service.IMachineScanService;
import org.dromara.tianxin.service.IMachineToolInfoService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 机床扫描查询
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/machinescan")
public class MachineScanController extends BaseController {
    private final IMachineScanService machineScanService;
    private final IMachineToolInfoService machineToolInfoService;

    /**
     * 查询机床扫描查询列表
     */
    @SaCheckPermission("tianxin:machinescan:list")
    @GetMapping("/list")
    public TableDataInfo<ScanRecordLogVo> list(MachineScanBo bo, PageQuery pageQuery) {
        return machineScanService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询所有机床列表
     */
    @SaCheckPermission("tianxin:machinescan:list")
    @GetMapping("/list_machine")
    public R<List<String>> listAllMachines() {
        return R.ok(machineToolInfoService.queryAll().stream().map(
            MachineToolInfoVo::getMachineTool
        ).sorted().toList());
    }

    /**
     * 导出机床扫描查询列表
     */
    @SaCheckPermission("tianxin:machinescan:export")
    @Log(title = "导出机床扫描查询列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MachineScanBo bo, HttpServletResponse response) {
        var list = machineScanService.queryPageList(
            bo, new PageQuery(1, Integer.MAX_VALUE)
        ).getRows();
        ExcelUtil.exportExcel(list, "动作信息", ScanRecordLogVo.class, response);
    }
}
