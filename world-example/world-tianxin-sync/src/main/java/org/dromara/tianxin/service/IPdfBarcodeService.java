package org.dromara.tianxin.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * PDF条形码服务接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IPdfBarcodeService {

    /**
     * 批量处理PDF文件，添加条形码
     *
     * @param files PDF文件数组
     * @param barcodeHorizontal 条形码水平位置（left/center/right）
     * @param barcodeX 条形码距离边缘的距离（像素）
     * @param barcodeY 条形码距离上边缘的距离（像素）
     * @param barcodeWidth 条形码宽度（像素）
     * @param barcodeHeight 条形码高度（像素）
     * @return 处理结果，包含ZIP包的字节数组和文件名
     * @throws Exception 处理异常
     */
    Map<String, Object> processPdfFiles(MultipartFile[] files, String barcodeHorizontal, int barcodeX, int barcodeY, int barcodeWidth, int barcodeHeight) throws Exception;

    /**
     * 验证上传的文件
     *
     * @param files 上传的文件数组
     * @return 验证结果
     */
    Map<String, Object> validateFiles(MultipartFile[] files);

    /**
     * 获取处理状态
     *
     * @param taskId 任务ID
     * @return 处理状态信息
     */
    Map<String, Object> getProcessStatus(String taskId);

    /**
     * 清理临时文件
     *
     * @param taskId 任务ID
     * @return 是否清理成功
     */
    boolean cleanupTempFiles(String taskId);
}
