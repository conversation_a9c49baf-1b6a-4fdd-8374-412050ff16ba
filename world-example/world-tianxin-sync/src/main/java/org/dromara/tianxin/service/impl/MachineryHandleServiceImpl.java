package org.dromara.tianxin.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.domain.bo.ReceiveInfoDetailBo;
import org.dromara.tianxin.domain.bo.ReceiveInfoHeadBo;
import org.dromara.tianxin.domain.dto.ReceiveInfoDTO;
import org.dromara.tianxin.domain.vo.ReceiveInfoDetailVo;
import org.dromara.tianxin.service.IMachineryHandleService;
import org.dromara.tianxin.service.IReceiveInfoDetailService;
import org.dromara.tianxin.service.IReceiveInfoHeadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class MachineryHandleServiceImpl implements IMachineryHandleService {

    private final IReceiveInfoHeadService receiveInfoHeadService;

    private final IReceiveInfoDetailService receiveInfoDetailService;

    @Override
    public void checkBeforeReceive(String mo, String zcNo, Long qty) {
        ReceiveInfoDetailBo  bo = new ReceiveInfoDetailBo();
        bo.setDjSta("9");
        if (mo != null && !mo.isEmpty()) {
            bo.setMoNo(mo);
        }
        if (zcNo != null && !zcNo.isEmpty()) {
            bo.setZcNo(zcNo);
        }
        List<ReceiveInfoDetailVo> receiveInfoDetailVos = receiveInfoDetailService.queryList(bo);

        if (receiveInfoDetailVos != null && !receiveInfoDetailVos.isEmpty()) {
            //receiveInfoDetailVos汇总actualQty数量加上qty，与poQty对比，如果大于poQty，则提示多收数量
            Long actualQtySum = receiveInfoDetailVos.stream().mapToLong(ReceiveInfoDetailVo::getActualQty).sum();
            Long poQty = receiveInfoDetailVos.get(0).getPoQty();
            //qty + actualQtySum > poQty 不能超收并给出超收数量
            if (actualQtySum.equals(poQty)) {
                throw new RuntimeException(mo + " 已收完货，请勿超收");
            }
            if (qty + actualQtySum > poQty) {
                throw new RuntimeException(mo + " 请勿超收，已收数量："+ actualQtySum +" 超收数量："+ (qty + actualQtySum - poQty) );
            }
        }
    }

}
