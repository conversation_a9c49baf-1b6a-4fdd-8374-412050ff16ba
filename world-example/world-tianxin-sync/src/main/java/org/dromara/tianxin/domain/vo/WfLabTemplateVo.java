package org.dromara.tianxin.domain.vo;

import lombok.Data;
import java.io.Serializable;

/**
 * 外发加工标签模板数据对象
 * 用于填充Excel模板中的占位符
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Data
public class WfLabTemplateVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 标题A
     */
    private String aa;

    /**
     * 标题B
     */
    private String bb;

    /**
     * 标题C
     */
    private String cc;

    /**
     * 标题D
     */
    private String dd;

    /**
     * MO号
     */
    private String mo;

    /**
     * 标题F
     */
    private String ff;

    /**
     * 标题G
     */
    private String gg;

    /**
     * 标题H
     */
    private String hh;

    /**
     * 图号
     */
    private String th;

    /**
     * 供应商
     */
    private String gys;

    /**
     * 品号
     */
    private String ph;

    /**
     * 订单数
     */
    private String dds;

    /**
     * 交期
     */
    private String jq;

    /**
     * 送货数
     */
    private String shu;

    /**
     * 外发日期
     */
    private String wfrq;

    /**
     * 指定材料
     */
    private String zdcl;

    /**
     * 收货地址
     */
    private String shdz;

    /**
     * 是否供料
     */
    private String sfgl;

    /**
     * 电镀标准
     */
    private String ddbz;

    /**
     * 刻字要求
     */
    private String kzyq;
}