package org.dromara.tianxin.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.MachineToolInfoBo;
import org.dromara.tianxin.domain.vo.MachineToolInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等Service接口
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface IMachineToolInfoService {

    /**
     * 查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param id 主键
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     */
    MachineToolInfoVo queryById(Long id);

    /**
     * 分页查询机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等分页列表
     */
    TableDataInfo<MachineToolInfoVo> queryPageList(MachineToolInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     *
     * @param bo 查询条件
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     */
    List<MachineToolInfoVo> queryList(MachineToolInfoBo bo);

    /**
     * 查询所有机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     *
     * @return 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等列表
     */
    List<MachineToolInfoVo> queryAll();

    /**
     * 新增机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param bo 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     * @return 是否新增成功
     */
    Boolean insertByBo(MachineToolInfoBo bo);

    /**
     * 修改机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     *
     * @param bo 机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等
     * @return 是否修改成功
     */
    Boolean updateByBo(MachineToolInfoBo bo);

    /**
     * 校验并批量删除机台信息 - 存储机台信息的详细信息，包括机床、资源、使用状况等信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
