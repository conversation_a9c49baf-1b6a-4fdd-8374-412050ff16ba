package org.dromara.tianxin.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.tianxin.domain.PurchaseInfo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 【请填写功能名称】视图对象 purchase_info
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = PurchaseInfo.class)
public class PurchaseInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    private Long serialkey;

    /**
     * 外部单号
     */
    @ExcelProperty(value = "外部单号")
    private String extdockey1;

    /**
     * 订单状态
     */
    @ExcelProperty(value = "订单状态")
    private String state;

    /**
     * PO类型
     */
    @ExcelProperty(value = "PO类型")
    private String poType;

    /**
     * MO号
     */
    @ExcelProperty(value = "MO号")
    private String moNo;

    /**
     * 上层工单
     */
    @ExcelProperty(value = "上层工单")
    private String parentOrderNo;

    /**
     * 工序号
     */
    @ExcelProperty(value = "工序号")
    private String zcNo;

    /**
     * 工序名称
     */
    @ExcelProperty(value = "工序名称")
    private String zcName;

    /**
     * 厂商代号
     */
    @ExcelProperty(value = "厂商代号")
    private String vendorCode;

    /**
     * 简称
     */
    @ExcelProperty(value = "简称")
    private String vendorSnm;

    /**
     * 采购单号
     */
    @ExcelProperty(value = "采购单号")
    private String purchaseNum;

    /**
     * 采购单行号
     */
    @ExcelProperty(value = "采购单行号")
    private String purchaseItm;

    /**
     * PO日期
     */
    @ExcelProperty(value = "PO日期")
    private Date purchaseDate;

    /**
     * 来源单号
     */
    @ExcelProperty(value = "来源单号")
    private String sourceOrderNumber;

    /**
     * 品号
     */
    @ExcelProperty(value = "品号")
    private String prdNo;

    /**
     * 物料名称
     */
    @ExcelProperty(value = "物料名称")
    private String prdName;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String prdDesc;

    /**
     * 图号
     */
    @ExcelProperty(value = "图号")
    private String partdrawno;

    /**
     * PO数量
     */
    @ExcelProperty(value = "PO数量")
    private Long poQty;

    /**
     * 已交数量
     */
    @ExcelProperty(value = "已交数量")
    private Long jhQty;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位")
    private String ut;

    /**
     * 是否供料
     */
    @ExcelProperty(value = "是否供料")
    private String isProvideMaterials;

    /**
     * 客户代码
     */
    @ExcelProperty(value = "客户代码")
    private String custNo;

    /**
     * 规格型号
     */
    @ExcelProperty(value = "规格型号")
    private String spc;

    /**
     * 品牌代号
     */
    @ExcelProperty(value = "品牌代号")
    private String markNo;

    /**
     * 品牌名称
     */
    @ExcelProperty(value = "品牌名称")
    private String markName;

    /**
     * 仓库
     */
    @ExcelProperty(value = "仓库")
    private String warehouse;

    /**
     * 储位
     */
    @ExcelProperty(value = "储位")
    private String storageLocation;

    /**
     * 采购工号
     */
    @ExcelProperty(value = "采购工号")
    private String usr;

    /**
     * 采购员姓名
     */
    @ExcelProperty(value = "采购员姓名")
    private String usrName;

    /**
     * 操作日期
     */
    @ExcelProperty(value = "操作日期")
    private Date sysDate;

    /**
     * 操作电脑
     */
    @ExcelProperty(value = "操作电脑")
    private String host;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String rem;

    /**
     * 审批工号
     */
    @ExcelProperty(value = "审批工号")
    private String chkUsr;

    /**
     * 审批姓名
     */
    @ExcelProperty(value = "审批姓名")
    private String chkName;

    /**
     * 审批日期
     */
    @ExcelProperty(value = "审批日期")
    private Date chkDate;

    /**
     * 承诺交期
     */
    @ExcelProperty(value = "承诺交期")
    private Date commitdate;


}
