package org.dromara.tianxin.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.service.IMasterService;
import org.dromara.tianxin.service.IRawMaterialScanService;
import org.dromara.tianxin.service.IScanRecordLogService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class IRawMaterialScanServiceImpl implements IRawMaterialScanService {
    private final IMasterService masterService;
    private final IScanRecordLogService scanRecordLogService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveScan(List<ScanRecordLogBo> scanList) {
        var actionScans = scanList.stream().map(item -> {
            var vo = new MasterBo();
            vo.setMoNo(item.getMoNo());
            vo.setSta(item.getProcessNo() + item.getProcessName() + item.getAction());
            vo.setAction(item.getAction());
            vo.setLastProcessTime(new Date());
            return vo;
        }).toList();
        // 更新master表
        actionScans.forEach(item -> {
            var status = masterService.updateByMoNo(item);
            if (!status) {
                throw new RuntimeException("更新master表失败");
            }
        });
        scanList.forEach(item -> {
            item.setId(null);
            item.setStartTime(new Date());
            item.setEndTime(new Date());
        });
        // todo: 报工天心系统接口调用
        // 插入扫描记录表
        var status = scanRecordLogService.insertBatchByBo(scanList);
        if (status != scanList.size()) {
            throw new RuntimeException("插入扫描记录表失败");
        }
    }
}
