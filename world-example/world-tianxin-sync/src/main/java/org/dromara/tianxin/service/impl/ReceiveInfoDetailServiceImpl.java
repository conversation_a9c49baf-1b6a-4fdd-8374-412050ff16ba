package org.dromara.tianxin.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
    import org.dromara.common.mybatis.core.page.TableDataInfo;
    import org.dromara.common.mybatis.core.page.PageQuery;
    import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.tianxin.domain.ReceiveInfoDetail;
import org.dromara.tianxin.domain.bo.ReceiveInfoDetailBo;
import org.dromara.tianxin.domain.vo.ReceiveInfoDetailVo;
import org.dromara.tianxin.mapper.ReceiveInfoDetailMapper;
import org.dromara.tianxin.service.IReceiveInfoDetailService;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReceiveInfoDetailServiceImpl implements IReceiveInfoDetailService {

    private final ReceiveInfoDetailMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 主键
     * @return 【请填写功能名称】
     */
    @Override
    public ReceiveInfoDetailVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

        /**
         * 分页查询【请填写功能名称】列表
         *
         * @param bo        查询条件
         * @param pageQuery 分页参数
         * @return 【请填写功能名称】分页列表
         */
        @Override
        public TableDataInfo<ReceiveInfoDetailVo> queryPageList(ReceiveInfoDetailBo bo, PageQuery pageQuery) {
            LambdaQueryWrapper<ReceiveInfoDetail> lqw = buildQueryWrapper(bo);
            Page<ReceiveInfoDetailVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
            return TableDataInfo.build(result);
        }

    /**
     * 查询符合条件的【请填写功能名称】列表
     *
     * @param bo 查询条件
     * @return 【请填写功能名称】列表
     */
    @Override
    public List<ReceiveInfoDetailVo> queryList(ReceiveInfoDetailBo bo) {
        LambdaQueryWrapper<ReceiveInfoDetail> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ReceiveInfoDetail> buildQueryWrapper(ReceiveInfoDetailBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ReceiveInfoDetail> lqw = Wrappers.lambdaQuery();
                lqw.orderByAsc(ReceiveInfoDetail::getId);
                    lqw.eq(StringUtils.isNotBlank(bo.getDjNo()), ReceiveInfoDetail::getDjNo, bo.getDjNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getDjItm()), ReceiveInfoDetail::getDjItm, bo.getDjItm());
                    lqw.eq(StringUtils.isNotBlank(bo.getDjSta()), ReceiveInfoDetail::getDjSta, bo.getDjSta());
                    lqw.eq(StringUtils.isNotBlank(bo.getPoType()), ReceiveInfoDetail::getPoType, bo.getPoType());
                    lqw.eq(StringUtils.isNotBlank(bo.getLpnNo()), ReceiveInfoDetail::getLpnNo, bo.getLpnNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getMoNo()), ReceiveInfoDetail::getMoNo, bo.getMoNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getZcNo()), ReceiveInfoDetail::getZcNo, bo.getZcNo());
                    lqw.like(StringUtils.isNotBlank(bo.getZcName()), ReceiveInfoDetail::getZcName, bo.getZcName());
                    lqw.eq(bo.getDjDate() != null, ReceiveInfoDetail::getDjDate, bo.getDjDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorCode()), ReceiveInfoDetail::getVendorCode, bo.getVendorCode());
                    lqw.eq(StringUtils.isNotBlank(bo.getVendorSnm()), ReceiveInfoDetail::getVendorSnm, bo.getVendorSnm());
                    lqw.eq(StringUtils.isNotBlank(bo.getPurchaseNum()), ReceiveInfoDetail::getPurchaseNum, bo.getPurchaseNum());
                    lqw.eq(StringUtils.isNotBlank(bo.getPurchaseItm()), ReceiveInfoDetail::getPurchaseItm, bo.getPurchaseItm());
                    lqw.eq(StringUtils.isNotBlank(bo.getPrdNo()), ReceiveInfoDetail::getPrdNo, bo.getPrdNo());
                    lqw.like(StringUtils.isNotBlank(bo.getPrdName()), ReceiveInfoDetail::getPrdName, bo.getPrdName());
                    lqw.eq(StringUtils.isNotBlank(bo.getPrdDesc()), ReceiveInfoDetail::getPrdDesc, bo.getPrdDesc());
                    lqw.eq(bo.getPoQty() != null, ReceiveInfoDetail::getPoQty, bo.getPoQty());
                    lqw.eq(bo.getActualQty() != null, ReceiveInfoDetail::getActualQty, bo.getActualQty());
                    lqw.eq(bo.getReturnQty() != null, ReceiveInfoDetail::getReturnQty, bo.getReturnQty());
                    lqw.eq(bo.getOuterQty() != null, ReceiveInfoDetail::getOuterQty, bo.getOuterQty());
                    lqw.eq(StringUtils.isNotBlank(bo.getUt()), ReceiveInfoDetail::getUt, bo.getUt());
                    lqw.eq(StringUtils.isNotBlank(bo.getIsProvideMaterials()), ReceiveInfoDetail::getIsProvideMaterials, bo.getIsProvideMaterials());
                    lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), ReceiveInfoDetail::getCustNo, bo.getCustNo());
                    lqw.eq(StringUtils.isNotBlank(bo.getSpc()), ReceiveInfoDetail::getSpc, bo.getSpc());
                    lqw.eq(StringUtils.isNotBlank(bo.getMark()), ReceiveInfoDetail::getMark, bo.getMark());
                    lqw.eq(StringUtils.isNotBlank(bo.getStoreCw()), ReceiveInfoDetail::getStoreCw, bo.getStoreCw());
                    lqw.eq(StringUtils.isNotBlank(bo.getUsr()), ReceiveInfoDetail::getUsr, bo.getUsr());
                    lqw.like(StringUtils.isNotBlank(bo.getUsrName()), ReceiveInfoDetail::getUsrName, bo.getUsrName());
                    lqw.eq(bo.getSysDate() != null, ReceiveInfoDetail::getSysDate, bo.getSysDate());
                    lqw.eq(StringUtils.isNotBlank(bo.getHost()), ReceiveInfoDetail::getHost, bo.getHost());
                    lqw.eq(StringUtils.isNotBlank(bo.getRem()), ReceiveInfoDetail::getRem, bo.getRem());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ReceiveInfoDetailBo bo) {
        ReceiveInfoDetail add = MapstructUtils.convert(bo, ReceiveInfoDetail.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param bo 【请填写功能名称】
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ReceiveInfoDetailBo bo) {
        ReceiveInfoDetail update = MapstructUtils.convert(bo, ReceiveInfoDetail.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ReceiveInfoDetail entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除【请填写功能名称】信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
