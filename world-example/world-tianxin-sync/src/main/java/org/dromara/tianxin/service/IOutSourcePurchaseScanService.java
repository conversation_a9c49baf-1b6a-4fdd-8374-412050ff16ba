package org.dromara.tianxin.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.bo.OutSourcePurchaseScanBo;
import org.dromara.tianxin.domain.vo.MasterVo;

import java.util.List;

/**
 * 外发扫描服务接口
 */
public interface IOutSourcePurchaseScanService {
    TableDataInfo<MasterVo> queryPageList(OutSourcePurchaseScanBo query, PageQuery pageQuery);

    /**
     * 保存动作扫描
     *
     * @param masterVos 动作扫描数据
     */
    void saveActionScan(List<MasterBo> masterBos);
}
