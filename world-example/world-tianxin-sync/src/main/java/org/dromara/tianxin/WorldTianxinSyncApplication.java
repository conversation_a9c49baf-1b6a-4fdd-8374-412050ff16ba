package org.dromara.tianxin;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.context.annotation.ComponentScan;

/**
 * 越南天心跟单服务启动类
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
@EnableDubbo
@SpringBootApplication
@MapperScan("org.dromara.tianxin.mapper")
public class WorldTianxinSyncApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(WorldTianxinSyncApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  越南天心跟单服务启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
