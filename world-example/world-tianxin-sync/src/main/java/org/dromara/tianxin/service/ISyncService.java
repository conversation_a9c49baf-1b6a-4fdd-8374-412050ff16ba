package org.dromara.tianxin.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.tianxin.domain.bo.SyncLogBo;
import org.dromara.tianxin.domain.vo.SyncLogVo;
import org.dromara.common.tianxin.domain.request.ProductRequest;
import org.dromara.common.tianxin.domain.request.CustomerRequest;
import org.dromara.common.tianxin.domain.request.SupplierRequest;

import java.util.List;

/**
 * 天心天思数据同步服务接口
 *
 * <AUTHOR>
 * @since 2025-01-27
 */
public interface ISyncService {

    // ==================== 基础资料同步 ====================

    /**
     * 同步商品数据
     *
     * @param requests 商品请求对象列表
     * @return 同步结果
     */
    Object syncProducts(List<ProductRequest> requests);

    /**
     * 同步单个商品数据
     *
     * @param request 商品请求对象
     * @return 同步结果
     */
    Object syncProduct(ProductRequest request);

    /**
     * 同步客户数据
     *
     * @param requests 客户请求对象列表
     * @return 同步结果
     */
    Object syncCustomers(List<CustomerRequest> requests);

    /**
     * 同步单个客户数据
     *
     * @param request 客户请求对象
     * @return 同步结果
     */
    Object syncCustomer(CustomerRequest request);

    /**
     * 同步供应商数据
     *
     * @param requests 供应商请求对象列表
     * @return 同步结果
     */
    Object syncSuppliers(List<SupplierRequest> requests);

    /**
     * 同步单个供应商数据
     *
     * @param request 供应商请求对象
     * @return 同步结果
     */
    Object syncSupplier(SupplierRequest request);

    // ==================== 业务数据同步 ====================

    /**
     * 同步库存数据
     *
     * @return 同步结果
     */
    Object syncInventory();

    /**
     * 同步销售订单数据
     *
     * @return 同步结果
     */
    Object syncSalesOrders();

    /**
     * 同步采购订单数据
     *
     * @return 同步结果
     */
    Object syncPurchaseOrders();

    /**
     * 同步生产计划数据
     *
     * @return 同步结果
     */
    Object syncProductionPlans();

    /**
     * 同步生产订单数据
     *
     * @return 同步结果
     */
    Object syncProductionOrders();

    // ==================== 通用同步方法 ====================

    /**
     * 执行全量数据同步
     *
     * @return 同步结果
     */
    Object executeFullSync();

    /**
     * 执行增量数据同步
     *
     * @return 同步结果
     */
    Object executeIncrementalSync();

    /**
     * 检查同步状态
     *
     * @return 同步状态
     */
    Object checkSyncStatus();

    /**
     * 重试失败的同步任务
     *
     * @return 重试结果
     */
    Object retryFailedSync();

    // ==================== 分页查询方法 ====================

    /**
     * 分页查询同步日志列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<SyncLogVo> selectPageSyncLogList(SyncLogBo bo, PageQuery pageQuery);
}
