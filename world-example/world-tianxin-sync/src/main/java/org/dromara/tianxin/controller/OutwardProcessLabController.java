package org.dromara.tianxin.controller;

import java.io.ByteArrayOutputStream;
import java.util.List;

import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.service.IEmailService;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等 前端访问路由地址为:/tianxin/processLab
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/processLab")
public class OutwardProcessLabController extends BaseController {

    private final IOutwardProcessLabService outwardProcessLabService;
    private final IEmailService emailService;

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:list")
    @GetMapping("/list")
    public TableDataInfo<OutwardProcessLabVo> list(OutwardProcessLabBo bo, PageQuery pageQuery) {
        return outwardProcessLabService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @SaCheckPermission("tianxin:processLab:export")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(OutwardProcessLabBo bo, HttpServletResponse response) {
        List<OutwardProcessLabVo> list = outwardProcessLabService.queryList(bo);
        ExcelUtil.exportExcel(list, "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", OutwardProcessLabVo.class, response);
    }

    /**
     * 获取外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:processLab:query")
    @GetMapping("/{id}")
    public R<OutwardProcessLabVo> getInfo(@NotNull(message = "主键不能为空") @PathVariable("id") Long id) {
        return R.ok(outwardProcessLabService.queryById(id));
    }

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:add")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.insertByBo(bo));
    }

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @SaCheckPermission("tianxin:processLab:edit")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody OutwardProcessLabBo bo) {
        return toAjax(outwardProcessLabService.updateByBo(bo));
    }

    /**
     * 删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:processLab:remove")
    @Log(title = "外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空") @PathVariable("ids") Long[] ids) {
        return toAjax(outwardProcessLabService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 获取外发加工信息
     *
     * @param bo 条件
     * @return 列表
     */
    @GetMapping("/getOutSourceList")
    public R<List<OutsourceVo>> getOutSourceList(OutwardProcessLabBo bo) {
        return R.ok(outwardProcessLabService.getOutSourceList(bo));
    }

    /**
     * 发送邮件
     *
     * @param files 附件文件列表
     * @param content 邮件内容
     * @param tableData 表格数据
     * @param subject 邮件主题
     */
    @Log(title = "发送外发加工标签邮件", businessType = BusinessType.OTHER)
    @PostMapping(value = "/sendEmail", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> sendEmail(@RequestParam(value = "files", required = false) MultipartFile[] files,
        @RequestParam(value = "content", required = false) String content, @RequestParam("tableData") String tableData,
        @RequestParam(value = "subject", required = false) String subject) {

        try {
            // 调用邮件服务发送邮件
            emailService.sendProcessLabEmail(files, content, tableData, subject);
            return R.ok();
        } catch (Exception e) {
            log.error("发送邮件失败", e);
            return R.fail("发送邮件失败：" + e.getMessage());
        }
    }

    /**
     * 打印外发加工标签
     *
     * @param ids 标签数据
     * @param response HTTP响应
     */
    @Log(title = "打印外发加工标签", businessType = BusinessType.OTHER)
    @PostMapping(value = "/printLabel")
    public void printLabel(@RequestParam("ids") String ids, HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''"+"test.xlsx");
            // 生成PDF标签
            ByteArrayOutputStream pdfOutputStream = outwardProcessLabService.generatePdfLabels(ids);

            // 将PDF写入响应
            ServletOutputStream outputStream = response.getOutputStream();
            pdfOutputStream.writeTo(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            log.error("打印外发加工标签失败", e);
        }
    }
}
