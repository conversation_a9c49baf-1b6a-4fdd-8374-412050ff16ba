package org.dromara.tianxin.service.qrcodeparse;



import org.dromara.common.core.exception.ServiceException;
import org.dromara.tianxin.domain.dto.MachineryQRCodeResultDTO;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 二维码解析抽象类
 * @param <T>
 * @param <R>
 */
public interface QRCodeAbstractMethod<T,R> {
	/**
	 * 解析抽象方法
	 * @param t
	 * @return
	 */
	R parsing(T t);


	default MachineryQRCodeResultDTO parsing26(String code){
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = new MachineryQRCodeResultDTO();
		machineryQRCodeResultDTO.setLength(code.length());
		machineryQRCodeResultDTO.setMokey(code.substring(0, 12));
		machineryQRCodeResultDTO.setQty(new BigDecimal(code.substring(12, 12+4)));
		machineryQRCodeResultDTO.setSaplot(code.substring(12+4));

		return machineryQRCodeResultDTO;
	}

	default MachineryQRCodeResultDTO parsing16(String code){
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = new MachineryQRCodeResultDTO();
		machineryQRCodeResultDTO.setLength(code.length());
		machineryQRCodeResultDTO.setMokey(code.substring(0, 12));
		machineryQRCodeResultDTO.setWorksetp(code.substring(12));
		return machineryQRCodeResultDTO;
	}

	default MachineryQRCodeResultDTO parsing12(String code){
		MachineryQRCodeResultDTO machineryQRCodeResultDTO = new MachineryQRCodeResultDTO();
		machineryQRCodeResultDTO.setLength(code.length());
		machineryQRCodeResultDTO.setMokey(code.substring(0, 12));
		return machineryQRCodeResultDTO;
	}

	default int checkDigit(String code){
		int length = code.length();
		if(length != 16 && length != 26){
			throw new ServiceException("条码错误,条码位数：" + length);
		}
		return length;
	}

	default int checkDigit(String code,Integer... targetLength){
		int length = code.length();
		AtomicBoolean flag = new AtomicBoolean(false);
		Arrays.stream(targetLength).forEach(x ->{
			if(length == x){
				flag.set(true);
			}
		});
		if(!flag.get()){
			throw new ServiceException("条码错误,条码位数：" + length);
		}
		return length;
	}
}
