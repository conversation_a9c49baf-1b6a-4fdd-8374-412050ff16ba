package org.dromara.tianxin.service.qrcodeparse;

import org.dromara.common.core.exception.ServiceException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class QRCodeMethodFactory {
	@Resource
	private Map<String, QRCodeAbstractMethod> abstractMethodMap = new ConcurrentHashMap<>();

	public <T,R> R run(String method ,T t){
		QRCodeAbstractMethod abstractMethod = abstractMethodMap.get(method);
		if(abstractMethod == null){
			throw new ServiceException("二维码解析规则未定义");
		}
		return (R)abstractMethod.parsing(t);
	}
}
