package org.dromara.tianxin.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * SMB共享文件夹配置
 *
 * <AUTHOR>
 * @date 2025-01-14
 */
@Data
@Component
@ConfigurationProperties(prefix = "tianxin.smb")
public class SmbConfig {

    /**
     * SMB服务器地址
     */
    private String host = "*************";

    /**
     * SMB用户名
     */
    private String username = "smbuser";

    /**
     * SMB密码
     */
    private String password = "smbpassword";

    /**
     * 共享文件夹名称
     */
    private String shareName = "drawings";

    /**
     * 图纸基础路径
     */
    private String basePath = "/drawings/";

    /**
     * 是否启用SMB功能
     */
    private boolean enabled = true;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    private int readTimeout = 30000;

    /**
     * 缓冲区大小（字节）
     */
    private int bufferSize = 131072;

    /**
     * 是否启用连接池
     */
    private boolean connectionPoolEnabled = true;

    /**
     * 连接池最大大小
     */
    private int maxPoolSize = 10;
}
