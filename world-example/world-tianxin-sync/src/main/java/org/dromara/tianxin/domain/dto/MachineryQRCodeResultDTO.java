package org.dromara.tianxin.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MachineryQRCodeResultDTO {
    /**
     * 条码总长度
     */
    private Integer length;
    /**
     * MO号
     */
    private String mokey;
    /**
     * SAP批次
     */
    private String saplot;
    /**
     * 工序
     */
    private String worksetp;
    /**
     * 收货数量
     */
    private BigDecimal qty;
    /**预约单号*/
    private String yyOrder;
}

