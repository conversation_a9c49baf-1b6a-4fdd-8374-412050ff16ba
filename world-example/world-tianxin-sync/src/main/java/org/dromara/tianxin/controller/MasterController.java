package org.dromara.tianxin.controller;

import java.util.List;
import java.util.Map;

import cn.hutool.core.map.MapUtil;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.web.core.BaseController;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.vo.MasterVo;
import org.dromara.tianxin.service.IMasterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.dev33.satoken.annotation.SaCheckPermission;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;

/**
 * PMC订单变更管理
 * 前端访问路由地址为:/tianxin/master
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/master")
public class MasterController extends BaseController {

    private final IMasterService masterService;

    /**
     * 查询PMC订单变更管理列表
     */
    @SaCheckPermission("tianxin:master:list")
    @GetMapping("/list")
    public TableDataInfo<MasterVo> list(MasterBo bo, PageQuery pageQuery) {
        return masterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出PMC订单变更管理列表
     */
    @SaCheckPermission("tianxin:master:export")
    @Log(title = "PMC订单变更管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MasterBo bo, HttpServletResponse response) {
        List<MasterVo> list = masterService.queryList(bo);
        ExcelUtil.exportExcel(list, "PMC订单变更管理", MasterVo.class, response);
    }

    /**
     * 获取PMC订单变更管理详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:master:query")
    @GetMapping("/{id}")
    public R<MasterVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(masterService.queryById(id));
    }

    /**
     * 新增PMC订单变更管理
     */
    @SaCheckPermission("tianxin:master:add")
    @Log(title = "PMC订单变更管理", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MasterBo bo) {
        return toAjax(masterService.insertByBo(bo));
    }

    /**
     * 修改PMC订单变更管理
     */
    @SaCheckPermission("tianxin:master:edit")
    @Log(title = "PMC订单变更管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MasterBo bo) {
        return toAjax(masterService.updateByBo(bo));
    }

    /**
     * 删除PMC订单变更管理
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:master:remove")
    @Log(title = "PMC订单变更管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(masterService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 提交处理方式
     *
     * @param data 包含查询参数和处理方式的数据
     */
    @SaCheckPermission("tianxin:master:edit")
    @Log(title = "PMC订单变更管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/submit")
    public R<Void> submitProcessMethod(@RequestBody Map<String, Object> data) {
        // 获取查询参数和处理方式
        Map<String, Object> queryParams = (Map<String, Object>) data.get("queryParams");
        String processMethod = (String) data.get("processMethod");

        // 将queryParams转换为MasterBo对象
        // sta: undefined,
        // customerCode: undefined,
        // customerPoNo: undefined,
        // moNo: undefined,
        // dwgNo: undefined,
        // finishStatus: '未完成',
        MasterBo bo = new MasterBo();
        bo.setSta((String) queryParams.get("sta"));
        bo.setMoNo((String) queryParams.get("moNo"));
        bo.setCustomerCode((String) queryParams.get("customerCode"));
        bo.setCustomerPoNo((String) queryParams.get("customerPoNo"));
        bo.setDwgNo((String) queryParams.get("dwgNo"));
        bo.setFinishStatus((String) queryParams.get("finishStatus"));

        // 这里可以根据转换后的 bo 对象和 processMethod 执行相应操作
        masterService.handleProcessMethod(bo, processMethod);

        return R.ok();
    }
}
