package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.tianxin.domain.vo.CusEmailVo;
import org.dromara.tianxin.domain.bo.CusEmailBo;
import org.dromara.tianxin.service.ICusEmailService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 供应商信息
 * 前端访问路由地址为:/tianxin/cusEmail
 *
 * <AUTHOR>
 * @date 2025-09-18
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/cusEmail")
public class CusEmailController extends BaseController {

    private final ICusEmailService cusEmailService;

    /**
     * 查询供应商信息列表
     */
    @SaCheckPermission("tianxin:cusEmail:list")
    @GetMapping("/list")
    public TableDataInfo<CusEmailVo> list(CusEmailBo bo, PageQuery pageQuery) {
        return cusEmailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出供应商信息列表
     */
    @SaCheckPermission("tianxin:cusEmail:export")
    @Log(title = "供应商信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CusEmailBo bo, HttpServletResponse response) {
        List<CusEmailVo> list = cusEmailService.queryList(bo);
        ExcelUtil.exportExcel(list, "供应商信息", CusEmailVo.class, response);
    }

    /**
     * 获取供应商信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("tianxin:cusEmail:query")
    @GetMapping("/{id}")
    public R<CusEmailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(cusEmailService.queryById(id));
    }

    /**
     * 新增供应商信息
     */
    @SaCheckPermission("tianxin:cusEmail:add")
    @Log(title = "供应商信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CusEmailBo bo) {
        return toAjax(cusEmailService.insertByBo(bo));
    }

    /**
     * 修改供应商信息
     */
    @SaCheckPermission("tianxin:cusEmail:edit")
    @Log(title = "供应商信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CusEmailBo bo) {
        return toAjax(cusEmailService.updateByBo(bo));
    }

    /**
     * 删除供应商信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("tianxin:cusEmail:remove")
    @Log(title = "供应商信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(cusEmailService.deleteWithValidByIds(List.of(ids), true));
    }
}
