package org.dromara.tianxin.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.LoginUser;
import org.dromara.tianxin.domain.bo.ProductScheduleInfoBo;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.domain.vo.ActionInfoVo;
import org.dromara.tianxin.domain.vo.MasterVo;
import org.dromara.tianxin.domain.vo.ProductScheduleInfoVo;
import org.dromara.tianxin.service.IActionInfoService;
import org.dromara.tianxin.service.IMasterService;
import org.dromara.tianxin.service.IProductScheduleInfoService;
import org.dromara.tianxin.service.IRawMaterialScanService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 原材料仓扫描
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/raw-material-scan")
public class RawMaterialScanController {
    private final IActionInfoService actionInfoService;
    private final IMasterService masterService;
    private final IRawMaterialScanService rawMaterialScanService;
    private final IProductScheduleInfoService productScheduleInfoService;
    @DubboReference
    private RemoteUserService remoteUserService;

    /**
     * 查询动作信息列表
     */
    @SaCheckPermission("tianxin:raw-material-scan:list")
    @GetMapping("/action-info")
    public R<List<ActionInfoVo>> listActionInfo() {
        return R.ok(actionInfoService.getOrganizationActionsByCurrentUser().getActionList());
    }

    /**
     * 查询排产信息列表
     */
    @SaCheckPermission("tianxin:raw-material-scan:list")
    @GetMapping("/schedule-info")
    public R<ProductScheduleInfoVo> listScheduleInfo(String processNumber) {
        var bo = new ProductScheduleInfoBo();
        bo.setZcItm(processNumber);
        var data = productScheduleInfoService.queryPageList(
            bo, new PageQuery()
        ).getRows();
        if (data.isEmpty()) {
            return R.ok();
        }
        return R.ok(data.get(0));
    }

    /**
     * 查询主数据列表
     *
     * @param moNoList moNoList
     * @return 主数据列表
     */
    @SaCheckPermission("tianxin:raw-material-scan:list")
    @GetMapping("/master")
    public R<List<MasterVo>> listMaster(@RequestParam(value = "moNoList", required = false) List<String> moNoList) {
        if (ObjectUtil.isEmpty(moNoList)) {
            return R.ok(List.of());
        }
        return R.ok(masterService.queryListByMoNo(moNoList));
    }

    /**
     * 更新扫描结果
     *
     * @param scanList 扫描结果
     * @return 是否保存成功
     */
    @SaCheckPermission("tianxin:raw-material-scan:list")
    @PostMapping("/scan")
    public R<Void> updateScan(@RequestBody List<ScanRecordLogBo> scanList) {
        rawMaterialScanService.saveScan(scanList);
        return R.ok();
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @SaCheckPermission("tianxin:raw-material-scan:list")
    @GetMapping("/user-info")
    public R<LoginUser> getUserInfoByName(String userName) {
        return R.ok(remoteUserService.getUserInfo(userName, LoginHelper.getTenantId()));
    }
}
