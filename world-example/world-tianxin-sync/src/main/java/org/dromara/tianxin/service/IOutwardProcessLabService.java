package org.dromara.tianxin.service;

import java.io.ByteArrayOutputStream;
import java.util.Collection;
import java.util.List;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等Service接口
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
public interface IOutwardProcessLabService {

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param id 主键
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    OutwardProcessLabVo queryById(Long id);

    /**
     * 分页查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等分页列表
     */
    TableDataInfo<OutwardProcessLabVo> queryPageList(OutwardProcessLabBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    List<OutwardProcessLabVo> queryList(OutwardProcessLabBo bo);

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否新增成功
     */
    Boolean insertByBo(OutwardProcessLabBo bo);

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否修改成功
     */
    Boolean updateByBo(OutwardProcessLabBo bo);

    /**
     * 校验并批量删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等信息
     *
     * @param ids 待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取外发标签列表
     *
     * @param bo
     * @return
     */
    List<OutsourceVo> getOutSourceList(OutwardProcessLabBo bo);

    /**
     * 生成外发加工标签PDF
     *
     * @param ids ID列表
     * @return PDF字节数组
     * @throws Exception 生成过程中可能发生的异常
     */
    ByteArrayOutputStream generatePdfLabels(String ids) throws Exception;
}
