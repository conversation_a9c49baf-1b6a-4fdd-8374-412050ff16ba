package org.dromara.tianxin.service.qrcodeparse.parsing;


import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.tianxin.domain.dto.StandardPartsQRCodeResultDTO;
import org.dromara.tianxin.service.qrcodeparse.QRCodeAbstractMethod;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 机加件条码解析逻辑
 */
@Service("standardPartsQRCodeParsing")
public class StandardPartsQRCodeParsing implements QRCodeAbstractMethod<String, StandardPartsQRCodeResultDTO> {
	@Override
	public StandardPartsQRCodeResultDTO parsing(String code) {
		if(StringUtils.isBlank(code)){
			throw new ServiceException("条码输入空");
		}
		String[] codeArr = code.split(",");
		if(codeArr[0].length() <= 25){
			throw new ServiceException("输入条码位数不正确");
		}
		StandardPartsQRCodeResultDTO machineryQRCodeResultDTO = new StandardPartsQRCodeResultDTO();
		if(codeArr.length > 1){
			machineryQRCodeResultDTO.setYyOrder(codeArr[1]);
		}
		String code01 = codeArr[0];
		machineryQRCodeResultDTO.setLength(code.length());
		machineryQRCodeResultDTO.setExtpokey(code01.substring(10, 20));
		machineryQRCodeResultDTO.setExtpoline(code01.substring(20, 25));
		machineryQRCodeResultDTO.setQty(new BigDecimal(code01.substring(25)));
		return machineryQRCodeResultDTO;
	}
}
