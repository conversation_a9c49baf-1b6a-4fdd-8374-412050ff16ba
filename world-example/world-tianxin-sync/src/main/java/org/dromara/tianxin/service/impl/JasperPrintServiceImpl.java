package org.dromara.tianxin.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import net.sf.jasperreports.engine.export.JRPdfExporter;
import net.sf.jasperreports.engine.util.JRLoader;
import net.sf.jasperreports.export.SimpleExporterInput;
import net.sf.jasperreports.export.SimpleOutputStreamExporterOutput;
import net.sf.jasperreports.export.SimplePdfExporterConfiguration;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.tianxin.service.IJasperPrintService;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Jasper报表打印服务实现类 - 优化版
 *
 * <AUTHOR>
 * @date 2025-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JasperPrintServiceImpl implements IJasperPrintService {

    private static final String JASPER_TEMPLATE_PATH = "jasper/";
    private static final String JASPER_EXTENSION = ".jasper";

    // 模板缓存，避免重复加载
    private static final Map<String, JasperReport> templateCache = new ConcurrentHashMap<>();

    // 字体配置标志，避免重复设置
    private static volatile boolean fontsConfigured = false;

    // 预加载常用模板
    static {
        // 在类加载时预配置字体
        configureFontsOnce();
        // 预加载常用模板
        preloadTemplates();
    }

    @Override
    public byte[] generatePdfPreview(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList) {
        return generatePdf(templateName, parameters, dataList);
    }

    /**
     * 通用PDF生成方法 - 高性能版本
     *
     * @param templateName 模板名称（不含.jasper后缀）
     * @param parameters 报表参数
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    public byte[] generatePdf(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList) {
        long startTime = System.currentTimeMillis();
        try {
            log.info("开始生成PDF，模板：{}，数据条数：{}", templateName, dataList != null ? dataList.size() : 0);

            // 一次性配置字体（避免重复设置）
            configureFontsOnce();

            // 从缓存加载模板
            JasperReport jasperReport = getCachedTemplate(templateName);

            // 准备参数
            Map<String, Object> reportParameters = prepareParameters(parameters);

            // 填充报表
            JasperPrint jasperPrint = fillReport(jasperReport, reportParameters, dataList);

            // 导出为PDF
            byte[] result = exportToPdf(jasperPrint);

            long endTime = System.currentTimeMillis();
            log.info("PDF生成完成，耗时：{}ms，大小：{} bytes", (endTime - startTime), result.length);
            return result;

        } catch (Exception e) {
            log.error("生成PDF失败，模板：{}，错误：{}", templateName, e.getMessage(), e);
            throw new RuntimeException("生成PDF失败：" + e.getMessage(), e);
        }
    }

    /**
     * 超快速PDF生成方法 - 极简版本
     * 适用于已知模板已预加载的情况
     *
     * @param templateName 模板名称
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    public byte[] generatePdfFast(String templateName, List<Map<String, Object>> dataList) {
        return generatePdf(templateName, null, dataList);
    }

    /**
     * 超快速PDF生成方法 - 带参数版本
     *
     * @param templateName 模板名称
     * @param parameters 报表参数
     * @param dataList 数据列表
     * @return PDF字节数组
     */
    public byte[] generatePdfFast(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList) {
        return generatePdf(templateName, parameters, dataList);
    }

    @Override
    public byte[] generatePdfPreviewWithSql(String templateName, Map<String, Object> parameters, String sqlWhere) {
        try {
            // 加载Jasper模板
            JasperReport jasperReport = loadJasperTemplate(templateName);

            // 准备参数
            Map<String, Object> reportParameters = prepareParameters(parameters);
            if (StringUtils.isNotBlank(sqlWhere)) {
                reportParameters.put("sqlwhere", sqlWhere);
            }

            // 填充报表（使用数据库连接）
            JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, reportParameters, new JREmptyDataSource());

            // 导出为PDF
            return exportToPdf(jasperPrint);

        } catch (Exception e) {
            throw new RuntimeException("生成PDF预览失败：" + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getAvailableTemplates() {
        List<String> templates = new ArrayList<>();
        try {
            // 这里可以根据实际需要扫描模板目录
            templates.add("ck_print_fxtgyswf");
            return templates;
        } catch (Exception e) {
            return templates;
        }
    }

    /**
     * 预加载常用模板
     */
    private static void preloadTemplates() {
        try {
            // 预加载常用模板
            String[] commonTemplates = {"ck_print_fxtgyswf"};
            for (String template : commonTemplates) {
                try {
                    loadTemplateToCache(template);
                } catch (Exception e) {
                    log.warn("预加载模板失败：{}，错误：{}", template, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.warn("预加载模板失败：{}", e.getMessage());
        }
    }

    /**
     * 加载模板到缓存
     */
    private static void loadTemplateToCache(String templateName) throws JRException {
        String templatePath = JASPER_TEMPLATE_PATH + templateName + JASPER_EXTENSION;
        ClassPathResource resource = new ClassPathResource(templatePath);

        if (!resource.exists()) {
            throw new RuntimeException("模板文件不存在：" + templatePath);
        }

        try (InputStream inputStream = resource.getInputStream()) {
            JasperReport jasperReport = (JasperReport) JRLoader.loadObject(inputStream);
            templateCache.put(templateName, jasperReport);
        } catch (Exception e) {
            throw new JRException("加载模板失败：" + e.getMessage(), e);
        }
    }

    /**
     * 一次性配置字体（避免重复设置）
     */
    private static void configureFontsOnce() {
        if (!fontsConfigured) {
            synchronized (JasperPrintServiceImpl.class) {
                if (!fontsConfigured) {
                    registerChineseFonts();
                    fontsConfigured = true;
                }
            }
        }
    }

    /**
     * 从缓存获取模板（避免重复加载）
     */
    private JasperReport getCachedTemplate(String templateName) throws JRException {
        JasperReport cached = templateCache.get(templateName);
        if (cached != null) {
            return cached;
        }
        // 如果缓存中没有，则加载并缓存
        JasperReport template = loadJasperTemplate(templateName);
        templateCache.put(templateName, template);
        return template;
    }

    /**
     * 加载Jasper模板
     */
    private JasperReport loadJasperTemplate(String templateName) throws JRException {
        String templatePath = JASPER_TEMPLATE_PATH + templateName + JASPER_EXTENSION;
        ClassPathResource resource = new ClassPathResource(templatePath);

        if (!resource.exists()) {
            throw new RuntimeException("模板文件不存在：" + templatePath);
        }
        try (InputStream inputStream = resource.getInputStream()) {
            JasperReport jasperReport = (JasperReport) JRLoader.loadObject(inputStream);
            return jasperReport;
        } catch (Exception e) {
            throw new JRException("加载模板失败：" + e.getMessage(), e);
        }
    }

    /**
     * 填充报表（优化版）
     */
    private JasperPrint fillReport(JasperReport jasperReport, Map<String, Object> reportParameters, List<Map<String, Object>> dataList) throws JRException {
        if (dataList != null && !dataList.isEmpty()) {
            JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(dataList);
            return JasperFillManager.fillReport(jasperReport, reportParameters, dataSource);
        } else {
            return JasperFillManager.fillReport(jasperReport, reportParameters, new JREmptyDataSource());
        }
    }

    /**
     * 准备报表参数（简化版）
     */
    private Map<String, Object> prepareParameters(Map<String, Object> parameters) {
        return parameters != null ? new HashMap<>(parameters) : new HashMap<>();
    }

    /**
     * 注册中文字体（优化版）
     */
    private static void registerChineseFonts() {
        try {
            // 设置基本编码
            System.setProperty("jasper.reports.encoding", "UTF-8");
            System.setProperty("file.encoding", "UTF-8");
            // 设置字体路径
            System.setProperty("jasper.reports.fonts", "fonts");
            System.setProperty("jasper.reports.fonts.extension", "fonts");
            System.setProperty("jasper.reports.fonts.extension.fonts", "fonts/fonts.xml");

            // 设置默认字体为宋体
            // System.setProperty("jasper.reports.default.font.name", "宋体");
            // System.setProperty("jasper.reports.pdf.font.name", "宋体");
            // System.setProperty("jasper.reports.pdf.encoding", "Identity-H");
            // System.setProperty("jasper.reports.pdf.embedded", "true");

            // log.info("字体配置完成");
        } catch (Exception e) {
            log.warn("中文字体配置失败：{}", e.getMessage());
        }
    }

    /**
     * 导出为PDF（优化版）
     */
    private byte[] exportToPdf(JasperPrint jasperPrint) throws JRException {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            JRPdfExporter exporter = new JRPdfExporter();
            exporter.setExporterInput(new SimpleExporterInput(jasperPrint));
            exporter.setExporterOutput(new SimpleOutputStreamExporterOutput(outputStream));
            // 优化PDF导出配置
            SimplePdfExporterConfiguration configuration = new SimplePdfExporterConfiguration();
            // 启用压缩以减小文件大小
            configuration.setCompressed(true);
            exporter.setConfiguration(configuration);

            exporter.exportReport();

            return outputStream.toByteArray();

        } catch (Exception e) {
            throw new JRException("导出PDF失败：" + e.getMessage(), e);
        }
    }

    /**
     * 简化版PDF生成方法 - 直接使用JasperExportManager
     * 适用于你提到的简化API
     */
    public byte[] generatePdfSimple(String templateName, Map<String, Object> parameters, List<Map<String, Object>> dataList) {
        try {
            // 一次性配置字体（避免重复设置）
            configureFontsOnce();

            // 从缓存加载模板
            JasperReport jasperReport = getCachedTemplate(templateName);

            // 准备参数
            Map<String, Object> reportParameters = prepareParameters(parameters);

            // 填充报表
            JasperPrint jasperPrint;
            if (dataList != null && !dataList.isEmpty()) {
                JRBeanCollectionDataSource dataSource = new JRBeanCollectionDataSource(dataList);
                jasperPrint = JasperFillManager.fillReport(jasperReport, reportParameters, dataSource);
            } else {
                jasperPrint = JasperFillManager.fillReport(jasperReport, reportParameters, new JREmptyDataSource());
            }

            // 使用JasperExportManager直接导出
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                JasperExportManager.exportReportToPdfStream(jasperPrint, outputStream);
                return outputStream.toByteArray();
            }

        } catch (Exception e) {
            log.error("生成PDF失败，模板：{}，错误：{}", templateName, e.getMessage(), e);
            throw new RuntimeException("生成PDF失败：" + e.getMessage(), e);
        }
    }
}
