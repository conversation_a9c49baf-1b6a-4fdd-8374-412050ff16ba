package org.dromara.tianxin.service.impl;

import io.swagger.v3.oas.annotations.servers.Server;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.bo.OutSourcePurchaseScanBo;
import org.dromara.tianxin.domain.vo.MasterVo;
import org.dromara.tianxin.service.IMasterService;
import org.dromara.tianxin.service.IOutSourcePurchaseScanService;
import org.dromara.tianxin.service.IScanRecordLogService;

import java.util.Date;
import java.util.List;

@Server
@AllArgsConstructor
@Slf4j
public class OutSourcePurchaseScanServiceImpl implements IOutSourcePurchaseScanService {
    private final IMasterService masterService;
    private final IScanRecordLogService scanRecordLogService;

    @Override
    public TableDataInfo<MasterVo> queryPageList(OutSourcePurchaseScanBo query, PageQuery pageQuery) {
        var bo = new MasterBo();
        bo.setMoNo(query.getMoNo());
        bo.setCompletionDepartment(query.getSupplier());
        return masterService.queryPageList(bo, pageQuery);
    }

    @Override
    public void saveActionScan(List<MasterBo> masterBos) {
        var actionScans = masterBos.stream().map(item -> {
            var vo = new MasterBo();
            vo.setId(item.getId());
            vo.setMoNo(item.getMoNo());
            vo.setSta(item.getSta());
            vo.setAction(item.getAction());
            vo.setLastProcessTime(new Date());
            return vo;
        }).toList();
        var status = masterService.updateBatchByBo(actionScans);
        if (!status) {
            throw new RuntimeException("更新失败");
        }
        // todo: 插入扫描记录
    }
}
