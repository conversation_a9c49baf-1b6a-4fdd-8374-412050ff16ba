package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.tianxin.domain.bo.PurchaseInfoBo;
import org.dromara.tianxin.domain.vo.PurchaseInfoVo;
import org.dromara.tianxin.service.IPurchaseInfoService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 * 前端访问路由地址为:/system/info
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/info")
public class PurchaseInfoController extends BaseController {

    private final IPurchaseInfoService purchaseInfoService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("system:info:list")
    @GetMapping("/list")
    public TableDataInfo<PurchaseInfoVo> list(PurchaseInfoBo bo, PageQuery pageQuery) {
        return purchaseInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("system:info:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(PurchaseInfoBo bo, HttpServletResponse response) {
        List<PurchaseInfoVo> list = purchaseInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", PurchaseInfoVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param serialkey 主键
     */
    @SaCheckPermission("system:info:query")
    @GetMapping("/{serialkey}")
    public R<PurchaseInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("serialkey") Long serialkey) {
        return R.ok(purchaseInfoService.queryById(serialkey));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("system:info:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody PurchaseInfoBo bo) {
        return toAjax(purchaseInfoService.insertByBo(bo));
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("system:info:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody PurchaseInfoBo bo) {
        return toAjax(purchaseInfoService.updateByBo(bo));
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param serialkeys 主键串
     */
    @SaCheckPermission("system:info:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{serialkeys}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("serialkeys") Long[] serialkeys) {
        return toAjax(purchaseInfoService.deleteWithValidByIds(List.of(serialkeys), true));
    }
}
