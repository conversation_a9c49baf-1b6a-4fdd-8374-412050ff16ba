package org.dromara.tianxin.service;

import org.dromara.tianxin.domain.ProductScheduleInfo;
import org.dromara.tianxin.domain.vo.ProductScheduleInfoVo;
import org.dromara.tianxin.domain.bo.ProductScheduleInfoBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-12
 */
public interface IProductScheduleInfoService {

    /**
     * 查询排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param id 主键
     * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     */
    ProductScheduleInfoVo queryById(Long id);

    /**
     * 分页查询排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息分页列表
     */
    TableDataInfo<ProductScheduleInfoVo> queryPageList(ProductScheduleInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
     *
     * @param bo 查询条件
     * @return 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息列表
     */
    List<ProductScheduleInfoVo> queryList(ProductScheduleInfoBo bo);

    /**
     * 新增排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param bo 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ProductScheduleInfoBo bo);

    /**
     * 修改排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     *
     * @param bo 排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ProductScheduleInfoBo bo);

    /**
     * 校验并批量删除排产信息 - 存储生产排产的详细信息，包括工序、工时、机台等信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
