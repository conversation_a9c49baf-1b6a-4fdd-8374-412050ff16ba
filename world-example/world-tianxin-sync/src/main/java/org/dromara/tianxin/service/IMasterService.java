package org.dromara.tianxin.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.bo.MasterBo;
import org.dromara.tianxin.domain.vo.MasterVo;

import java.util.Collection;
import java.util.List;

/**
 * PMC订单变更管理Service接口
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface IMasterService {

    /**
     * 查询PMC订单变更管理
     *
     * @param id 主键
     * @return PMC订单变更管理
     */
    MasterVo queryById(Long id);

    /**
     * 分页查询PMC订单变更管理列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return PMC订单变更管理分页列表
     */
    TableDataInfo<MasterVo> queryPageList(MasterBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的PMC订单变更管理列表
     *
     * @param bo 查询条件
     * @return PMC订单变更管理列表
     */
    List<MasterVo> queryList(MasterBo bo);

    /**
     * 新增PMC订单变更管理
     *
     * @param bo PMC订单变更管理
     * @return 是否新增成功
     */
    Boolean insertByBo(MasterBo bo);

    /**
     * 修改PMC订单变更管理
     *
     * @param bo PMC订单变更管理
     * @return 是否修改成功
     */
    Boolean updateByBo(MasterBo bo);

    /**
     * 批量更新PMC订单变更管理
     *
     * @param boList PMC订单变更管理列表
     * @return 是否批量更新成功
     */
    Boolean updateBatchByBo(List<MasterBo> boList);

    /**
     * 校验并批量删除PMC订单变更管理信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void handleProcessMethod(MasterBo bo, String processMethod);
}
