package org.dromara.tianxin.service.impl;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.excel.convert.ExcelBigNumberConvert;
import org.dromara.common.excel.utils.PoiUtil;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.tianxin.domain.Master;
import org.dromara.tianxin.domain.OutwardProcessLab;
import org.dromara.tianxin.domain.bo.OutwardProcessLabBo;
import org.dromara.tianxin.domain.vo.OutsourceVo;
import org.dromara.tianxin.domain.vo.OutwardProcessLabVo;
import org.dromara.tianxin.domain.vo.WfLabTemplateVo;
import org.dromara.tianxin.mapper.MasterMapper;
import org.dromara.tianxin.mapper.OutwardProcessLabMapper;
import org.dromara.tianxin.service.IOutwardProcessLabService;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.AreaBreak;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Table;
import com.itextpdf.layout.properties.AreaBreakType;
import com.itextpdf.layout.properties.UnitValue;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.idev.excel.ExcelWriter;
import cn.idev.excel.FastExcel;
import cn.idev.excel.write.metadata.WriteSheet;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OutwardProcessLabServiceImpl implements IOutwardProcessLabService {

    private final OutwardProcessLabMapper baseMapper;

    @Resource
    private MasterMapper masterMapper;

    /**
     * 查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param id 主键
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     */
    @Override
    public OutwardProcessLabVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @param pageQuery 分页参数
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等分页列表
     */
    @Override
    public TableDataInfo<OutwardProcessLabVo> queryPageList(OutwardProcessLabBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        Page<OutwardProcessLabVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     *
     * @param bo 查询条件
     * @return 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等列表
     */
    @Override
    public List<OutwardProcessLabVo> queryList(OutwardProcessLabBo bo) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<OutwardProcessLab> buildQueryWrapper(OutwardProcessLabBo bo) {
        LambdaQueryWrapper<OutwardProcessLab> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(OutwardProcessLab::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getPrtSw()), OutwardProcessLab::getPrtSw, bo.getPrtSw());
        lqw.eq(StringUtils.isNotBlank(bo.getPrNo()), OutwardProcessLab::getPrNo, bo.getPrNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPrItm()), OutwardProcessLab::getPrItm, bo.getPrItm());
        lqw.eq(StringUtils.isNotBlank(bo.getPrType()), OutwardProcessLab::getPrType, bo.getPrType());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdNo()), OutwardProcessLab::getPrdNo, bo.getPrdNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPrdDesc()), OutwardProcessLab::getPrdDesc, bo.getPrdDesc());
        lqw.eq(bo.getQty() != null, OutwardProcessLab::getQty, bo.getQty());
        lqw.eq(StringUtils.isNotBlank(bo.getUt()), OutwardProcessLab::getUt, bo.getUt());
        lqw.eq(bo.getPrDate() != null, OutwardProcessLab::getPrDate, bo.getPrDate());
        lqw.eq(StringUtils.isNotBlank(bo.getPmcRequestDate()), OutwardProcessLab::getPmcRequestDate,
            bo.getPmcRequestDate());
        lqw.eq(StringUtils.isNotBlank(bo.getVendorCode()), OutwardProcessLab::getVendorCode, bo.getVendorCode());
        lqw.eq(StringUtils.isNotBlank(bo.getVendorSnm()), OutwardProcessLab::getVendorSnm, bo.getVendorSnm());
        lqw.like(StringUtils.isNotBlank(bo.getVendorName()), OutwardProcessLab::getVendorName, bo.getVendorName());
        lqw.eq(bo.getUnitPrice() != null, OutwardProcessLab::getUnitPrice, bo.getUnitPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrency()), OutwardProcessLab::getCurrency, bo.getCurrency());
        lqw.eq(StringUtils.isNotBlank(bo.getIsProvideMaterials()), OutwardProcessLab::getIsProvideMaterials,
            bo.getIsProvideMaterials());
        lqw.eq(StringUtils.isNotBlank(bo.getDwgNo()), OutwardProcessLab::getDwgNo, bo.getDwgNo());
        lqw.eq(StringUtils.isNotBlank(bo.getPjNo()), OutwardProcessLab::getPjNo, bo.getPjNo());
        if (StringUtils.isNotBlank(bo.getMoNo())) {
            List<String> moList =
                bo.getMoNo().lines().map(String::trim).filter(s -> !s.isEmpty()).collect(Collectors.toList());
            lqw.in(OutwardProcessLab::getMoNo, moList);
        }

        lqw.eq(StringUtils.isNotBlank(bo.getPurchaseNum()), OutwardProcessLab::getPurchaseNum, bo.getPurchaseNum());
        lqw.eq(StringUtils.isNotBlank(bo.getBatchNo()), OutwardProcessLab::getBatchNo, bo.getBatchNo());
        lqw.eq(bo.getSumPrice() != null, OutwardProcessLab::getSumPrice, bo.getSumPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getSta()), OutwardProcessLab::getSta, bo.getSta());
        lqw.eq(bo.getOutsourcingDate() != null, OutwardProcessLab::getOutsourcingDate, bo.getOutsourcingDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCustNo()), OutwardProcessLab::getCustNo, bo.getCustNo());
        lqw.eq(StringUtils.isNotBlank(bo.getZcNo()), OutwardProcessLab::getZcNo, bo.getZcNo());
        lqw.eq(StringUtils.isNotBlank(bo.getMachiningCenter()), OutwardProcessLab::getMachiningCenter,
            bo.getMachiningCenter());
        lqw.like(StringUtils.isNotBlank(bo.getZcName()), OutwardProcessLab::getZcName, bo.getZcName());
        lqw.eq(StringUtils.isNotBlank(bo.getEpfSnm()), OutwardProcessLab::getEpfSnm, bo.getEpfSnm());
        lqw.eq(StringUtils.isNotBlank(bo.getElectroplateContent()), OutwardProcessLab::getElectroplateContent,
            bo.getElectroplateContent());
        lqw.eq(StringUtils.isNotBlank(bo.getSpecifiedMaterials()), OutwardProcessLab::getSpecifiedMaterials,
            bo.getSpecifiedMaterials());
        lqw.eq(StringUtils.isNotBlank(bo.getIsElectroplate()), OutwardProcessLab::getIsElectroplate,
            bo.getIsElectroplate());
        lqw.eq(StringUtils.isNotBlank(bo.getEpfCode()), OutwardProcessLab::getEpfCode, bo.getEpfCode());
        lqw.like(StringUtils.isNotBlank(bo.getEpfName()), OutwardProcessLab::getEpfName, bo.getEpfName());
        lqw.eq(StringUtils.isNotBlank(bo.getUsr()), OutwardProcessLab::getUsr, bo.getUsr());
        lqw.eq(bo.getSysDate() != null, OutwardProcessLab::getSysDate, bo.getSysDate());
        lqw.eq(StringUtils.isNotBlank(bo.getHost()), OutwardProcessLab::getHost, bo.getHost());
        lqw.eq(StringUtils.isNotBlank(bo.getRem()), OutwardProcessLab::getRem, bo.getRem());

        // 登图日期
        Map<String, Object> params = bo.getParams();
        String beginSysDate = (String)params.get("beginSysDate");
        String endSysDate = (String)params.get("endSysDate");
        if (StringUtils.isNotBlank(beginSysDate) && StringUtils.isNotBlank(endSysDate)) {
            // 解析为 LocalDateTime，end +1 天，用 lt（<）
            LocalDateTime begin = LocalDateTime.parse(beginSysDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime end =
                LocalDateTime.parse(endSysDate, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).plusDays(1); // 加一天
            lqw.ge(OutwardProcessLab::getSysDate, begin).lt(OutwardProcessLab::getSysDate, end); // < end
        }
        return lqw;
    }

    /**
     * 新增外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(OutwardProcessLabBo bo) {
        OutwardProcessLab add = MapstructUtils.convert(bo, OutwardProcessLab.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     *
     * @param bo 外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(OutwardProcessLabBo bo) {
        OutwardProcessLab update = MapstructUtils.convert(bo, OutwardProcessLab.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(OutwardProcessLab entity) {
        // TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除外发加工标签信息 - 存储外发加工标签的详细信息，包括PR信息、厂商信息、电镀信息等信息
     *
     * @param ids 待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 获取外发标签列表
     *
     * @param bo
     * @return
     */
    @Override
    public List<OutsourceVo> getOutSourceList(OutwardProcessLabBo bo) {
        List<OutsourceVo> result = new ArrayList<>();
        LambdaQueryWrapper<OutwardProcessLab> lqw = buildQueryWrapper(bo);
        List<OutwardProcessLabVo> outwardProcessLabVos = baseMapper.selectVoList(lqw);
        if (ObjectUtils.isEmpty(outwardProcessLabVos)) {
            return result;
        }
        // 获取MO列表
        List<String> moNos = outwardProcessLabVos.stream().map(OutwardProcessLabVo::getMoNo).distinct().toList();
        // 根据mo获取mater信息
        LambdaQueryWrapper<Master> masterLqw = new LambdaQueryWrapper<>();
        masterLqw.in(Master::getMoNo, moNos);
        List<Master> masters = masterMapper.selectList(masterLqw);
        for (OutwardProcessLabVo outwardProcessLabVo : outwardProcessLabVos) {
            OutsourceVo outsourceVo = new OutsourceVo();
            outsourceVo.setId(outwardProcessLabVo.getId());
            outsourceVo.setMoNo(outwardProcessLabVo.getMoNo());
            outsourceVo.setVendorSnm(outwardProcessLabVo.getVendorSnm());
            Master master = masters.stream().filter(ma -> ma.getMoNo().equals(outwardProcessLabVo.getMoNo()))
                .findFirst().orElse(null);
            if (master != null) {
                outsourceVo.setDwgNo(master.getDwgNo());
                outsourceVo.setCustomerCode(master.getCustomerCode());
                outsourceVo.setPoType(master.getPoItm());
                outsourceVo.setOrderQuantity(master.getOrderQuantity());
                outsourceVo.setProcessContent(master.getCurrentProcess());
                outsourceVo.setPartClassification(master.getPartClassification());
            }
            result.add(outsourceVo);
        }
        return result;
    }

    /**
     * 生成外发加工标签PDF
     *
     * @param ids 标签数据列表
     * @return PDF字节数组
     * @throws Exception 生成过程中可能发生的异常
     */
    @Override
    public ByteArrayOutputStream generatePdfLabels(String ids) throws Exception {
        if (StringUtils.isBlank(ids)) {
            throw new IllegalArgumentException("标签数据不能为空");
        }
        List<OutwardProcessLabVo> outwardProcessLabVos = baseMapper.selectVoByIds(Arrays.asList(ids.split(",")));

        // 获取模板文件路径
        String templatePath = "/excel/WFLAB_template.xlsx";
        ClassPathResource templateResource = new ClassPathResource(templatePath);

        // 按每4个MO一个sheet页进行分组
        int groupSize = 4;
        int totalSheets = (int)Math.ceil((double)outwardProcessLabVos.size() / groupSize);

        // 使用POI工具预先创建具有足够sheet数量的模板
        InputStream enhancedTemplate = PoiUtil.createSheetFromTemplate(templateResource.getStream(), totalSheets);

        // 创建输出流
        ByteArrayOutputStream excelOutputStream = new ByteArrayOutputStream();

        // 创建Excel写入器
        ExcelWriter excelWriter = FastExcel.write(excelOutputStream).withTemplate(enhancedTemplate)
            .autoCloseStream(false).registerConverter(new ExcelBigNumberConvert()).build();

        for (int sheetIndex = 0; sheetIndex < totalSheets; sheetIndex++) {
            // 使用对应索引的工作表，每个都具有模板格式
            WriteSheet writeSheet = FastExcel.writerSheet(sheetIndex).build();

            // 获取当前sheet的数据
            int fromIndex = sheetIndex * groupSize;
            int toIndex = Math.min(fromIndex + groupSize, outwardProcessLabVos.size());
            List<OutwardProcessLabVo> sheetData = outwardProcessLabVos.subList(fromIndex, toIndex);

            // 填充数据
            Map<String, Object> fillData = new HashMap<>();
            for (int i = 0; i < sheetData.size(); i++) {
                OutwardProcessLabVo item = sheetData.get(i);
                int labelIndex = i + 1;

                // 创建模板数据对象
                WfLabTemplateVo template = convertToTemplateVo(item);

                // 添加到填充数据中，使用索引区分不同标签
                fillData.put("barcode" + labelIndex, template.getBarcode() != null ? template.getBarcode() : "");
                fillData.put("a" + labelIndex, template.getAa() != null ? template.getAa() : "");
                fillData.put("b" + labelIndex, template.getBb() != null ? template.getBb() : "");
                fillData.put("c" + labelIndex, template.getCc() != null ? template.getCc() : "");
                fillData.put("d" + labelIndex, template.getDd() != null ? template.getDd() : "");
                fillData.put("mo" + labelIndex, template.getMo() != null ? template.getMo() : "");
                fillData.put("f" + labelIndex, template.getFf() != null ? template.getFf() : "");
                fillData.put("g" + labelIndex, template.getGg() != null ? template.getGg() : "");
                fillData.put("h" + labelIndex, template.getHh() != null ? template.getHh() : "");
                fillData.put("th" + labelIndex, template.getTh() != null ? template.getTh() : "");
                fillData.put("gys" + labelIndex, template.getGys() != null ? template.getGys() : "");
                fillData.put("ph" + labelIndex, template.getPh() != null ? template.getPh() : "");
                fillData.put("dds" + labelIndex, template.getDds() != null ? template.getDds() : "");
                fillData.put("jq" + labelIndex, template.getJq() != null ? template.getJq() : "");
                fillData.put("shu" + labelIndex, template.getShu() != null ? template.getShu() : "");
                fillData.put("wfrq" + labelIndex, template.getWfrq() != null ? template.getWfrq() : "");
                fillData.put("zdcl" + labelIndex, template.getZdcl() != null ? template.getZdcl() : "");
                fillData.put("shdz" + labelIndex, template.getShdz() != null ? template.getShdz() : "");
                fillData.put("sfgl" + labelIndex, template.getSfgl() != null ? template.getSfgl() : "");
                fillData.put("ddbz" + labelIndex, template.getDdbz() != null ? template.getDdbz() : "");
                fillData.put("kzyq" + labelIndex, template.getKzyq() != null ? template.getKzyq() : "");
            }

            // 如果当前页数据不足4个，用空数据填充剩余位置
            if (sheetData.size() < groupSize) {
                for (int i = sheetData.size(); i < groupSize; i++) {
                    int labelIndex = i + 1;

                    // 添加空数据到填充数据中，使用索引区分不同标签
                    fillData.put("barcode" + labelIndex, "");
                    fillData.put("a" + labelIndex, "");
                    fillData.put("b" + labelIndex, "");
                    fillData.put("c" + labelIndex, "");
                    fillData.put("d" + labelIndex, "");
                    fillData.put("mo" + labelIndex, "");
                    fillData.put("f" + labelIndex, "");
                    fillData.put("g" + labelIndex, "");
                    fillData.put("h" + labelIndex, "");
                    fillData.put("th" + labelIndex, "");
                    fillData.put("gys" + labelIndex, "");
                    fillData.put("ph" + labelIndex, "");
                    fillData.put("dds" + labelIndex, "");
                    fillData.put("jq" + labelIndex, "");
                    fillData.put("shu" + labelIndex, "");
                    fillData.put("wfrq" + labelIndex, "");
                    fillData.put("zdcl" + labelIndex, "");
                    fillData.put("shdz" + labelIndex, "");
                    fillData.put("sfgl" + labelIndex, "");
                    fillData.put("ddbz" + labelIndex, "");
                    fillData.put("kzyq" + labelIndex, "");
                }
            }

            // 填充数据到sheet
            excelWriter.fill(fillData, writeSheet);
        }

        // 完成Excel写入
        excelWriter.finish();

        // 将Excel转换为PDF
        return convertExcelToPdf(excelOutputStream.toByteArray());
    }

    /**
     * 将OutwardProcessLabVo转换为模板数据对象
     *
     * @param data 外发加工标签数据
     * @return 模板数据对象
     */
    private WfLabTemplateVo convertToTemplateVo(OutwardProcessLabVo data) {
        WfLabTemplateVo template = new WfLabTemplateVo();

        // 设置条形码（使用MO号作为条形码内容）
        template.setBarcode(data.getMoNo());

        // 设置标题信息
        template.setAa(data.getPrdDesc());
        template.setBb(data.getPrdNo());
        template.setCc(data.getVendorName());
        template.setDd(data.getZcName());
        template.setMo(data.getMoNo());
        template.setFf(data.getCustNo());
        template.setGg(data.getPjNo());
        template.setHh(data.getRem());

        // 设置详细信息
        template.setTh(data.getDwgNo());
        template.setGys(data.getVendorSnm());
        template.setPh(data.getPrdNo());
        template.setDds(data.getQty() != null ? data.getQty().toString() : null);
        template.setJq(data.getPmcRequestDate());
        template.setShu(data.getQty() != null ? data.getQty().toString() : null);
        template.setWfrq(data.getOutsourcingDate() != null
            ? new java.text.SimpleDateFormat("yyyy-MM-dd").format(data.getOutsourcingDate()) : null);
        template.setZdcl(data.getSpecifiedMaterials());
        template.setShdz(""); // 收货地址暂无对应字段
        template.setSfgl(data.getIsProvideMaterials());
        template.setDdbz(data.getElectroplateContent());
        template.setKzyq(""); // 刻字要求暂无对应字段

        return template;
    }

    /**
     * 将Excel转换为PDF
     *
     * @param excelBytes Excel文件字节数组
     * @return PDF文件输出流
     * @throws Exception 转换过程中可能发生的异常
     */
    private ByteArrayOutputStream convertExcelToPdf(byte[] excelBytes) throws Exception {
        // 创建临时文件用于存储Excel
        File tempExcelFile = File.createTempFile("temp_excel_", ".xlsx");
        try (FileOutputStream fos = new FileOutputStream(tempExcelFile)) {
            fos.write(excelBytes);
        }

        // 创建PDF输出流
        ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();

        // 使用Apache POI将Excel转换为PDF
        // 这里使用更直接的方式，保持Excel的原始格式
        try (FileInputStream fis = new FileInputStream(tempExcelFile)) {
            XSSFWorkbook workbook = new XSSFWorkbook(fis);

            // 使用iText创建PDF文档
            PdfDocument pdfDoc = new PdfDocument(new PdfWriter(pdfOutputStream));
            Document document = new Document(pdfDoc);

            // 为每个sheet创建一页PDF
            for (int sheetIndex = 0; sheetIndex < workbook.getNumberOfSheets(); sheetIndex++) {
                XSSFSheet sheet = workbook.getSheetAt(sheetIndex);

                // 如果不是第一个sheet，添加新页
                if (sheetIndex > 0) {
                    document.add(new AreaBreak(AreaBreakType.NEXT_PAGE));
                }

                // 计算列宽比例
                int lastRowNum = sheet.getLastRowNum();
                if (lastRowNum >= 0) {
                    // 获取第一行来确定列数
                    Row firstRow = sheet.getRow(0);
                    if (firstRow != null) {
                        int columnCount = firstRow.getLastCellNum();
                        if (columnCount > 0) {
                            // 创建表格，根据列数设置列宽
                            float[] columnWidths = new float[columnCount];
                            for (int i = 0; i < columnCount; i++) {
                                columnWidths[i] = 1.0f;
                            }

                            Table table = new Table(UnitValue.createPercentArray(columnWidths))
                                .setWidth(UnitValue.createPercentValue(100));

                            // 处理每一行
                            for (int rowIndex = 0; rowIndex <= lastRowNum; rowIndex++) {
                                Row row = sheet.getRow(rowIndex);
                                if (row != null) {
                                    // 处理每一列
                                    for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                                        org.apache.poi.ss.usermodel.Cell cell = row.getCell(colIndex);
                                        String cellValue = getCellValueAsString(cell);

                                        // 添加单元格到表格
                                        table.addCell(new com.itextpdf.layout.element.Cell()
                                            .add(new Paragraph(cellValue)).setPadding(2));
                                    }
                                } else {
                                    // 添加空行
                                    for (int colIndex = 0; colIndex < columnCount; colIndex++) {
                                        table.addCell(new com.itextpdf.layout.element.Cell().add(new Paragraph(""))
                                            .setPadding(2));
                                    }
                                }
                            }

                            // 添加表格到文档
                            document.add(table);
                        }
                    }
                }
            }

            workbook.close();
            document.close();
        } finally {
            // 删除临时文件
            if (tempExcelFile.exists()) {
                tempExcelFile.delete();
            }
        }

        return pdfOutputStream;
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell Excel单元格
     * @return 单元格的字符串表示
     */
    private String getCellValueAsString(org.apache.poi.ss.usermodel.Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                } else {
                    // 避免科学计数法显示
                    return new java.text.DecimalFormat("#.##").format(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return new java.text.DecimalFormat("#.##").format(cell.getNumericCellValue());
                } catch (Exception e) {
                    try {
                        return cell.getStringCellValue();
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            default:
                return "";
        }
    }
}
