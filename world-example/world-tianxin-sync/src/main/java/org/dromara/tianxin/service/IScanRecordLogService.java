package org.dromara.tianxin.service;


import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.tianxin.domain.bo.ScanRecordLogBo;
import org.dromara.tianxin.domain.vo.ScanRecordLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-10
 */
public interface IScanRecordLogService {

    /**
     * 查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param id 主键
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     */
    ScanRecordLogVo queryById(Long id);

    /**
     * 分页查询扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息分页列表
     */
    TableDataInfo<ScanRecordLogVo> queryPageList(ScanRecordLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     *
     * @param bo 查询条件
     * @return 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息列表
     */
    List<ScanRecordLogVo> queryList(ScanRecordLogBo bo);

    /**
     * 新增扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param bo 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     * @return 是否新增成功
     */
    Boolean insertByBo(ScanRecordLogBo bo);

    /**
     * 修改扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     *
     * @param bo 扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息
     * @return 是否修改成功
     */
    Boolean updateByBo(ScanRecordLogBo bo);

    /**
     * 校验并批量删除扫描记录日志 - 存储扫描记录的详细信息，包括工序、操作员、数量等信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
