package org.dromara.tianxin.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.smbj.SmbjConnect;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * SMB连接池
 * 用于复用SMB连接，避免重复创建连接的开销
 */
@Slf4j
@Component
public class SmbConnectionPool {
    
    private final ConcurrentLinkedQueue<SmbjConnect> availableConnections = new ConcurrentLinkedQueue<>();
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    
    // 连接池配置
    private static final int MAX_TOTAL_CONNECTIONS = 20;
    
    public SmbConnectionPool() {
        // 启动定期清理任务，每30秒清理一次无效连接
        cleanupExecutor.scheduleAtFixedRate(this::cleanupInvalidConnections, 30, 30, TimeUnit.SECONDS);
        log.info("SMB连接池初始化完成，定期清理任务已启动");
    }
    
    /**
     * 获取SMB连接
     */
    public SmbjConnect getConnection(String host, String username, String password) {
        // 尝试从池中获取可用连接
        SmbjConnect connection = availableConnections.poll();
        
        if (connection != null) {
            log.debug("从连接池获取SMB连接，当前活跃连接数: {}", activeConnections.get());
            activeConnections.incrementAndGet();
            return connection;
        }
        
        // 如果池中没有可用连接，创建新连接
        if (totalConnections.get() < MAX_TOTAL_CONNECTIONS) {
            try {
                connection = new SmbjConnect(host, username, password);
                totalConnections.incrementAndGet();
                activeConnections.incrementAndGet();
                log.debug("创建新的SMB连接，总连接数: {}, 活跃连接数: {}", 
                    totalConnections.get(), activeConnections.get());
                return connection;
            } catch (Exception e) {
                log.error("创建SMB连接失败", e);
                throw new RuntimeException("创建SMB连接失败: " + e.getMessage(), e);
            }
        }
        
        // 如果达到最大连接数，等待或抛出异常
        throw new RuntimeException("SMB连接池已满，无法创建新连接");
    }
    
    /**
     * 归还SMB连接到池中
     */
    public void returnConnection(SmbjConnect connection) {
        if (connection != null) {
            try {
                // 检查连接是否仍然有效
                if (isConnectionValid(connection)) {
                    availableConnections.offer(connection);
                    log.debug("SMB连接已归还到池中，当前可用连接数: {}", availableConnections.size());
                } else {
                    // 连接无效，关闭并减少计数
                    connection.close();
                    totalConnections.decrementAndGet();
                    log.debug("无效SMB连接已关闭，总连接数: {}", totalConnections.get());
                }
            } catch (Exception e) {
                log.warn("归还SMB连接时发生异常", e);
                try {
                    connection.close();
                } catch (Exception closeException) {
                    log.warn("关闭SMB连接时发生异常", closeException);
                }
                totalConnections.decrementAndGet();
            } finally {
                activeConnections.decrementAndGet();
            }
        }
    }
    
    /**
     * 检查连接是否有效
     */
    private boolean isConnectionValid(SmbjConnect connection) {
        try {
            if (connection == null) {
                return false;
            }
            // 检查连接是否仍然活跃
            return connection.isConnected();
        } catch (Exception e) {
            log.debug("SMB连接有效性检查失败", e);
            return false;
        }
    }
    
    /**
     * 获取连接池状态
     */
    public String getPoolStatus() {
        return String.format("SMB连接池状态 - 总连接数: %d, 活跃连接数: %d, 可用连接数: %d", 
            totalConnections.get(), activeConnections.get(), availableConnections.size());
    }
    
    /**
     * 定期清理无效连接
     */
    private void cleanupInvalidConnections() {
        try {
            int cleanedCount = 0;
            SmbjConnect connection;
            while ((connection = availableConnections.poll()) != null) {
                if (!isConnectionValid(connection)) {
                    try {
                        connection.close();
                        totalConnections.decrementAndGet();
                        cleanedCount++;
                    } catch (Exception e) {
                        log.debug("清理无效SMB连接时发生异常", e);
                    }
                } else {
                    // 连接仍然有效，重新放回队列
                    availableConnections.offer(connection);
                }
            }
            
            if (cleanedCount > 0) {
                log.debug("SMB连接池清理完成，清理了 {} 个无效连接", cleanedCount);
            }
        } catch (Exception e) {
            log.warn("SMB连接池定期清理时发生异常", e);
        }
    }

    /**
     * 清理连接池
     */
    public void clearPool() {
        log.info("开始清理SMB连接池");
        SmbjConnect connection;
        while ((connection = availableConnections.poll()) != null) {
            try {
                connection.close();
            } catch (Exception e) {
                log.warn("关闭SMB连接时发生异常", e);
            }
        }
        totalConnections.set(0);
        activeConnections.set(0);
        log.info("SMB连接池清理完成");
    }
    
    /**
     * 关闭连接池
     */
    public void shutdown() {
        log.info("开始关闭SMB连接池");
        cleanupExecutor.shutdown();
        clearPool();
        log.info("SMB连接池已关闭");
    }
}
