package org.dromara.tianxin.domain.bo;

import lombok.Generated;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.ReceiveInfoHead;
import org.springframework.data.annotation.Id;

/**
 * 【请填写功能名称】业务对象 receive_info_head
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ReceiveInfoHead.class, reverseConvertGenerate = false)
public class ReceiveInfoHeadBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 单据状态
     */
    private String djSta;

    /**
     * ASN单号
     */
    private String djNo;

    /**
     * 托盘号
     */
    private String lpnNo;

    /**
     * 供应商简称
     */
    private String vendorSnm;

    /**
     * 供应商代号
     */
    private String vendorCode;

    /**
     * 单据日期
     */
    private Date djDate;

    /**
     * 备注
     */
    private String rem;

    /**
     * 操作员工号
     */
    private String usr;

    /**
     * 姓名
     */
    private String usrName;

    /**
     * 操作日期
     */
    private Date sysDate;

    /**
     * 操作电脑名
     */
    private String host;


}
