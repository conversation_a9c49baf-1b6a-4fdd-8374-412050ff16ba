package org.dromara.tianxin.service;

import org.dromara.tianxin.domain.CusEmail;
import org.dromara.tianxin.domain.vo.CusEmailVo;
import org.dromara.tianxin.domain.bo.CusEmailBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 供应商信息Service接口
 *
 * <AUTHOR>
 * @date 2025-09-18
 */
public interface ICusEmailService {

    /**
     * 查询供应商信息
     *
     * @param id 主键
     * @return 供应商信息
     */
    CusEmailVo queryById(Long id);

    /**
     * 分页查询供应商信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 供应商信息分页列表
     */
    TableDataInfo<CusEmailVo> queryPageList(CusEmailBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的供应商信息列表
     *
     * @param bo 查询条件
     * @return 供应商信息列表
     */
    List<CusEmailVo> queryList(CusEmailBo bo);

    /**
     * 新增供应商信息
     *
     * @param bo 供应商信息
     * @return 是否新增成功
     */
    Boolean insertByBo(CusEmailBo bo);

    /**
     * 修改供应商信息
     *
     * @param bo 供应商信息
     * @return 是否修改成功
     */
    Boolean updateByBo(CusEmailBo bo);

    /**
     * 校验并批量删除供应商信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
