package org.dromara.tianxin.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.tianxin.domain.bo.ReceiveInfoDetailBo;
import org.dromara.tianxin.domain.vo.ReceiveInfoDetailVo;
import org.dromara.tianxin.service.IReceiveInfoDetailService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 【请填写功能名称】
 * 前端访问路由地址为:/system/infoDetail
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/infoDetail")
public class ReceiveInfoDetailController extends BaseController {

    private final IReceiveInfoDetailService receiveInfoDetailService;

    /**
     * 查询【请填写功能名称】列表
     */
    @SaCheckPermission("system:infoDetail:list")
    @GetMapping("/list")
    public TableDataInfo<ReceiveInfoDetailVo> list(ReceiveInfoDetailBo bo, PageQuery pageQuery) {
        return receiveInfoDetailService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @SaCheckPermission("system:infoDetail:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ReceiveInfoDetailBo bo, HttpServletResponse response) {
        List<ReceiveInfoDetailVo> list = receiveInfoDetailService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", ReceiveInfoDetailVo.class, response);
    }

    /**
     * 获取【请填写功能名称】详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:infoDetail:query")
    @GetMapping("/{id}")
    public R<ReceiveInfoDetailVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(receiveInfoDetailService.queryById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @SaCheckPermission("system:infoDetail:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ReceiveInfoDetailBo bo) {
        return toAjax(receiveInfoDetailService.insertByBo(bo));
    }

    /**
     * 修改【请填写功能名称】
     */
    @SaCheckPermission("system:infoDetail:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ReceiveInfoDetailBo bo) {
        return toAjax(receiveInfoDetailService.updateByBo(bo));
    }

    /**
     * 删除【请填写功能名称】
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:infoDetail:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ids") Long[] ids) {
        return toAjax(receiveInfoDetailService.deleteWithValidByIds(List.of(ids), true));
    }
}
