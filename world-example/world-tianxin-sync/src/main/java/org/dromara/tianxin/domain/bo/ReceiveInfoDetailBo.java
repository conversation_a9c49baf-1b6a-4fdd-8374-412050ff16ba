package org.dromara.tianxin.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.tianxin.domain.ReceiveInfoDetail;

/**
 * 【请填写功能名称】业务对象 receive_info_detail
 *
 * <AUTHOR>
 * @date 2025-09-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = ReceiveInfoDetail.class, reverseConvertGenerate = false)
public class ReceiveInfoDetailBo extends BaseEntity {

    /**
     * ID
     */
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 单号
     */
    private String djNo;

    /**
     * 行号
     */
    private String djItm;

    /**
     * 单据状态
     */
    private String djSta;

    /**
     * PO类型
     */
    private String poType;

    /**
     * 托盘号
     */
    private String lpnNo;

    /**
     * MO号
     */
    private String moNo;

    /**
     * 工序号
     */
    private String zcNo;

    /**
     * 工序名称
     */
    private String zcName;

    /**
     * 单据日期
     */
    private Date djDate;

    /**
     * 厂商代号
     */
    private String vendorCode;

    /**
     * 简称
     */
    private String vendorSnm;

    /**
     * 采购单号
     */
    private String purchaseNum;

    /**
     * 采购单行号
     */
    private String purchaseItm;

    /**
     * 品号
     */
    private String prdNo;

    /**
     * 物料名称
     */
    private String prdName;

    /**
     * 描述
     */
    private String prdDesc;

    /**
     * PO数量
     */
    private Long poQty;

    /**
     * 收货数量
     */
    private Long actualQty;

    /**
     * 退货数量
     */
    private Long returnQty;

    /**
     * 外发数量
     */
    private Long outerQty;

    /**
     * 单位
     */
    private String ut;

    /**
     * 是否供料
     */
    private String isProvideMaterials;

    /**
     * 客户代码
     */
    private String custNo;

    /**
     * 规格型号
     */
    private String spc;

    /**
     * 品牌
     */
    private String mark;

    /**
     * 储位
     */
    private String storeCw;

    /**
     * 工号
     */
    private String usr;

    /**
     * 姓名
     */
    private String usrName;

    /**
     * SYS_DATE
     */
    private Date sysDate;

    /**
     * host
     */
    private String host;

    /**
     * 备注
     */
    private String rem;


}
