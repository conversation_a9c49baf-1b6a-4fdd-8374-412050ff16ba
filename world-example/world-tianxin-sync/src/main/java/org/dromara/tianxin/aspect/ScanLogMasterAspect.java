package org.dromara.tianxin.aspect;


import org.apache.dubbo.common.logger.Logger;
import org.apache.dubbo.common.logger.LoggerFactory;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.dromara.tianxin.domain.Master;
import org.dromara.tianxin.domain.ScanRecordLog;
import org.dromara.tianxin.domain.dto.ReceiveInfoDTO;
import org.dromara.tianxin.mapper.MasterMapper;
import org.dromara.tianxin.mapper.ScanRecordLogMapper;
import org.dromara.tianxin.utils.ReceivingActionResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.Date;

/**
 * 新增扫描日志和更新master状态
 * 特点：
 * 1. 直接切入Controller层方法
 * 2. 通过@HandleScanRecordMaster注解
 * 3. 在事务提交后执行操作
 */
@Aspect
@Component
public class ScanLogMasterAspect {
    private static final Logger log = LoggerFactory.getLogger(ScanLogMasterAspect.class);

    private final ScanRecordLogMapper scanRecordLogMapper;
    private final MasterMapper masterMapper;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    public ScanLogMasterAspect(ScanRecordLogMapper scanRecordLogMapper, MasterMapper masterMapper) {
        this.scanRecordLogMapper = scanRecordLogMapper;
        this.masterMapper = masterMapper;
    }

    /**
     * 定义通用切入点：所有添加@HandleScanRecordMaster注解的Controller方法
     */
    @Pointcut("@annotation(org.dromara.tianxin.annotation.HandleScanRecordMaster)")
    public void receivingLogPointcut() {}

    /**
     * 统一处理收货完成日志
     */
    @AfterReturning("receivingLogPointcut()")
    public void handleReceivingLog(JoinPoint joinPoint) {
        // 获取DTO参数
        ReceiveInfoDTO receiveInfoDTO = getReceiveInfoDTO(joinPoint);
        if (receiveInfoDTO == null) {
            log.warn("未找到ReceiveInfoDTO参数，跳过日志处理");
            return;
        }

        // 获取当前类的代理实例，确保@Async生效
        ScanLogMasterAspect self = applicationContext.getBean(ScanLogMasterAspect.class);

        // 注册事务同步（确保在事务提交后执行）
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 使用代理实例调用异步方法
                        self.processReceivingLogsAsync(receiveInfoDTO);
                    }
                }
            );
        } else {
            // 无事务环境直接处理
            self.processReceivingLogsAsync(receiveInfoDTO);
        }
    }

    /**
     * 异步处理收货日志的入口（使用指定线程池）
     */
    @Async("tianxinApiExecutor")
    public void processReceivingLogsAsync(ReceiveInfoDTO receiveInfoDTO) {
        processReceivingLogs(receiveInfoDTO);
    }

    /**
     * 处理收货日志的核心逻辑（独立事务）
     */
    @Transactional
    public void processReceivingLogs(ReceiveInfoDTO receiveInfoDTO) {
        String threadName = Thread.currentThread().getName();
        log.info("开始处理收货日志，线程名称: {}", threadName);

        //延迟5秒
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            String vendorCode = receiveInfoDTO.getVendorCode();
            String operator = receiveInfoDTO.getUsr();
            String operatorName = receiveInfoDTO.getUsrName();
            Date startTime = getSysDate("");

            // 遍历所有明细项处理
            for (ReceiveInfoDTO.ReceiveInfoDetailItem item : receiveInfoDTO.getItems()) {
                try {
                    // 1. 插入扫描日志
                    insertScanLog(item, vendorCode, operator, operatorName, startTime);

                    // 2. 更新Master表
                    updateMasterStatus(item);

                    log.info("收货日志处理成功: moNo={}, zcNo={}, 线程名称: {}", item.getMoNo(), item.getZcNo(), threadName);
                } catch (Exception e) {
                    log.error("处理收货日志失败 [moNo:{}], 线程名称: {}", item.getMoNo(), threadName, e);
                }
            }
        } catch (Exception e) {
            log.error("收货日志批处理异常, 线程名称: {}", threadName, e);
        }
    }

    // ===== 工具方法 =====
    private ReceiveInfoDTO getReceiveInfoDTO(JoinPoint joinPoint) {
        for (Object arg : joinPoint.getArgs()) {
            if (arg instanceof ReceiveInfoDTO) {
                return (ReceiveInfoDTO) arg;
            }
        }
        return null;
    }

    private Date getSysDate(String sysDateStr) {
        if (sysDateStr == null || sysDateStr.isEmpty()) {
            return new Date();
        }
        try {
            // 根据实际日期格式调整
            return new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(sysDateStr);
        } catch (Exception e) {
            log.warn("日期解析失败: {}", sysDateStr);
            return new Date();
        }
    }

    private void insertScanLog(ReceiveInfoDTO.ReceiveInfoDetailItem item,
                               String vendorCode, String operator, String operatorName,
                               Date startTime) {
        String action = ReceivingActionResolver.resolveAction(item.getDjNo());
        ScanRecordLog log = new ScanRecordLog();
        log.setMoNo(item.getMoNo());
        log.setProcessNo(item.getZcNo());       // 工序号 = zcNo
        log.setProcessName(item.getZcName());   // 工序名称 = zcName
        log.setOperator(operator);
        log.setOperatorName(operatorName);
        log.setAction(action);
        log.setStartTime(startTime);
        log.setEndTime(new Date());
        log.setRemark(String.valueOf(item.getActualQty())); // 通过数量 = 实际收货量
        log.setCustomerCode(item.getCustNo());
        log.setDrawingNo(item.getSpc());        // 图号 = spc(规格)
        log.setPartNo(item.getPrdNo());         // 品号 = prdNo
        log.setSupplier(vendorCode);            // 供应商代号

        scanRecordLogMapper.insert(log);
    }

    private void updateMasterStatus(ReceiveInfoDTO.ReceiveInfoDetailItem item) {
        String action = ReceivingActionResolver.resolveAction(item.getDjNo());
        Master master = new Master();
        master.setMoNo(item.getMoNo());
        // 状态 = 工序号+工序名称+外发收货送检
        master.setSta(item.getZcNo() + item.getZcName() + action);
        master.setAction(action);
        master.setLastProcessTime(new Date());

        masterMapper.updateStatusByMoNo(master);
    }
}
