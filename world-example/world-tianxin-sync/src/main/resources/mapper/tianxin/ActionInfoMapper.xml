<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.tianxin.mapper.ActionInfoMapper">
    <!-- 根据部门ID列表获取动作信息列表 -->
    <select id="selectActionInfosByDeptIds" resultType="org.dromara.tianxin.domain.vo.ActionInfoVo">
        SELECT DISTINCT ai.action,  ai.status, ai.sequence_no as sequenceNo,
               ai.barcode_no as barcodeNo, ai.is_quantity_controlled as isQuantityControlled,
               ai.is_passed_quantity as isPassedQuantityInputRequired,
               ai.is_scrapped_quantity as isScrappedQuantityInputRequired,
               ai.is_previous_process as isPreviousProcessQuantityCarriedOver,
               ai.allow_skip_process as allowSkipProcess, ai.no_quantity as noQuantityInputRequired,
               ai.operator_code as operatorCode, ai.operator_name as operatorName, ai.operation_time as operationTime
        FROM action_info ai
        WHERE ai.create_dept IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        AND ai.del_flag = '0'
        ORDER BY ai.sequence_no ASC, ai.action ASC
    </select>

</mapper>
