<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.tianxin.mapper.ScanRecordLogMapper">

    <insert id="insertBatchScanRecordLogs" parameterType="java.util.List">
        INSERT INTO scan_record_log (
            mo_no,
            process_no,
            process_name,
            operator,
            operator_name,
            machine_no,
            action,
            start_time,
            end_time,
            passed_quantity,
            scrapped_quantity,
            total_progress,
            current_progress,
            process_man_hours,
            actual_time,
            remark,
            customer_code,
            supplier,
            storage_location,
            part_no,
            drawing_no,
            version,
            material_description,
            customer_product_no,
            customer_product_name,
            batch_no,
            computer_name,
            industry,
            production_schedule_man_hours,
            creation_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
        (
            #{item.moNo},
            #{item.processNo},
            #{item.processName},
            #{item.operator},
            #{item.operatorName},
            #{item.machineNo},
            #{item.action},
            #{item.startTime},
            #{item.endTime},
            #{item.passedQuantity},
            #{item.scrappedQuantity},
            #{item.totalProgress},
            #{item.currentProgress},
            #{item.processManHours},
            #{item.actualTime},
            #{item.remark},
            #{item.customerCode},
            #{item.supplier},
            #{item.storageLocation},
            #{item.partNo},
            #{item.drawingNo},
            #{item.version},
            #{item.materialDescription},
            #{item.customerProductNo},
            #{item.customerProductName},
            #{item.batchNo},
            #{item.computerName},
            #{item.industry},
            #{item.productionScheduleManHours},
            #{item.creationTime}
        )
        </foreach>
    </insert>

</mapper>
