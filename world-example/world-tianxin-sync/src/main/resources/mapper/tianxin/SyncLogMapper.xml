<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.tianxin.mapper.SyncLogMapper">

    <resultMap type="org.dromara.tianxin.domain.vo.SyncLogVo" id="SyncLogVoResult">
        <result property="id" column="id"/>
        <result property="module" column="module"/>
        <result property="dataType" column="data_type"/>
        <result property="dataId" column="data_id"/>
        <result property="syncStatus" column="sync_status"/>
        <result property="errorMessage" column="error_message"/>
        <result property="requestData" column="request_data"/>
        <result property="responseData" column="response_data"/>
        <result property="responseTime" column="response_time"/>
        <result property="retryCount" column="retry_count"/>
        <result property="remark" column="remark"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectSyncLogVo">
        select id, module, data_type, data_id, sync_status, error_message, request_data,
               response_data, response_time, retry_count, remark, create_time, update_time
        from t_tianxin_sync_log
    </sql>

    <select id="selectPageSyncLogList" resultMap="SyncLogVoResult">
        <include refid="selectSyncLogVo"/>
        <where>
            <if test="module != null and module != ''">
                AND module = #{module}
            </if>
            <if test="dataType != null and dataType != ''">
                AND data_type = #{dataType}
            </if>
            <if test="syncStatus != null">
                AND sync_status = #{syncStatus}
            </if>
            <if test="dataId != null and dataId != ''">
                AND data_id LIKE CONCAT('%', #{dataId}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper>
