# SMB连接问题解决方案

## 问题描述

在调用图纸功能时遇到以下问题：
1. 前台没有显示PDF
2. SMB连接异常：`Connection reset` 错误

## 解决方案

### 1. 前台PDF显示问题

**问题原因**：
- 后端返回的Base64数据格式与前端期望不匹配
- 前端没有正确处理Base64数据

**解决方案**：
- 后端直接返回Base64编码的PDF数据
- 前端根据数据类型自动判断处理方式：
  - 如果数据以`http`开头：直接作为URL打开
  - 否则：构造`data:application/pdf;base64,`格式的Data URL

### 2. SMB连接异常问题

**问题原因**：
- SMB服务器连接被重置
- 网络不稳定或SMB服务器配置问题
- 连接管理不当

**解决方案**：

#### 2.1 改进连接管理
```java
// 添加详细的连接日志
log.info("开始连接SMB服务器: {}", smbConfig.getHost());
smbjConnect = new SmbjConnect(smbConfig.getHost(), smbConfig.getUsername(), smbConfig.getPassword());
log.info("SMB连接创建成功");

// 安全关闭连接
try {
    log.info("正在关闭SMB连接...");
    smbjConnect.close();
    log.info("SMB连接已关闭");
} catch (Exception e) {
    log.warn("关闭SMB连接时发生异常: {}", e.getMessage());
}
```

#### 2.2 改进错误处理
```java
// 检查是否是连接相关错误
if (e.getMessage() != null && e.getMessage().contains("Connection reset")) {
    throw new RuntimeException("SMB服务器连接被重置，请检查网络连接和SMB服务器状态");
} else if (e.getMessage() != null && e.getMessage().contains("文件不存在")) {
    throw new RuntimeException("SMB共享文件夹中未找到对应的图纸文件，MO号: " + moNo + ", 图号: " + dwgNo);
} else {
    throw new RuntimeException("从SMB共享文件夹获取图纸失败: " + e.getMessage());
}
```

## 配置建议

### 1. SMB服务器配置检查

确保SMB服务器配置正确：
```yaml
tianxin:
  smb:
    host: *************          # 确保IP地址正确
    username: smbuser            # 确保用户名正确
    password: smbpassword        # 确保密码正确
    share-name: drawings         # 确保共享文件夹名称正确
    base-path: /VN_HW2/          # 确保路径正确
    enabled: true                # 确保功能已启用
```

### 2. 网络连接检查

1. **ping测试**：
   ```bash
   ping *************
   ```

2. **端口测试**：
   ```bash
   telnet ************* 445
   ```

3. **SMB连接测试**：
   ```bash
   smbclient -L //************* -U smbuser
   ```

### 3. 防火墙和权限检查

1. **防火墙设置**：
   - 确保445端口（SMB）开放
   - 确保139端口（NetBIOS）开放

2. **用户权限**：
   - 确保SMB用户有读取共享文件夹的权限
   - 确保共享文件夹路径正确

## 测试步骤

### 1. 测试SMB连接
```bash
# 测试SMB连接
smbclient //*************/drawings -U smbuser

# 列出文件
ls

# 测试文件读取
get MO20240301001.pdf
```

### 2. 测试应用程序
1. 启动应用程序
2. 选择有MO号的订单
3. 点击"调取图纸"按钮
4. 检查日志输出
5. 验证PDF是否正确显示

## 常见问题排查

### 1. 连接被重置
- 检查网络连接稳定性
- 检查SMB服务器状态
- 检查防火墙设置
- 检查用户权限

### 2. 文件不存在
- 检查文件路径是否正确
- 检查文件名是否正确
- 检查文件权限

### 3. PDF不显示
- 检查Base64数据是否正确
- 检查浏览器是否支持PDF显示
- 检查Data URL格式是否正确

## 日志监控

关注以下日志信息：
- SMB连接创建和关闭日志
- 文件查找过程日志
- 错误异常日志
- PDF数据大小日志

## 性能优化建议

1. **连接池**：考虑实现SMB连接池
2. **缓存**：对频繁访问的图纸进行缓存
3. **超时设置**：设置合理的连接和读取超时时间
4. **重试机制**：对连接失败的情况实现重试机制
