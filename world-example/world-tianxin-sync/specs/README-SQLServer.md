# World Tianxin Sync - SQL Server 配置指南

## 概述

本文档说明如何将 world-tianxin-sync 模块从 MySQL 数据库迁移到 SQL Server 数据库。

## 数据库信息

- **服务器地址**: **************
- **端口**: 1433
- **数据库名**: vn_scan
- **用户名**: wderp
- **密码**: Wd@java123

## 配置步骤

### 1. 数据库准备

首先在 SQL Server 中执行建表脚本：

```bash
# 执行建表脚本
sqlcmd -S ************** -U wderp -P "Wd@java123" -d vn_scan -i script/sql/tianxin_sync_tables_sqlserver.sql
```

### 2. 应用配置

#### 方式一：使用配置文件启动

```bash
# 使用 SQL Server 配置启动
java -jar world-tianxin-sync.jar --spring.profiles.active=sqlserver
```

#### 方式二：使用环境变量

```bash
export SPRING_PROFILES_ACTIVE=sqlserver
java -jar world-tianxin-sync.jar
```

#### 方式三：使用 JVM 参数

```bash
java -Dspring.profiles.active=sqlserver -jar world-tianxin-sync.jar
```

### 3. 验证配置

运行数据库连接测试：

```bash
# 运行测试
mvn test -Dtest=DatabaseConnectionTest
```

## 配置文件说明

### application-sqlserver.yml

SQL Server 专用配置文件，包含：

- 数据库连接配置
- 连接池配置
- MyBatis Plus 配置
- 日志配置

### application-dev-sqlserver.yml

开发环境配置文件，用于指定使用 SQL Server 配置。

## 数据库表结构

### t_sync_log (同步日志表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| module | NVARCHAR(50) | 同步模块 |
| data_type | NVARCHAR(50) | 数据类型 |
| data_id | NVARCHAR(100) | 数据ID |
| sync_status | TINYINT | 同步状态: 0-失败 1-成功 |
| sync_time | DATETIME2 | 同步时间 |
| error_message | NVARCHAR(MAX) | 错误信息 |
| request_data | NVARCHAR(MAX) | 请求数据 |
| response_data | NVARCHAR(MAX) | 响应数据 |
| response_time | BIGINT | 响应时间（毫秒） |
| retry_count | INT | 重试次数 |
| remark | NVARCHAR(500) | 备注信息 |
| create_time | DATETIME2 | 创建时间 |
| update_time | DATETIME2 | 更新时间 |
| create_by | NVARCHAR(64) | 创建者 |
| update_by | NVARCHAR(64) | 更新者 |
| deleted | TINYINT | 删除标志 |
| tenant_id | NVARCHAR(20) | 租户编号 |

### t_sync_config (同步配置表)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | BIGINT | 主键ID |
| module | NVARCHAR(50) | 模块名称 |
| config_key | NVARCHAR(100) | 配置键 |
| config_value | NVARCHAR(MAX) | 配置值 |
| description | NVARCHAR(500) | 配置说明 |
| enabled | TINYINT | 是否启用: 1-启用 0-禁用 |
| create_time | DATETIME2 | 创建时间 |
| update_time | DATETIME2 | 更新时间 |
| create_by | NVARCHAR(64) | 创建者 |
| update_by | NVARCHAR(64) | 更新者 |
| deleted | TINYINT | 删除标志 |
| tenant_id | NVARCHAR(20) | 租户编号 |

## 数据库对象

### 视图

- **v_sync_statistics**: 同步统计视图，提供各模块的同步成功率统计

### 存储过程

- **sp_clean_expired_sync_logs**: 清理过期同步日志

### 函数

- **fn_get_sync_status_name**: 获取同步状态描述

### 触发器

- **tr_t_sync_log_update_time**: 自动更新同步日志表的更新时间
- **tr_t_sync_config_update_time**: 自动更新同步配置表的更新时间

## 常用查询示例

### 查询最近24小时的同步统计

```sql
SELECT * FROM [dbo].[v_sync_statistics] 
WHERE [last_sync_time] >= DATEADD(DAY, -1, GETDATE());
```

### 查询失败的同步记录

```sql
SELECT * FROM [dbo].[t_sync_log] 
WHERE [sync_status] = 0 AND [sync_time] >= DATEADD(DAY, -1, GETDATE()) 
ORDER BY [sync_time] DESC;
```

### 查询各模块的同步成功率

```sql
SELECT [module], [data_type], [success_rate] 
FROM [dbo].[v_sync_statistics] 
ORDER BY [success_rate] DESC;
```

### 清理过期日志

```sql
-- 清理30天前的日志
EXEC [dbo].[sp_clean_expired_sync_logs] @days = 30;
```

## 故障排除

### 连接问题

1. **连接超时**
   - 检查网络连接
   - 确认防火墙设置
   - 验证服务器地址和端口

2. **认证失败**
   - 验证用户名和密码
   - 检查用户权限
   - 确认数据库名称

3. **SSL/TLS 问题**
   - 配置文件中已设置 `encrypt=false` 和 `trustServerCertificate=true`
   - 如需启用加密，请修改配置

### 性能优化

1. **连接池配置**
   - 根据实际负载调整连接池大小
   - 监控连接使用情况

2. **索引优化**
   - 已创建必要的索引
   - 根据查询模式调整索引

3. **日志清理**
   - 定期执行日志清理存储过程
   - 避免日志表过大影响性能

## 注意事项

1. **字符编码**: SQL Server 使用 NVARCHAR 支持 Unicode 字符
2. **日期时间**: 使用 DATETIME2 类型获得更好的精度
3. **大字段**: 使用 NVARCHAR(MAX) 存储大文本数据
4. **自增主键**: 使用 IDENTITY(1,1) 实现自增
5. **注释**: 使用扩展属性添加表和字段注释

## 技术支持

如有问题，请检查：

1. 数据库连接配置
2. 网络连通性
3. 用户权限
4. 表结构是否正确创建
5. 应用日志中的错误信息
