# 天心天思API对接架构设计

## 🎯 项目概述

### 对接目标
将world-mes系统与天心天思数字化API平台进行无缝对接，实现以下核心业务的数据同步：

- **基础资料同步**：商品、客户、供应商、仓库等基础信息
- **进销存管理**：采购订单、销售订单、库存管理等
- **生产制造**：生产计划、生产订单、工艺路线等
- **财务管理**：应收应付、成本核算等

### 技术架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   World-MES     │    │   天心API网关    │    │  天心天思ERP     │
│   系统          │◄──►│   客户端模块     │◄──►│   系统          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   业务数据层     │    │   数据转换层     │    │   ERP数据层     │
│   (MySQL)       │    │   (JSON/XML)    │    │   (SQL Server)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🏗️ 系统架构设计

### 1. 分层架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Vue3)                        │
├─────────────────────────────────────────────────────────────┤
│                    网关层 (Gateway)                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  MES业务    │  │  天心API    │  │  数据同步    │         │
│  │   模块      │  │  客户端     │  │   服务      │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (MyBatis)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │   MES数据   │  │   缓存层    │  │  消息队列    │         │
│  │   (MySQL)   │  │  (Redis)    │  │ (RocketMQ)  │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2. 核心模块设计

#### 2.1 天心API客户端模块 (world-common-tianxin)
```
world-common-tianxin/
├── src/main/java/org/dromara/common/tianxin/
│   ├── client/                    # API客户端
│   │   ├── TianxinApiClient.java  # 核心API客户端
│   │   ├── AuthClient.java        # 认证客户端
│   │   └── BaseClient.java        # 基础客户端
│   ├── config/                    # 配置类
│   │   ├── TianxinApiConfig.java  # API配置
│   │   └── TianxinProperties.java # 配置属性
│   ├── domain/                    # 数据模型
│   │   ├── request/               # 请求对象
│   │   │   ├── BaseRequest.java
│   │   │   ├── ProductRequest.java
│   │   │   ├── CustomerRequest.java
│   │   │   └── OrderRequest.java
│   │   └── response/              # 响应对象
│   │       ├── BaseResponse.java
│   │       ├── ProductResponse.java
│   │       └── ApiResult.java
│   ├── enums/                     # 枚举类
│   │   ├── ApiModule.java         # API模块枚举
│   │   ├── DataType.java          # 数据类型枚举
│   │   └── SyncStatus.java        # 同步状态枚举
│   ├── exception/                 # 异常处理
│   │   ├── TianxinApiException.java
│   │   └── TianxinAuthException.java
│   ├── service/                   # 服务接口
│   │   ├── IProductSyncService.java
│   │   ├── ICustomerSyncService.java
│   │   ├── IOrderSyncService.java
│   │   └── IInventorySyncService.java
│   └── utils/                     # 工具类
│       ├── JsonUtils.java
│       ├── DateUtils.java
│       └── ValidationUtils.java
```

#### 2.2 数据同步服务模块 (world-tianxin-sync)
```
world-tianxin-sync/
├── src/main/java/org/dromara/tianxin/
│   ├── controller/                # 控制器
│   │   ├── SyncController.java    # 同步控制
│   │   └── MonitorController.java # 监控控制
│   ├── service/                   # 服务层
│   │   ├── impl/
│   │   │   ├── ProductSyncServiceImpl.java
│   │   │   ├── CustomerSyncServiceImpl.java
│   │   │   ├── OrderSyncServiceImpl.java
│   │   │   └── InventorySyncServiceImpl.java
│   │   └── ISyncService.java
│   ├── mapper/                    # 数据访问
│   │   ├── SyncLogMapper.java
│   │   └── SyncConfigMapper.java
│   ├── domain/                    # 领域模型
│   │   ├── SyncLog.java
│   │   ├── SyncConfig.java
│   │   └── SyncTask.java
│   └── job/                       # 定时任务
│       ├── DataSyncJob.java
│       └── HealthCheckJob.java
```

## 🔄 数据同步流程设计

### 1. 基础资料同步流程
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   MES系统   │    │  数据转换   │    │  天心API    │    │  天心ERP    │
│   发起同步  │───►│   服务      │───►│   客户端    │───►│   系统      │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │                   │
       ▼                   ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  记录同步   │    │  数据验证   │    │  请求封装   │    │  数据存储   │
│   日志      │◄───│   和转换    │◄───│   和发送    │◄───│   和响应    │
└─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

### 2. 业务数据同步流程
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  定时任务   │    │  数据同步   │    │  状态更新   │
│   触发      │───►│   服务      │───►│   和通知    │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  配置检查   │    │  批量处理   │    │  错误处理   │
│   和准备    │◄───│   和同步    │◄───│   和重试    │
└─────────────┘    └─────────────┘    └─────────────┘
```

## 📊 数据映射设计

### 1. 基础资料映射
| MES字段 | 天心字段 | 数据类型 | 说明 |
|---------|----------|----------|------|
| supplier_code | 供应商编码 | String | 供应商唯一标识 |
| supplier_name | 供应商名称 | String | 供应商名称 |
| contact_person | 联系人 | String | 联系人姓名 |
| contact_phone | 联系电话 | String | 联系电话 |
| address | 地址 | String | 供应商地址 |
| status | 状态 | Integer | 1-启用 0-禁用 |

### 2. 商品信息映射
| MES字段 | 天心字段 | 数据类型 | 说明 |
|---------|----------|----------|------|
| part_code | 商品编码 | String | 商品唯一标识 |
| part_name | 商品名称 | String | 商品名称 |
| specification | 规格型号 | String | 商品规格 |
| unit | 单位 | String | 计量单位 |
| price | 价格 | BigDecimal | 商品价格 |
| category | 分类 | String | 商品分类 |

### 3. 订单信息映射
| MES字段 | 天心字段 | 数据类型 | 说明 |
|---------|----------|----------|------|
| order_no | 订单号 | String | 订单唯一标识 |
| customer_code | 客户编码 | String | 客户标识 |
| order_date | 订单日期 | Date | 订单日期 |
| delivery_date | 交货日期 | Date | 交货日期 |
| total_amount | 订单金额 | BigDecimal | 订单总金额 |
| status | 订单状态 | Integer | 订单状态码 |

## 🔧 技术实现方案

### 1. API认证机制
```java
@Component
public class TianxinAuthManager {
    
    private String accessToken;
    private LocalDateTime tokenExpireTime;
    
    /**
     * 获取访问令牌
     */
    public String getAccessToken() {
        if (isTokenExpired()) {
            refreshToken();
        }
        return accessToken;
    }
    
    /**
     * 刷新令牌
     */
    private void refreshToken() {
        // 调用天心API获取新令牌
        AuthRequest request = new AuthRequest();
        request.setAppId(config.getAppId());
        request.setAppSecret(config.getAppSecret());
        
        AuthResponse response = authClient.authenticate(request);
        this.accessToken = response.getAccessToken();
        this.tokenExpireTime = LocalDateTime.now().plusSeconds(response.getExpiresIn());
    }
}
```

### 2. 数据同步服务
```java
@Service
public class ProductSyncServiceImpl implements IProductSyncService {
    
    @Autowired
    private TianxinApiClient apiClient;
    
    @Autowired
    private ProductMapper productMapper;
    
    /**
     * 同步商品数据
     */
    @Override
    @Transactional
    public void syncProducts() {
        try {
            // 1. 获取MES系统中的商品数据
            List<Product> mesProducts = productMapper.selectList(null);
            
            // 2. 转换为天心API格式
            List<ProductRequest> requests = mesProducts.stream()
                .map(this::convertToTianxinFormat)
                .collect(Collectors.toList());
            
            // 3. 批量同步到天心系统
            for (ProductRequest request : requests) {
                ApiResult result = apiClient.syncProduct(request);
                if (result.isSuccess()) {
                    // 更新同步状态
                    updateSyncStatus(request.getPartCode(), SyncStatus.SUCCESS);
                } else {
                    // 记录同步失败
                    logSyncError(request.getPartCode(), result.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("商品数据同步失败", e);
            throw new TianxinApiException("商品数据同步失败: " + e.getMessage());
        }
    }
}
```

### 3. 定时同步任务
```java
@Component
public class DataSyncJob {
    
    @Autowired
    private IProductSyncService productSyncService;
    
    @Autowired
    private ICustomerSyncService customerSyncService;
    
    /**
     * 基础资料同步任务
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void syncBasicData() {
        log.info("开始执行基础资料同步任务");
        
        try {
            // 同步商品数据
            productSyncService.syncProducts();
            
            // 同步客户数据
            customerSyncService.syncCustomers();
            
            log.info("基础资料同步任务执行完成");
        } catch (Exception e) {
            log.error("基础资料同步任务执行失败", e);
        }
    }
    
    /**
     * 业务数据同步任务
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void syncBusinessData() {
        log.info("开始执行业务数据同步任务");
        
        try {
            // 同步订单数据
            orderSyncService.syncOrders();
            
            // 同步库存数据
            inventorySyncService.syncInventory();
            
            log.info("业务数据同步任务执行完成");
        } catch (Exception e) {
            log.error("业务数据同步任务执行失败", e);
        }
    }
}
```

## 📋 配置管理

### 1. 应用配置
```yaml
# application-tianxin.yml
tianxin:
  api:
    base-url: http://api.amtxts.com
    app-id: ${TIANXIN_APP_ID:your_app_id}
    app-secret: ${TIANXIN_APP_SECRET:your_app_secret}
    timeout: 30000
    retry-times: 3
    retry-interval: 1000
  sync:
    enabled: true
    batch-size: 100
    thread-pool-size: 10
    schedule:
      basic-data: "0 0 2 * * ?"  # 基础资料同步时间
      business-data: "0 */5 * * * ?"  # 业务数据同步时间
```

### 2. 数据库配置
```sql
-- 同步日志表
CREATE TABLE t_sync_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    module VARCHAR(50) NOT NULL COMMENT '同步模块',
    data_type VARCHAR(50) NOT NULL COMMENT '数据类型',
    data_id VARCHAR(100) NOT NULL COMMENT '数据ID',
    sync_status TINYINT NOT NULL COMMENT '同步状态: 0-失败 1-成功',
    sync_time DATETIME NOT NULL COMMENT '同步时间',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_module_type (module, data_type),
    INDEX idx_sync_time (sync_time)
);

-- 同步配置表
CREATE TABLE t_sync_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    module VARCHAR(50) NOT NULL COMMENT '模块名称',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(500) COMMENT '配置说明',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_module_key (module, config_key)
);
```

## 🚀 部署方案

### 1. 模块部署
```xml
<!-- 在world-common/pom.xml中添加 -->
<module>world-common-tianxin</module>

<!-- 在world-modules/pom.xml中添加 -->
<module>world-tianxin-sync</module>
```

### 2. 依赖配置
```xml
<!-- world-tianxin-sync/pom.xml -->
<dependencies>
    <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>world-common-tianxin</artifactId>
    </dependency>
    <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>world-common-web</artifactId>
    </dependency>
    <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>world-common-mybatis</artifactId>
    </dependency>
    <dependency>
        <groupId>org.dromara</groupId>
        <artifactId>world-common-job</artifactId>
    </dependency>
</dependencies>
```

## 📈 监控和运维

### 1. 健康检查
```java
@RestController
@RequestMapping("/tianxin/monitor")
public class TianxinMonitorController {
    
    @GetMapping("/health")
    public R<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        // 检查API连接状态
        boolean apiConnected = checkApiConnection();
        health.put("apiConnected", apiConnected);
        
        // 检查同步任务状态
        List<SyncTask> recentTasks = getRecentSyncTasks();
        health.put("recentTasks", recentTasks);
        
        // 检查错误率
        double errorRate = calculateErrorRate();
        health.put("errorRate", errorRate);
        
        return R.ok(health);
    }
}
```

### 2. 日志监控
```java
@Component
public class TianxinLogMonitor {
    
    @EventListener
    public void handleSyncEvent(SyncEvent event) {
        // 记录同步事件
        log.info("同步事件: {} - {} - {}", 
            event.getModule(), 
            event.getDataType(), 
            event.getStatus());
        
        // 发送监控告警
        if (event.getStatus() == SyncStatus.FAILED) {
            sendAlert(event);
        }
    }
}
```

## 🔒 安全考虑

### 1. 数据加密
- API通信使用HTTPS协议
- 敏感数据使用AES加密存储
- 访问令牌定期刷新

### 2. 权限控制
- 基于角色的访问控制
- API调用频率限制
- 数据访问审计日志

### 3. 异常处理
- 网络异常重试机制
- 数据校验和清洗
- 错误日志记录和告警

## 📚 使用说明

### 1. 环境准备
1. 确保天心天思API平台账号和权限
2. 配置API访问密钥
3. 准备测试环境数据

### 2. 配置步骤
1. 修改配置文件中的API地址和密钥
2. 配置数据库连接
3. 启动同步服务

### 3. 测试验证
1. 执行基础资料同步测试
2. 验证数据映射正确性
3. 检查同步日志和状态

这个架构设计确保了world-mes系统与天心天思API平台的高效、稳定、安全对接，支持实时和批量数据同步，具备完善的监控和异常处理机制。
