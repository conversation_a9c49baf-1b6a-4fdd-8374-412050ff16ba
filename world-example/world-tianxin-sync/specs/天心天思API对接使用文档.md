# 天心天思API对接使用文档

## 📋 目录

- [项目概述](#项目概述)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [API接口文档](#api接口文档)
- [数据同步功能](#数据同步功能)
- [监控和运维](#监控和运维)
- [故障排除](#故障排除)
- [常见问题](#常见问题)
- [更新日志](#更新日志)

---

## 🎯 项目概述

### 功能简介

本系统实现了world-mes系统与天心天思数字化API平台的无缝对接，支持以下核心功能：

- **基础资料同步**：商品、客户、供应商等基础信息
- **进销存管理**：采购订单、销售订单、库存管理等
- **生产制造**：生产计划、生产订单、工艺路线等
- **财务管理**：应收应付、成本核算等
- **实时监控**：同步状态监控、健康检查、告警通知

### 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   World-MES     │    │   天心API网关    │    │  天心天思ERP     │
│   系统          │◄──►│   客户端模块     │◄──►│   系统          │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心特性

- ✅ **高性能**：支持批量同步，异步处理
- ✅ **高可靠**：自动重试机制，异常处理
- ✅ **可监控**：完整的日志记录，状态监控
- ✅ **易扩展**：模块化设计，支持新业务接入
- ✅ **安全**：API认证，数据加密传输
- ✅ **轻量级**：基于Hutool工具包，减少依赖

---

## 🚀 快速开始

### 环境要求

- **Java**: JDK 17+
- **Spring Boot**: 3.4.6+
- **MySQL**: 5.7+ 或 8.0+
- **Redis**: 6.x+
- **Maven**: 3.8+
- **Hutool**: 5.8+ (HTTP和JSON工具包)

### 安装步骤

#### 1. 克隆项目

```bash
git clone [项目地址]
cd world-cloud-plus
```

#### 2. 数据库初始化

```bash
# 执行数据库脚本
sql server -u root -p < script/sql/[tianxin_sync_tables_sqlserver.sql](../../../script/sql/tianxin_sync_tables_sqlserver.sql)
```

#### 3. 配置修改

```bash
# 复制配置文件
cp script/config/application-tianxin.yml src/main/resources/

# 修改配置
vim src/main/resources/application-tianxin.yml
```

#### 4. 编译运行

```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 验证安装

访问健康检查接口：

```bash
curl http://localhost:8080/tianxin/monitor/health
```

预期响应：

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "systemStatus": "UP",
    "syncStatus": {
      "apiConnected": true,
      "productStats": {
        "totalCount": 0,
        "successCount": 0,
        "failCount": 0,
        "successRate": "0.00%"
      }
    },
    "timestamp": 1706342400000
  }
}
```

---

## ⚙️ 配置说明

### 基础配置

```yaml
tianxin:
  api:
    # API基础URL
    base-url: http://api.amtxts.com
    
    # 应用认证信息
    app-id: your_app_id_here
    app-secret: your_app_secret_here
    
    # 请求超时配置
    timeout: 30000
    retry-times: 3
    retry-interval: 1000
    
    # 功能开关
    enabled: true
    log-enabled: true
    cache-enabled: true
```

### 同步配置

```yaml
tianxin:
  sync:
    # 同步功能总开关
    enabled: true
    
    # 定时任务配置
    schedule:
      basic-data: "0 0 2 * * ?"      # 基础资料同步
      business-data: "0 */5 * * * ?"  # 业务数据同步
      health-check: "0 */10 * * * ?"  # 健康检查
    
    # 各模块同步配置
    modules:
      product:
        enabled: true
        batch-size: 50
        schedule: "0 0 2 * * ?"
```

### 环境变量配置

生产环境建议使用环境变量：

```bash
# 设置环境变量
export TIANXIN_APP_ID=your_actual_app_id
export TIANXIN_APP_SECRET=your_actual_app_secret
export TIANXIN_API_BASE_URL=https://api.amtxts.com
```

---

## 📚 API接口文档

### 基础资料同步

#### 1. 同步商品数据

**接口地址**: `POST /tianxin/sync/product`

**请求参数**:
```json
{
  "productCode": "P001",
  "productName": "测试商品",
  "specification": "规格型号",
  "unit": "个",
  "category": "电子产品",
  "standardPrice": 100.00,
  "status": 1,
  "operationType": "ADD"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "商品同步成功",
    "data": {
      "productId": "P001",
      "syncTime": "2025-01-27 10:00:00"
    }
  }
}
```

#### 2. 批量同步商品数据

**接口地址**: `POST /tianxin/sync/product/batch`

**请求参数**:
```json
[
  {
    "productCode": "P001",
    "productName": "测试商品1",
    "specification": "规格型号1",
    "unit": "个",
    "category": "电子产品",
    "standardPrice": 100.00,
    "status": 1,
    "operationType": "ADD"
  },
  {
    "productCode": "P002",
    "productName": "测试商品2",
    "specification": "规格型号2",
    "unit": "个",
    "category": "电子产品",
    "standardPrice": 200.00,
    "status": 1,
    "operationType": "ADD"
  }
]
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 2,
    "successCount": 2,
    "failCount": 0,
    "errors": [],
    "message": "商品数据同步完成，成功: 2，失败: 0"
  }
}
```

#### 3. 同步客户数据

**接口地址**: `POST /tianxin/sync/customer`

**请求参数**:
```json
{
  "customerCode": "C001",
  "customerName": "测试客户",
  "customerType": 1,
  "contactPerson": "张三",
  "contactPhone": "13800138000",
  "contactEmail": "<EMAIL>",
  "address": "北京市朝阳区",
  "customerLevel": 1,
  "status": 1,
  "operationType": "ADD"
}
```

#### 4. 同步供应商数据

**接口地址**: `POST /tianxin/sync/supplier`

**请求参数**:
```json
{
  "supplierCode": "S001",
  "supplierName": "测试供应商",
  "supplierType": 1,
  "contactPerson": "李四",
  "contactPhone": "13900139000",
  "contactEmail": "<EMAIL>",
  "address": "上海市浦东新区",
  "supplierLevel": 1,
  "status": 1,
  "operationType": "ADD"
}
```

### 业务数据同步

#### 1. 同步库存数据

**接口地址**: `POST /tianxin/sync/inventory`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "库存数据同步完成",
    "syncCount": 150
  }
}
```

#### 2. 同步销售订单

**接口地址**: `POST /tianxin/sync/sales/orders`

#### 3. 同步采购订单

**接口地址**: `POST /tianxin/sync/purchase/orders`

#### 4. 同步生产计划

**接口地址**: `POST /tianxin/sync/production/plans`

#### 5. 同步生产订单

**接口地址**: `POST /tianxin/sync/production/orders`

### 通用同步方法

#### 1. 执行全量数据同步

**接口地址**: `POST /tianxin/sync/full`

#### 2. 执行增量数据同步

**接口地址**: `POST /tianxin/sync/incremental`

#### 3. 检查同步状态

**接口地址**: `GET /tianxin/sync/status`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "apiConnected": true,
    "productStats": {
      "totalCount": 100,
      "successCount": 95,
      "failCount": 5,
      "successRate": "95.00%"
    },
    "customerStats": {
      "totalCount": 50,
      "successCount": 48,
      "failCount": 2,
      "successRate": "96.00%"
    }
  }
}
```

#### 4. 重试失败的同步任务

**接口地址**: `POST /tianxin/sync/retry`

### 同步日志管理

#### 1. 查询同步日志列表

**接口地址**: `GET /tianxin/sync/log/list`

**请求参数**:
- `module`: 同步模块（可选）
- `dataType`: 数据类型（可选）
- `syncStatus`: 同步状态（可选）
- `pageNum`: 页码（默认1）
- `pageSize`: 页大小（默认10）

#### 2. 获取同步日志详情

**接口地址**: `GET /tianxin/sync/log/{id}`

#### 3. 删除同步日志

**接口地址**: `DELETE /tianxin/sync/log/{ids}`

#### 4. 获取同步统计信息

**接口地址**: `GET /tianxin/sync/statistics`

---

## 🔄 数据同步功能

### 自动同步

系统支持以下自动同步功能：

#### 1. 定时同步

- **基础资料同步**: 每天凌晨2点执行
- **业务数据同步**: 每5分钟执行一次
- **健康检查**: 每10分钟执行一次
- **日志清理**: 每天凌晨3点执行

#### 2. 触发同步

- **手动触发**: 通过API接口手动触发同步
- **事件触发**: 基于业务事件自动触发同步
- **失败重试**: 自动重试失败的同步任务

### 同步策略

#### 1. 全量同步

- 适用于基础资料数据
- 定期执行，确保数据一致性
- 支持数据校验和冲突处理

#### 2. 增量同步

- 适用于业务数据
- 基于时间戳或版本号
- 提高同步效率，减少网络开销

#### 3. 实时同步

- 适用于关键业务数据
- 基于消息队列或事件驱动
- 保证数据的实时性

### 数据映射

#### 商品信息映射

| MES字段 | 天心字段 | 数据类型 | 说明 |
|---------|----------|----------|------|
| part_code | productCode | String | 商品编码 |
| part_name | productName | String | 商品名称 |
| specification | specification | String | 商品规格 |
| unit | unit | String | 计量单位 |
| price | standardPrice | BigDecimal | 标准价格 |
| status | status | Integer | 商品状态 |

#### 客户信息映射

| MES字段 | 天心字段 | 数据类型 | 说明 |
|---------|----------|----------|------|
| customer_code | customerCode | String | 客户编码 |
| customer_name | customerName | String | 客户名称 |
| contact_person | contactPerson | String | 联系人 |
| contact_phone | contactPhone | String | 联系电话 |
| address | address | String | 客户地址 |
| status | status | Integer | 客户状态 |

---

## 📊 监控和运维

### 健康检查

#### 1. 系统健康检查

**接口地址**: `GET /tianxin/monitor/health`

**检查项目**:
- API连接状态
- 数据库连接状态
- Redis连接状态
- 同步任务状态
- 系统资源使用情况

#### 2. API状态检查

**接口地址**: `GET /tianxin/monitor/api/status`

#### 3. 系统信息查询

**接口地址**: `GET /tianxin/monitor/info`

### 日志监控

#### 1. 同步日志

- 记录所有API调用详情
- 包含请求参数、响应结果、执行时间
- 支持按模块、状态、时间范围查询

#### 2. 错误日志

- 记录同步失败的原因
- 包含异常堆栈信息
- 支持错误统计和分析

#### 3. 性能日志

- 记录API响应时间
- 统计同步成功率
- 监控系统性能指标

### 告警通知

#### 1. 邮件告警

```yaml
alert:
  email:
    enabled: true
    smtp:
      host: smtp.qq.com
      port: 587
      username: <EMAIL>
      password: your_password
    to: <EMAIL>
```

#### 2. 短信告警

```yaml
alert:
  sms:
    enabled: true
    phone: 13800138000
```

#### 3. 钉钉告警

```yaml
alert:
  dingtalk:
    enabled: true
    webhook: https://oapi.dingtalk.com/robot/send?access_token=xxx
    secret: your_secret
```

### 性能优化

#### 1. 批量处理

- 支持批量同步，减少API调用次数
- 可配置批量大小，平衡性能和内存使用
- 支持分批处理，避免超时

#### 2. 异步处理

- 使用线程池异步处理同步任务
- 支持并发同步，提高处理效率
- 避免阻塞主业务流程

#### 3. 缓存机制

- 缓存API访问令牌
- 缓存频繁查询的数据
- 减少重复请求，提高响应速度

---

## 🔧 故障排除

### 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| AUTH_ERROR | 认证失败 | 检查AppId和AppSecret配置 |
| HTTP_ERROR | HTTP请求失败 | 检查网络连接和API地址 |
| IO_ERROR | 网络请求异常 | 检查网络连接和防火墙设置 |
| VALIDATION_ERROR | 数据验证失败 | 检查请求参数格式和必填字段 |
| SYSTEM_ERROR | 系统异常 | 查看详细日志，联系技术支持 |

### 日志分析

#### 1. 查看同步日志

```bash
# 查看最近的同步日志
tail -f logs/tianxin-sync.log

# 查看错误日志
grep "ERROR" logs/tianxin-sync.log

# 查看特定模块的日志
grep "PRODUCT" logs/tianxin-sync.log
```

#### 2. 数据库查询

```sql
-- 查询失败的同步记录
SELECT * FROM t_sync_log 
WHERE sync_status = 0 
AND sync_time >= DATE_SUB(NOW(), INTERVAL 1 DAY) 
ORDER BY sync_time DESC;

-- 查询同步统计
SELECT * FROM v_sync_statistics;

-- 查询各模块成功率
SELECT module, data_type, success_rate 
FROM v_sync_statistics 
ORDER BY success_rate DESC;
```

### 性能调优

#### 1. 调整线程池配置

```yaml
spring:
  task:
    execution:
      pool:
        core-size: 20
        max-size: 50
        queue-capacity: 500
```

#### 2. 调整批量大小

```yaml
tianxin:
  sync:
    modules:
      product:
        batch-size: 100
```

#### 3. 调整超时时间

```yaml
tianxin:
  api:
    timeout: 60000
```

---

## ❓ 常见问题

### Q1: 如何获取天心天思API的AppId和AppSecret？

A: 请联系天心天思技术支持，申请API访问权限，获取AppId和AppSecret。

### Q2: 同步失败如何处理？

A: 
1. 查看同步日志，了解失败原因
2. 检查网络连接和API配置
3. 使用重试接口重新同步
4. 联系技术支持

### Q3: 如何监控同步状态？

A: 
1. 使用健康检查接口监控系统状态
2. 查看同步统计接口了解成功率
3. 配置告警通知，及时发现问题

### Q4: 数据同步有延迟怎么办？

A: 
1. 检查网络连接质量
2. 调整批量大小和线程池配置
3. 考虑使用增量同步减少数据量

### Q5: 如何添加新的同步模块？

A: 
1. 在`TianxinApiClient`中添加新的API方法
2. 在`SyncService`中实现同步逻辑
3. 在`SyncController`中添加接口
4. 配置定时任务和权限

### Q6: 生产环境部署注意事项？

A: 
1. 使用环境变量管理敏感配置
2. 配置适当的日志级别
3. 启用告警通知
4. 定期备份数据库
5. 监控系统资源使用情况

---

## 📝 更新日志

### v1.0.0 (2025-01-27)

#### 新增功能
- ✅ 实现天心天思API客户端模块
- ✅ 支持基础资料同步（商品、客户、供应商）
- ✅ 支持业务数据同步（库存、订单、生产）
- ✅ 实现自动重试和异常处理机制
- ✅ 提供完整的监控和日志功能
- ✅ 支持定时任务和手动触发同步
- ✅ 提供RESTful API接口
- ✅ 支持批量同步和异步处理

#### 技术特性
- ✅ 基于Spring Boot 3.x构建
- ✅ 使用MyBatis-Plus进行数据访问
- ✅ 集成Redis缓存和消息队列
- ✅ 支持多环境配置
- ✅ 提供完整的单元测试

#### 文档完善
- ✅ 提供详细的API文档
- ✅ 包含配置说明和部署指南
- ✅ 提供故障排除和常见问题解答
- ✅ 包含性能优化建议

---

## 📞 技术支持

### 联系方式

- **技术支持邮箱**: <EMAIL>
- **项目地址**: [GitHub链接]
- **文档地址**: [文档链接]
- **问题反馈**: [Issues链接]

### 开发团队

- **架构设计**: World
- **后端开发**: World
- **测试**: World
- **文档**: World

---

## 📄 许可证

本项目采用 [MIT License](LICENSE) 许可证。

---

*最后更新时间: 2025-01-27*
