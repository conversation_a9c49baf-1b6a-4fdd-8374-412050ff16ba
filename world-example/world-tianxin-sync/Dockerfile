#FROM anapsix/alpine-java:8_server-jre_unlimited
FROM dockerhub.world-machining.com/library/dragonwell:dragonwell-********.3.7_jdk-17.0.3-ga

MAINTAINER world.com
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN sed -i 's/TLSv1, TLSv1.1, //' /opt/java/openjdk/conf/security/java.security

ENV JAVA_OPTS=""

RUN mkdir -p /world
WORKDIR /world

ADD ./target/world-tianxin-sync.jar .

EXPOSE 9405

CMD ["java","-Dsun.zip.disableMemoryMapping = true","-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=8888", "-XX:+HeapDumpOnOutOfMemoryError", "-XX:HeapDumpPath=/logs/springboot/heapdump.hprof", "-jar", "world-tianxin-sync.jar"]
