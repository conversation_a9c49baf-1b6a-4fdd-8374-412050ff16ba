<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>world-example</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>world-tianxin-sync</artifactId>

    <description>
        world-tianxin-sync 越南天心天思数据同步服务模块
    </description>

    <dependencies>

        <!-- world Common Core -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-core</artifactId>
        </dependency>

        <!-- world Common Nacos -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-nacos</artifactId>
        </dependency>

        <!-- world Common Sentinel -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-sentinel</artifactId>
        </dependency>

        <!-- world Common Log -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-log</artifactId>
        </dependency>

        <!-- world Common Service Impl -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-service-impl</artifactId>
        </dependency>

        <!-- world Common Doc -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-doc</artifactId>
        </dependency>

        <!-- world Common Web -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-web</artifactId>
        </dependency>

        <!-- world Common MyBatis -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-mybatis</artifactId>
        </dependency>

        <!-- world Common Dubbo -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-dubbo</artifactId>
        </dependency>

        <!-- world Common Seata -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-seata</artifactId>
        </dependency>

        <!-- world Common Idempotent -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-idempotent</artifactId>
        </dependency>

        <!-- world Common Tenant -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-tenant</artifactId>
        </dependency>

        <!-- world Common Security -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-security</artifactId>
        </dependency>

        <!-- world Common Job -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-job</artifactId>
        </dependency>

        <!-- world Common Redis -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-redis</artifactId>
        </dependency>

        <!-- Spring Core -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
        </dependency>

        <!-- world Common Tianxin -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-tianxin</artifactId>
        </dependency>
        <!-- world Common Mail -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-mail</artifactId>
        </dependency>

        <!-- world API System -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-api-system</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- PDF处理依赖 -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>kernel</artifactId>
            <version>7.2.4</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>layout</artifactId>
            <version>7.2.4</version>
        </dependency>
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-translation</artifactId>
        </dependency>


    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
