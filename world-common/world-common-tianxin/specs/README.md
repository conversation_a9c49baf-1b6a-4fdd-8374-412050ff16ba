# 天心API调用工具使用说明

## 📋 目录

- [项目概述](#项目概述)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [使用方式](#使用方式)
- [API接口文档](#api接口文档)
- [最佳实践](#最佳实践)
- [故障排除](#故障排除)
- [更新日志](#更新日志)

---

## 🎯 项目概述

### 功能简介

本模块实现了与天心天思数字化API平台的无缝对接，支持以下核心功能：

- **基础资料同步**：商品、客户、供应商等基础信息
- **进销存管理**：采购订单、销售订单、库存管理等
- **生产制造**：生产计划、生产订单、工艺路线等
- **财务管理**：应收应付、成本核算等
- **智能重试**：指数退避算法，自动重试失败请求
- **熔断器保护**：防止级联故障，提升系统稳定性
- **参数验证**：自动验证请求参数，确保数据质量

### 核心特性

- ✅ **高性能**：支持批量同步，异步处理
- ✅ **高可靠**：自动重试机制，熔断器保护
- ✅ **可监控**：完整的日志记录，健康检查
- ✅ **易扩展**：模块化设计，支持新业务接入
- ✅ **安全**：API认证，数据加密传输
- ✅ **轻量级**：基于Hutool工具包，减少依赖

---

## 🚀 快速开始

### 1. 添加依赖

在您的项目 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>org.dromara</groupId>
    <artifactId>world-common-tianxin</artifactId>
    <version>${revision}</version>
</dependency>
```

### 2. 配置文件

在 `application.yml` 中添加配置：

```yaml
tianxin:
  api:
    # API基础URL
    base-url: http://api.amtxts.com
    
    # 应用认证信息
    app-id: your_app_id_here
    app-secret: your_app_secret_here
    
    # 请求超时配置
    timeout: 30000
    retry-times: 3
    retry-interval: 1000
    
    # 熔断器配置
    circuit-breaker-enabled: true
    circuit-breaker-failure-threshold: 5
    circuit-breaker-timeout: 60000
    circuit-breaker-half-open-max-calls: 3
    
    # 连接池配置
    max-connections: 100
    max-connections-per-route: 20
    connection-timeout: 5000
    read-timeout: 30000
    
    # 功能开关
    enabled: true
    log-enabled: true
    cache-enabled: true
```

### 3. 环境变量配置（推荐生产环境）

```bash
# 设置环境变量
export TIANXIN_API_BASE_URL=https://api.amtxts.com
export TIANXIN_API_APP_ID=your_actual_app_id
export TIANXIN_API_APP_SECRET=your_actual_app_secret
```

---

## ⚙️ 配置说明

### 基础配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `base-url` | String | `http://api.amtxts.com` | API基础URL |
| `app-id` | String | - | 应用ID（必填） |
| `app-secret` | String | - | 应用密钥（必填） |
| `timeout` | Integer | `30000` | 请求超时时间（毫秒） |
| `enabled` | Boolean | `true` | 是否启用API调用 |

### 重试配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `retry-times` | Integer | `3` | 重试次数 |
| `retry-interval` | Long | `1000` | 重试间隔（毫秒） |

### 熔断器配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `circuit-breaker-enabled` | Boolean | `true` | 是否启用熔断器 |
| `circuit-breaker-failure-threshold` | Integer | `5` | 失败阈值 |
| `circuit-breaker-timeout` | Long | `60000` | 熔断超时时间（毫秒） |
| `circuit-breaker-half-open-max-calls` | Integer | `3` | 半开状态最大请求数 |

### 连接池配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `max-connections` | Integer | `100` | 最大连接数 |
| `max-connections-per-route` | Integer | `20` | 每个路由的最大连接数 |
| `connection-timeout` | Integer | `5000` | 连接超时时间（毫秒） |
| `read-timeout` | Integer | `30000` | 读取超时时间（毫秒） |

---

## 💻 使用方式

### 1. 基础使用

```java
@RestController
@RequestMapping("/api/tianxin")
public class TianxinController {

    @Autowired
    private TianxinApiClient tianxinApiClient;

    @Autowired
    private TxBaseClient txBaseClient;

    /**
     * 同步商品信息
     */
    @PostMapping("/product/sync")
    public R<Object> syncProduct(@RequestBody ProductRequest request) {
        ApiResult<Object> result = tianxinApiClient.syncProduct(request);
        
        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getErrorMessage());
        }
    }

    /**
     * 查询商品信息
     */
    @GetMapping("/product/{productCode}")
    public R<Object> getProduct(@PathVariable String productCode) {
        ApiResult<Object> result = tianxinApiClient.getProduct(productCode);
        
        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getErrorMessage());
        }
    }

    /**
     * 批量同步商品
     */
    @PostMapping("/product/batch-sync")
    public R<Object> batchSyncProducts(@RequestBody List<ProductRequest> requests) {
        ApiResult<Object> result = tianxinApiClient.batchSyncProducts(requests);
        
        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getErrorMessage());
        }
    }
}
```

### 2. 服务层使用

```java
@Service
public class TianxinSyncService {

    @Autowired
    private TianxinApiClient tianxinApiClient;

    /**
     * 同步商品数据
     */
    public void syncProductData(Product product) {
        try {
            // 构建请求对象
            ProductRequest request = new ProductRequest();
            request.setProductCode(product.getCode());
            request.setProductName(product.getName());
            request.setSpecification(product.getSpecification());
            request.setUnit(product.getUnit());
            request.setCategory(product.getCategory());
            request.setStandardPrice(product.getPrice());
            request.setStatus(1);
            request.setOperationType("ADD");

            // 调用API
            ApiResult<Object> result = tianxinApiClient.syncProduct(request);
            
            if (result.isSuccess()) {
                log.info("商品同步成功: {}", product.getCode());
            } else {
                log.error("商品同步失败: {}, 错误: {}", product.getCode(), result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("商品同步异常: {}", product.getCode(), e);
        }
    }

    /**
     * 批量同步商品数据
     */
    public void batchSyncProductData(List<Product> products) {
        List<ProductRequest> requests = products.stream()
            .map(this::buildProductRequest)
            .collect(Collectors.toList());

        ApiResult<Object> result = tianxinApiClient.batchSyncProducts(requests);
        
        if (result.isSuccess()) {
            log.info("批量商品同步成功，数量: {}", products.size());
        } else {
            log.error("批量商品同步失败: {}", result.getErrorMessage());
        }
    }

    private ProductRequest buildProductRequest(Product product) {
        ProductRequest request = new ProductRequest();
        request.setProductCode(product.getCode());
        request.setProductName(product.getName());
        request.setSpecification(product.getSpecification());
        request.setUnit(product.getUnit());
        request.setCategory(product.getCategory());
        request.setStandardPrice(product.getPrice());
        request.setStatus(1);
        request.setOperationType("ADD");
        return request;
    }
}
```

### 3. 定时任务使用

```java
@Component
public class TianxinSyncTask {

    @Autowired
    private TianxinSyncService tianxinSyncService;

    /**
     * 定时同步商品数据
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void syncProductData() {
        log.info("开始执行商品数据同步任务");
        
        try {
            // 获取需要同步的商品数据
            List<Product> products = getProductsToSync();
            
            if (CollectionUtils.isNotEmpty(products)) {
                tianxinSyncService.batchSyncProductData(products);
            }
            
            log.info("商品数据同步任务执行完成");
        } catch (Exception e) {
            log.error("商品数据同步任务执行异常", e);
        }
    }

    private List<Product> getProductsToSync() {
        // 实现获取需要同步的商品数据逻辑
        return productService.getProductsToSync();
    }
}
```

### 4. 健康检查和监控

```java
@RestController
@RequestMapping("/api/monitor")
public class TianxinMonitorController {

    @Autowired
    private TxBaseClient txBaseClient;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public R<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        
        // 检查API健康状态
        boolean isHealthy = txBaseClient.isHealthy();
        health.put("apiHealthy", isHealthy);
        
        // 获取熔断器信息
        CircuitBreakerInfo circuitBreakerInfo = txBaseClient.getCircuitBreakerInfo();
        if (circuitBreakerInfo != null) {
            health.put("circuitBreaker", circuitBreakerInfo);
        }
        
        return R.ok(health);
    }

    /**
     * 重置熔断器
     */
    @PostMapping("/circuit-breaker/reset")
    public R<String> resetCircuitBreaker() {
        txBaseClient.resetCircuitBreaker();
        return R.ok("熔断器重置成功");
    }
}
```

---

## 📚 API接口文档

### 基础资料管理

#### 商品管理

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncProduct` | `POST /basic/product/sync` | 同步商品信息 |
| `getProduct` | `GET /basic/product/get/{productCode}` | 查询商品信息 |
| `batchSyncProducts` | `POST /basic/product/batch-sync` | 批量同步商品信息 |

#### 客户管理

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncCustomer` | `POST /basic/customer/sync` | 同步客户信息 |
| `getCustomer` | `GET /basic/customer/get/{customerCode}` | 查询客户信息 |
| `batchSyncCustomers` | `POST /basic/customer/batch-sync` | 批量同步客户信息 |

#### 供应商管理

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncSupplier` | `POST /basic/supplier/sync` | 同步供应商信息 |
| `getSupplier` | `GET /basic/supplier/get/{supplierCode}` | 查询供应商信息 |
| `batchSyncSuppliers` | `POST /basic/supplier/batch-sync` | 批量同步供应商信息 |

### 进销存管理

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncInventory` | `POST /inventory/sync` | 同步库存信息 |
| `getInventory` | `GET /inventory/get/{productCode}` | 查询库存信息 |
| `syncSalesOrder` | `POST /sales/order/sync` | 同步销售订单 |
| `getSalesOrder` | `GET /sales/order/get/{orderNo}` | 查询销售订单 |
| `syncPurchaseOrder` | `POST /purchase/order/sync` | 同步采购订单 |
| `getPurchaseOrder` | `GET /purchase/order/get/{orderNo}` | 查询采购订单 |

### 生产制造

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncProductionPlan` | `POST /production/plan/sync` | 同步生产计划 |
| `getProductionPlan` | `GET /production/plan/get/{planNo}` | 查询生产计划 |
| `syncProductionOrder` | `POST /production/order/sync` | 同步生产订单 |
| `getProductionOrder` | `GET /production/order/get/{orderNo}` | 查询生产订单 |

### 财务管理

| 方法 | 接口 | 说明 |
|------|------|------|
| `syncReceivablePayable` | `POST /finance/receivable-payable/sync` | 同步应收应付 |
| `getReceivablePayable` | `GET /finance/receivable-payable/get/{billNo}` | 查询应收应付 |

### 通用方法

| 方法 | 接口 | 说明 |
|------|------|------|
| `healthCheck` | `GET /health` | 健康检查 |
| `getVersion` | `GET /version` | 获取API版本信息 |

---

## 🎯 最佳实践

### 1. 错误处理

```java
public void handleApiCall() {
    try {
        ApiResult<Object> result = tianxinApiClient.syncProduct(request);
        
        if (result.isSuccess()) {
            // 处理成功情况
            log.info("API调用成功: {}", result.getData());
        } else {
            // 处理业务失败情况
            log.error("API调用失败: {}", result.getErrorMessage());
            handleApiError(result.getErrorCode(), result.getErrorMessage());
        }
    } catch (Exception e) {
        // 处理系统异常
        log.error("API调用异常", e);
        handleSystemError(e);
    }
}

private void handleApiError(String errorCode, String errorMessage) {
    switch (errorCode) {
        case "CIRCUIT_BREAKER_OPEN":
            // 熔断器开启，稍后重试
            scheduleRetry();
            break;
        case "VALIDATION_ERROR":
            // 参数验证失败，检查请求数据
            validateRequestData();
            break;
        default:
            // 其他业务错误
            log.error("业务错误: {} - {}", errorCode, errorMessage);
    }
}
```

### 2. 批量处理

```java
public void batchSyncWithChunking(List<Product> products) {
    int batchSize = 50; // 每批处理50个
    
    for (int i = 0; i < products.size(); i += batchSize) {
        int endIndex = Math.min(i + batchSize, products.size());
        List<Product> batch = products.subList(i, endIndex);
        
        try {
            List<ProductRequest> requests = batch.stream()
                .map(this::buildProductRequest)
                .collect(Collectors.toList());
            
            ApiResult<Object> result = tianxinApiClient.batchSyncProducts(requests);
            
            if (result.isSuccess()) {
                log.info("批次 {}-{} 同步成功", i + 1, endIndex);
            } else {
                log.error("批次 {}-{} 同步失败: {}", i + 1, endIndex, result.getErrorMessage());
            }
            
            // 批次间延迟，避免过于频繁的请求
            Thread.sleep(1000);
            
        } catch (Exception e) {
            log.error("批次 {}-{} 同步异常", i + 1, endIndex, e);
        }
    }
}
```

### 3. 异步处理

```java
@Service
public class AsyncTianxinService {

    @Autowired
    private TianxinApiClient tianxinApiClient;

    @Async("tianxinApiExecutor")
    public CompletableFuture<ApiResult<Object>> syncProductAsync(ProductRequest request) {
        try {
            ApiResult<Object> result = tianxinApiClient.syncProduct(request);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步同步商品异常", e);
            return CompletableFuture.completedFuture(
                ApiResult.error("ASYNC_ERROR", "异步同步异常: " + e.getMessage())
            );
        }
    }

    public void batchSyncAsync(List<ProductRequest> requests) {
        List<CompletableFuture<ApiResult<Object>>> futures = requests.stream()
            .map(this::syncProductAsync)
            .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
            .thenRun(() -> {
                log.info("所有异步同步任务完成");
            })
            .exceptionally(throwable -> {
                log.error("异步同步任务异常", throwable);
                return null;
            });
    }
}
```

### 4. 配置优化

```yaml
# 生产环境推荐配置
tianxin:
  api:
    # 基础配置
    base-url: https://api.amtxts.com
    timeout: 30000
    
    # 重试配置（生产环境）
    retry-times: 5
    retry-interval: 2000
    
    # 熔断器配置（生产环境）
    circuit-breaker-enabled: true
    circuit-breaker-failure-threshold: 10
    circuit-breaker-timeout: 120000
    circuit-breaker-half-open-max-calls: 5
    
    # 连接池配置（生产环境）
    max-connections: 200
    max-connections-per-route: 50
    connection-timeout: 10000
    read-timeout: 60000
    
    # 日志配置
    log-enabled: true
```

---

## 🔧 故障排除

### 常见问题

#### 1. 认证失败

**问题**: `AUTH_ERROR` 认证失败

**解决方案**:
- 检查 `app-id` 和 `app-secret` 是否正确
- 确认API基础URL是否正确
- 检查网络连接是否正常

```yaml
tianxin:
  api:
    app-id: your_correct_app_id
    app-secret: your_correct_app_secret
    base-url: https://api.amtxts.com
```

#### 2. 熔断器开启

**问题**: `CIRCUIT_BREAKER_OPEN` 熔断器开启

**解决方案**:
- 检查天心API服务是否正常
- 调整熔断器配置参数
- 手动重置熔断器

```java
// 手动重置熔断器
txBaseClient.resetCircuitBreaker();
```

#### 3. 参数验证失败

**问题**: `VALIDATION_ERROR` 参数验证失败

**解决方案**:
- 检查请求参数是否符合要求
- 确认必填字段是否已填写
- 检查数据类型是否正确

#### 4. 网络超时

**问题**: 请求超时

**解决方案**:
- 增加超时时间配置
- 检查网络连接
- 调整重试配置

```yaml
tianxin:
  api:
    timeout: 60000  # 增加到60秒
    retry-times: 5  # 增加重试次数
    retry-interval: 3000  # 增加重试间隔
```

### 日志分析

#### 启用详细日志

```yaml
logging:
  level:
    org.dromara.common.tianxin: DEBUG
```

#### 关键日志信息

- `天心API熔断器初始化完成` - 熔断器初始化成功
- `发送天心API请求` - 请求发送日志
- `天心API响应` - 响应接收日志
- `天心API请求失败，准备重试` - 重试日志
- `熔断器开启，拒绝请求` - 熔断器开启日志

---

## 📝 更新日志

### v1.0.0 (2025-09-08)

#### 新增功能
- ✅ 实现智能重试机制，支持指数退避算法
- ✅ 实现熔断器模式，防止级联故障
- ✅ 增强错误处理和异常分类
- ✅ 添加请求参数验证功能
- ✅ 优化HTTP连接池和线程池配置
- ✅ 完善Token管理和缓存策略

#### 优化改进
- 🔧 移除复杂的指标监控，保持代码简洁
- 🔧 优化配置属性，提供合理的默认值
- 🔧 完善日志记录，包含请求ID和重试次数
- 🔧 统一错误响应格式

#### 修复问题
- 🐛 修复编译错误和导入问题
- 🐛 清理无用代码和未使用的字段
- 🐛 优化熔断器实现，移除未使用的失败率阈值

---

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件中的错误信息
3. 确认配置参数是否正确
4. 联系技术支持团队

---

**祝您使用愉快！** 🎉
