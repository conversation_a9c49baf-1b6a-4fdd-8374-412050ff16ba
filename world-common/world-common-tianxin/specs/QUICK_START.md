# 天心API调用工具 - 快速入门

## 🚀 5分钟快速上手

### 1. 添加依赖

```xml
<dependency>
    <groupId>org.dromara</groupId>
    <artifactId>world-common-tianxin</artifactId>
    <version>${revision}</version>
</dependency>
```

### 2. 基础配置

```yaml
tianxin:
  api:
    base-url: http://api.amtxts.com
    app-id: your_app_id
    app-secret: your_app_secret
    enabled: true
```

### 3. 代码使用

```java
@RestController
public class TianxinController {

    @Autowired
    private TianxinApiClient tianxinApiClient;

    @PostMapping("/sync-product")
    public R<Object> syncProduct(@RequestBody ProductRequest request) {
        ApiResult<Object> result = tianxinApiClient.syncProduct(request);
        
        if (result.isSuccess()) {
            return R.ok(result.getData());
        } else {
            return R.fail(result.getErrorMessage());
        }
    }
}
```

### 4. 请求对象示例

```java
// 商品同步请求
ProductRequest request = new ProductRequest();
request.setProductCode("P001");
request.setProductName("测试商品");
request.setSpecification("规格型号");
request.setUnit("个");
request.setCategory("电子产品");
request.setStandardPrice(100.00);
request.setStatus(1);
request.setOperationType("ADD");
```

## ✅ 就这么简单！

现在您就可以在其他项目模块中使用天心API调用工具了。详细的使用说明请参考 [README.md](README.md)。

## 🔧 常用配置

```yaml
tianxin:
  api:
    # 基础配置
    base-url: http://api.amtxts.com
    app-id: your_app_id
    app-secret: your_app_secret
    
    # 重试配置
    retry-times: 3
    retry-interval: 1000
    
    # 熔断器配置
    circuit-breaker-enabled: true
    circuit-breaker-failure-threshold: 5
    circuit-breaker-timeout: 60000
    
    # 功能开关
    enabled: true
    log-enabled: true
```

## 📚 更多功能

- **智能重试** - 自动重试失败请求
- **熔断器保护** - 防止级联故障
- **参数验证** - 自动验证请求参数
- **健康检查** - 监控API状态
- **批量处理** - 支持批量同步

详细文档请查看 [README.md](README.md)
