<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.dromara</groupId>
        <artifactId>world-common</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>world-common-tianxin</artifactId>

    <description>
        world-common-tianxin 天心天思API客户端模块
    </description>

    <dependencies>
        <!-- world Common Core -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-core</artifactId>
        </dependency>

        <!-- world Common Web -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-web</artifactId>
        </dependency>

        <!-- world Common Redis -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-redis</artifactId>
        </dependency>

        <!-- world Common Log -->
        <dependency>
            <groupId>org.dromara</groupId>
            <artifactId>world-common-log</artifactId>
        </dependency>

        <!-- Spring Boot Starter Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot Starter Validation -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- Hutool JSON工具 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
        </dependency>

        <!-- Hutool HTTP工具 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

</project>
