/*
package org.dromara.common.tianxin;

import org.dromara.common.tianxin.client.TianxinApiClient;
import org.dromara.common.tianxin.client.TxBaseClient;
import org.dromara.common.tianxin.config.TianxinProperties;
import org.dromara.common.tianxin.domain.response.ApiResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import javax.annotation.Resource;

*/
/**
 * 天心API测试类
 *
 * <AUTHOR>
 * @since 2025-09-08
 *//*

@SpringBootTest
@TestPropertySource(properties = {
    "tianxin.api.base-url=http://api.amtxts.com",
    "tianxin.api.app-id=test_app_id",
    "tianxin.api.app-secret=test_app_secret",
    "tianxin.api.enabled=true",
    "tianxin.api.circuit-breaker-enabled=true",
    "tianxin.api.log-enabled=true"
})
public class TianxinApiTest {

    @Resource
    private TianxinApiClient tianxinApiClient;

    @Resource
    private TxBaseClient txBaseClient;

    @Resource
    private TianxinProperties properties;

    @Test
    public void testProperties() {
        System.out.println("API基础URL: " + properties.getBaseUrl());
        System.out.println("应用ID: " + properties.getAppId());
        System.out.println("熔断器启用: " + properties.getCircuitBreakerEnabled());
        System.out.println("重试次数: " + properties.getRetryTimes());
    }

    @Test
    public void testHealthCheck() {
        // 健康检查测试
        boolean isHealthy = txBaseClient.isHealthy();
        System.out.println("API健康状态: " + isHealthy);
    }

    @Test
    public void testCircuitBreakerInfo() {
        // 熔断器信息测试
        var info = txBaseClient.getCircuitBreakerInfo();
        if (info != null) {
            System.out.println("熔断器状态: " + info.getState());
            System.out.println("失败次数: " + info.getFailureCount());
            System.out.println("成功次数: " + info.getSuccessCount());
        } else {
            System.out.println("熔断器未启用");
        }
    }

    @Test
    public void testApiCall() {
        // 测试API调用（这里只是测试调用流程，不会真正发送请求）
        try {
            ApiResult<Object> result = tianxinApiClient.healthCheck();
            System.out.println("API调用结果: " + result.isSuccess());
            if (!result.isSuccess()) {
                System.out.println("错误信息: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            System.out.println("API调用异常: " + e.getMessage());
        }
    }
}
*/
