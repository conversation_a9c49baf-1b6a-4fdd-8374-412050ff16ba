package org.dromara.common.oss.core;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.validation.constraints.NotNull;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.file.FileUtils;
import org.dromara.common.oss.constant.OssConstant;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.enums.AccessPolicyType;
import org.dromara.common.oss.exception.OssException;
import org.dromara.common.oss.properties.OssProperties;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.async.AsyncResponseTransformer;
import software.amazon.awssdk.core.async.BlockingInputStreamAsyncRequestBody;
import software.amazon.awssdk.core.async.ResponsePublisher;
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.transfer.s3.S3TransferManager;
import software.amazon.awssdk.transfer.s3.model.*;
import software.amazon.awssdk.transfer.s3.progress.LoggingTransferListener;

import java.io.*;
import java.net.URI;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.WritableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.security.MessageDigest;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * S3 存储协议 所有兼容S3协议的云厂商均支持
 * 阿里云 腾讯云 七牛云 minio
 *
 * <AUTHOR>
 */
public class OssClient {

    /**
     * 服务商
     */
    private final String configKey;

    /**
     * 配置属性
     */
    private final OssProperties properties;

    /**
     * Amazon S3 异步客户端
     */
    private final S3AsyncClient client;

    /**
     * 用于管理 S3 数据传输的高级工具
     */
    private final S3TransferManager transferManager;

    /**
     * AWS S3 预签名 URL 的生成器
     */
    private final S3Presigner presigner;

    /**
     * 分片大小（5MB）
     */
    private static final long PART_SIZE = 5 * 1024 * 1024;

    /**
     * 构造方法
     *
     * @param configKey     配置键
     * @param ossProperties Oss配置属性
     */
    public OssClient(String configKey, OssProperties ossProperties) {
        this.configKey = configKey;
        this.properties = ossProperties;
        try {
            // 创建 AWS 认证信息
            StaticCredentialsProvider credentialsProvider = StaticCredentialsProvider.create(
                AwsBasicCredentials.create(properties.getAccessKey(), properties.getSecretKey()));

            // MinIO 使用 HTTPS 限制使用域名访问，站点填域名。需要启用路径样式访问
            boolean isStyle = !StringUtils.containsAny(properties.getEndpoint(), OssConstant.CLOUD_SERVICE);

            // 创建AWS基于 Netty 的 S3 客户端
            this.client = S3AsyncClient.builder()
                .credentialsProvider(credentialsProvider)
                .endpointOverride(URI.create(getEndpoint()))
                .region(of())
                .forcePathStyle(isStyle)
                .httpClient(NettyNioAsyncHttpClient.builder()
                    .connectionTimeout(Duration.ofSeconds(60)).build())
                .build();

            // AWS基于 CRT 的 S3 AsyncClient 实例用作 S3 传输管理器的底层客户端
            this.transferManager = S3TransferManager.builder().s3Client(this.client).build();

            // 创建 S3 配置对象
            S3Configuration config = S3Configuration.builder().chunkedEncodingEnabled(false)
                .pathStyleAccessEnabled(isStyle).build();

            // 创建 预签名 URL 的生成器 实例，用于生成 S3 预签名 URL
            this.presigner = S3Presigner.builder()
                .region(of())
                .credentialsProvider(credentialsProvider)
                .endpointOverride(URI.create(getDomain()))
                .serviceConfiguration(config)
                .build();

        } catch (Exception e) {
            if (e instanceof OssException) {
                throw e;
            }
            throw new OssException("配置错误! 请检查系统配置:[" + e.getMessage() + "]");
        }
    }

    /**
     * 上传文件到 Amazon S3，并返回上传结果
     *
     * @param filePath    本地文件路径
     * @param key         在 Amazon S3 中的对象键
     * @param md5Digest   本地文件的 MD5 哈希值（可选）
     * @param contentType 文件内容类型
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    public UploadResult upload(Path filePath, String key, String md5Digest, String contentType) {
        try {
            // 构建上传请求对象
            FileUpload fileUpload = transferManager.uploadFile(
                x -> x.putObjectRequest(
                        y -> y.bucket(properties.getBucketName())
                            .key(key)
                            .contentMD5(StringUtils.isNotEmpty(md5Digest) ? md5Digest : null)
                            .contentType(contentType)
                            // 用于设置对象的访问控制列表（ACL）。不同云厂商对ACL的支持和实现方式有所不同，
                            // 因此根据具体的云服务提供商，你可能需要进行不同的配置（自行开启，阿里云有acl权限配置，腾讯云没有acl权限配置）
                            // .acl(getAccessPolicy().getObjectCannedACL())
                            .build())
                    .addTransferListener(LoggingTransferListener.create())
                    .source(filePath).build());

            // 等待上传完成并获取上传结果
            CompletedFileUpload uploadResult = fileUpload.completionFuture().join();
            String eTag = uploadResult.response().eTag();

            // 提取上传结果中的 ETag，并构建一个自定义的 UploadResult 对象
            return UploadResult.builder().url(getUrl() + StringUtils.SLASH + key).filename(key).eTag(eTag).build();
        } catch (Exception e) {
            // 捕获异常并抛出自定义异常
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        } finally {
            // 无论上传是否成功，最终都会删除临时文件
            FileUtils.del(filePath);
        }
    }

    /**
     * 上传文件到指定的存储桶中
     *
     * @param inputStream 文件输入流
     * @param key         文件在存储桶中的键名
     * @param length      文件长度
     * @param contentType 文件内容类型
     * @param bucketName  存储桶名称
     * @return 上传结果，包含文件的 URL、文件名和 ETag
     * @throws OssException 上传文件失败时抛出
     */
    public UploadResult upload(InputStream inputStream, String key, Long length, String contentType, String bucketName)
        throws IOException {
        ByteArrayOutputStream buffer = null;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            // 复制输入流以避免后续操作消耗原始流
            buffer = getByteArrayOutputStream(inputStream);
            byteArrayInputStream = new ByteArrayInputStream(buffer.toByteArray());
            // 重置流位置
            byteArrayInputStream.reset();

            createBucket(bucketName);
            // 创建异步请求体（length如果为空会报错）
            BlockingInputStreamAsyncRequestBody body = BlockingInputStreamAsyncRequestBody.builder()
                .contentLength(length).subscribeTimeout(Duration.ofSeconds(120)).build();

            // 使用 transferManager 进行上传
            Upload upload = transferManager.upload(x -> x.requestBody(body).addTransferListener(LoggingTransferListener.create())
                .putObjectRequest(y -> y.bucket(bucketName).key(key).contentType(contentType)
                    // 用于设置对象的访问控制列表（ACL）。不同云厂商对ACL的支持和实现方式有所不同，
                    // 因此根据具体的云服务提供商，你可能需要进行不同的配置（自行开启，阿里云有acl权限配置，腾讯云没有acl权限配置）
                    // .acl(getAccessPolicy().getObjectCannedACL())
                    .build())
                .build());

            // 将输入流写入请求体
            body.writeInputStream(byteArrayInputStream);
            // 等待文件上传操作完成
            CompletedUpload uploadResult = upload.completionFuture().join();
            String eTag = uploadResult.response().eTag();
            return UploadResult.builder().url(getUrl(bucketName) + StringUtils.SLASH + key)
                .filename(key).eTag(eTag).build();
        } catch (Exception e) {
            throw new OssException("上传文件失败:[" + e.getMessage() + "]");
        } finally {
            // 关闭资源
            IoUtil.close(buffer);
            IoUtil.close(byteArrayInputStream);
        }
    }

    /**
     * 上传 InputStream 到 Amazon S3
     *
     * @param inputStream 要上传的输入流
     * @param key         在 Amazon S3 中的对象键
     * @param length      输入流的长度
     * @param contentType 文件内容类型
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    public UploadResult upload(InputStream inputStream, String key, Long length, String contentType) {
        // 如果输入流不是 ByteArrayInputStream，则将其读取为字节数组再创建 ByteArrayInputStream
        if (!(inputStream instanceof ByteArrayInputStream)) {
            inputStream = new ByteArrayInputStream(IoUtil.readBytes(inputStream));
        }
        try {
            // 创建异步请求体（length如果为空会报错）
            BlockingInputStreamAsyncRequestBody body = BlockingInputStreamAsyncRequestBody.builder()
                .contentLength(length)
                .subscribeTimeout(Duration.ofSeconds(120))
                .build();

            // 使用 transferManager 进行上传
            Upload upload = transferManager.upload(
                x -> x.requestBody(body).addTransferListener(LoggingTransferListener.create())
                    .putObjectRequest(
                        y -> y.bucket(properties.getBucketName())
                            .key(key)
                            .contentType(contentType)
                            // 用于设置对象的访问控制列表（ACL）。不同云厂商对ACL的支持和实现方式有所不同，
                            // 因此根据具体的云服务提供商，你可能需要进行不同的配置（自行开启，阿里云有acl权限配置，腾讯云没有acl权限配置）
                            // .acl(getAccessPolicy().getObjectCannedACL())
                            .build())
                    .build());

            // 将输入流写入请求体
            body.writeInputStream(inputStream);

            // 等待文件上传操作完成
            CompletedUpload uploadResult = upload.completionFuture().join();
            String eTag = uploadResult.response().eTag();

            // 提取上传结果中的 ETag，并构建一个自定义的 UploadResult 对象
            return UploadResult.builder().url(getUrl() + StringUtils.SLASH + key).filename(key).eTag(eTag).build();
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 下载文件从 Amazon S3 到临时目录
     *
     * @param path 文件在 Amazon S3 中的对象键
     * @return 下载后的文件在本地的临时路径
     * @throws OssException 如果下载失败，抛出自定义异常
     */
    public Path fileDownload(String path) {
        // 构建临时文件
        Path tempFilePath = FileUtils.createTempFile().toPath();
        // 使用 S3TransferManager 下载文件
        FileDownload downloadFile = transferManager.downloadFile(
            x -> x.getObjectRequest(
                    y -> y.bucket(properties.getBucketName())
                        .key(removeBaseUrl(path))
                        .build())
                .addTransferListener(LoggingTransferListener.create())
                .destination(tempFilePath)
                .build());
        try {
            // 等待文件下载操作完成
            downloadFile.completionFuture().join();
            return tempFilePath;
        } catch (Exception e) {
            // 如果下载失败，删除临时文件
            FileUtils.del(tempFilePath);
            throw new OssException("文件下载失败，错误信息:[" + e.getMessage() + "]");
        }
    }

    public Path fileDownload(String path, String bucketName) {
        // 构建临时文件
        Path tempFilePath = FileUtils.createTempFile().toPath();
        // 使用 S3TransferManager 下载文件
        createBucket(bucketName);
        FileDownload downloadFile = transferManager
            .downloadFile(x -> x.getObjectRequest(y -> y.bucket(bucketName).key(removeBaseUrl(path)).build())
                .addTransferListener(LoggingTransferListener.create()).destination(tempFilePath).build());
        try {
            // 等待文件下载操作完成
            downloadFile.completionFuture().join();
        } catch (Exception e) {
            throw new OssException("文件下载失败，错误信息:[" + e.getMessage() + "]");
        }

        return tempFilePath;
    }

    /**
     * 下载文件从 Amazon S3 到 输出流
     *
     * @param key      文件在 Amazon S3 中的对象键
     * @param out      输出流
     * @param consumer 自定义处理逻辑
     * @throws OssException 如果下载失败，抛出自定义异常
     */
    public void download(String key, OutputStream out, Consumer<Long> consumer) {
        try {
            this.download(key, consumer).writeTo(out);
        } catch (Exception e) {
            throw new OssException("文件下载失败，错误信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 下载文件从 Amazon S3 到 输出流
     *
     * @param key                   文件在 Amazon S3 中的对象键
     * @param contentLengthConsumer 文件大小消费者函数
     * @return 写出订阅器
     * @throws OssException 如果下载失败，抛出自定义异常
     */
    public WriteOutSubscriber<OutputStream> download(String key, Consumer<Long> contentLengthConsumer) {
        try {
            // 构建下载请求
            DownloadRequest<ResponsePublisher<GetObjectResponse>> publisherDownloadRequest = DownloadRequest.builder()
                // 文件对象
                .getObjectRequest(y -> y.bucket(properties.getBucketName())
                    .key(key)
                    .build())
                .addTransferListener(LoggingTransferListener.create())
                // 使用发布订阅转换器
                .responseTransformer(AsyncResponseTransformer.toPublisher())
                .build();

            // 使用 S3TransferManager 下载文件
            Download<ResponsePublisher<GetObjectResponse>> publisherDownload = transferManager.download(publisherDownloadRequest);
            // 获取下载发布订阅转换器
            ResponsePublisher<GetObjectResponse> publisher = publisherDownload.completionFuture().join().result();
            // 执行文件大小消费者函数
            Optional.ofNullable(contentLengthConsumer)
                .ifPresent(lengthConsumer -> lengthConsumer.accept(publisher.response().contentLength()));

            // 构建写出订阅器对象
            return out -> {
                // 创建可写入的字节通道
                try (WritableByteChannel channel = Channels.newChannel(out)) {
                    // 订阅数据
                    publisher.subscribe(byteBuffer -> {
                        while (byteBuffer.hasRemaining()) {
                            try {
                                channel.write(byteBuffer);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    }).join();
                }
            };
        } catch (Exception e) {
            throw new OssException("文件下载失败，错误信息:[" + e.getMessage() + "]");
        }
    }


    /**
     * 删除云存储服务中指定路径下文件
     *
     * @param path 指定路径
     */
    public void delete(String path) {
        try {
            client.deleteObject(
                x -> x.bucket(properties.getBucketName())
                    .key(removeBaseUrl(path))
                    .build());
        } catch (Exception e) {
            throw new OssException("删除文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 删除指定存储桶中的文件
     *
     * @param path       文件路径
     * @param bucketName 存储桶名称
     */
    public void delete(String path, String bucketName) {
        try {
            createBucket(bucketName);
            client.deleteObject(
                x -> x.bucket(bucketName)
                    .key(removeBaseUrl(path))
                    .build());
        } catch (Exception e) {
            throw new OssException("删除文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 批量删除文件
     *
     * @param paths 文件路径列表
     */
    public void deleteBatch(List<String> paths) {
        try {
            List<ObjectIdentifier> objects = paths.stream()
                .map(path -> ObjectIdentifier.builder().key(removeBaseUrl(path)).build())
                .collect(Collectors.toList());

            client.deleteObjects(
                x -> x.bucket(properties.getBucketName())
                    .delete(Delete.builder().objects(objects).build())
                    .build());
        } catch (Exception e) {
            throw new OssException("批量删除文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 批量删除指定存储桶中的文件
     *
     * @param paths      文件路径列表
     * @param bucketName 存储桶名称
     */
    public void deleteBatch(List<String> paths, String bucketName) {
        try {
            createBucket(bucketName);
            List<ObjectIdentifier> objects = paths.stream()
                .map(path -> ObjectIdentifier.builder().key(removeBaseUrl(path)).build())
                .collect(Collectors.toList());

            client.deleteObjects(
                x -> x.bucket(bucketName)
                    .delete(Delete.builder().objects(objects).build())
                    .build());
        } catch (Exception e) {
            throw new OssException("批量删除文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 删除指定前缀的所有文件
     *
     * @param prefix 文件前缀
     */
    public void deleteByPrefix(String prefix) {
        try {
            ListObjectsV2Response response = client.listObjectsV2(
                    x -> x.bucket(properties.getBucketName())
                        .prefix(prefix)
                        .build())
                .join();

            List<ObjectIdentifier> objects = response.contents().stream()
                .map(s3Object -> ObjectIdentifier.builder().key(s3Object.key()).build())
                .collect(Collectors.toList());

            if (!objects.isEmpty()) {
                client.deleteObjects(
                    x -> x.bucket(properties.getBucketName())
                        .delete(Delete.builder().objects(objects).build())
                        .build());
            }
        } catch (Exception e) {
            throw new OssException("删除前缀文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 删除指定存储桶中指定前缀的所有文件
     *
     * @param prefix     文件前缀
     * @param bucketName 存储桶名称
     */
    public void deleteByPrefix(String prefix, String bucketName) {
        try {
            createBucket(bucketName);
            ListObjectsV2Response response = client.listObjectsV2(
                    x -> x.bucket(bucketName)
                        .prefix(prefix)
                        .build())
                .join();

            List<ObjectIdentifier> objects = response.contents().stream()
                .map(s3Object -> ObjectIdentifier.builder().key(s3Object.key()).build())
                .collect(Collectors.toList());

            if (!objects.isEmpty()) {
                client.deleteObjects(
                    x -> x.bucket(bucketName)
                        .delete(Delete.builder().objects(objects).build())
                        .build());
            }
        } catch (Exception e) {
            throw new OssException("删除前缀文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    /**
     * 获取私有URL链接
     *
     * @param objectKey   对象KEY
     * @param expiredTime 链接授权到期时间
     */
    public String getPrivateUrl(String objectKey, Duration expiredTime) {
        // 使用 AWS S3 预签名 URL 的生成器 获取对象的预签名 URL
        URL url = presigner.presignGetObject(
                x -> x.signatureDuration(expiredTime)
                    .getObjectRequest(
                        y -> y.bucket(properties.getBucketName())
                            .key(objectKey)
                            .build())
                    .build())
            .url();
        return url.toString();
    }

    /**
     * 获取1分钟有效期的临时访问链接
     *
     * @param objectKey 对象KEY
     * @return 临时访问链接
     */
    public String getOneMinuteUrl(String objectKey) {
        return getPrivateUrl(objectKey, Duration.ofMinutes(1));
    }

    /**
     * 获取1分钟有效期的临时访问链接（指定存储桶）
     *
     * @param objectKey  对象KEY
     * @param bucketName 存储桶名称
     * @return 临时访问链接
     */
    public String getOneMinuteUrl(String objectKey, String bucketName) {
        try {
            createBucket(bucketName);
            // 使用 AWS S3 预签名 URL 的生成器 获取对象的预签名 URL
            URL url = presigner.presignGetObject(
                    x -> x.signatureDuration(Duration.ofMinutes(1))
                        .getObjectRequest(
                            y -> y.bucket(bucketName)
                                .key(objectKey)
                                .build())
                        .build())
                .url();
            return url.toString();
        } catch (Exception e) {
            throw new OssException("获取临时访问链接失败:[" + e.getMessage() + "]");
        }
    }

    /**
     * 获取指定存储桶的私有URL链接
     *
     * @param objectKey   对象KEY
     * @param bucketName  存储桶名称
     * @param expiredTime 链接授权到期时间
     * @return 临时访问链接
     */
    public String getPrivateUrl(String objectKey, String bucketName, Duration expiredTime) {
        try {
            createBucket(bucketName);
            // 使用 AWS S3 预签名 URL 的生成器 获取对象的预签名 URL
            URL url = presigner.presignGetObject(
                    x -> x.signatureDuration(expiredTime)
                        .getObjectRequest(
                            y -> y.bucket(bucketName)
                                .key(objectKey)
                                .build())
                        .build())
                .url();
            return url.toString();
        } catch (Exception e) {
            throw new OssException("获取临时访问链接失败:[" + e.getMessage() + "]");
        }
    }

    /**
     * 上传 byte[] 数据到 Amazon S3，使用指定的后缀构造对象键。
     *
     * @param data   要上传的 byte[] 数据
     * @param suffix 对象键的后缀
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    public UploadResult uploadSuffix(byte[] data, String suffix, String contentType) {
        return upload(new ByteArrayInputStream(data), getPath(properties.getPrefix(), suffix),
            Long.valueOf(data.length), contentType);
    }

    /**
     * 文件上传指定路径
     *
     * @param data
     * @param suffix
     * @param fileName    指定路径和名称
     * @param bucketName
     * @param contentType
     * @return
     * @throws IOException
     */
    public UploadResult uploadSuffix(byte[] data, String suffix, String fileName, String bucketName, String contentType)
        throws IOException {
        return upload(new ByteArrayInputStream(data), fileName, Long.valueOf(data.length), contentType, bucketName);
    }

    /**
     * 上传 InputStream 到 Amazon S3，使用指定的后缀构造对象键。
     *
     * @param inputStream 要上传的输入流
     * @param suffix      对象键的后缀
     * @param length      输入流的长度
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    public UploadResult uploadSuffix(InputStream inputStream, String suffix, Long length, String contentType) {
        return upload(inputStream, getPath(properties.getPrefix(), suffix), length, contentType);
    }

    /**
     * 上传文件到 Amazon S3，使用指定的后缀构造对象键
     *
     * @param file   要上传的文件
     * @param suffix 对象键的后缀
     * @return UploadResult 包含上传后的文件信息
     * @throws OssException 如果上传失败，抛出自定义异常
     */
    public UploadResult uploadSuffix(File file, String suffix) {
        return upload(file.toPath(), getPath(properties.getPrefix(), suffix), null, FileUtils.getMimeType(suffix));
    }

    /**
     * 获取文件输入流
     *
     * @param path 完整文件路径
     * @return 输入流
     */
    public InputStream getObjectContent(String path) throws IOException {
        Path tempFilePath = null;
        InputStream inputStream = null;
        try {
            // 下载文件到临时目录
            tempFilePath = fileDownload(path);
            // 创建输入流
            inputStream = Files.newInputStream(tempFilePath);
            // 返回对象内容的输入流
            return inputStream;
        } catch (Exception e) {
            // 关闭资源
            IoUtil.close(inputStream);
            throw e;
        } finally {
            // 删除临时文件
            if (tempFilePath != null) {
                FileUtils.del(tempFilePath);
            }
        }
    }

    public InputStream getObjectContent(String path, String bucketName) throws IOException {
        Path tempFilePath = null;
        InputStream inputStream = null;
        try {
            // 下载文件到临时目录
            tempFilePath = fileDownload(path, bucketName);
            // 创建输入流
            inputStream = Files.newInputStream(tempFilePath);
            // 返回对象内容的输入流
            return inputStream;
        } catch (Exception e) {
            // 关闭资源
            IoUtil.close(inputStream);
            throw e;
        } finally {
            // 删除临时文件
            if (tempFilePath != null) {
                FileUtils.del(tempFilePath);
            }
        }
    }

    /**
     * 获取 S3 客户端的终端点 URL
     *
     * @return 终端点 URL
     */
    public String getEndpoint() {
        // 根据配置文件中的是否使用 HTTPS，设置协议头部
        String header = getIsHttps();
        // 拼接协议头部和终端点，得到完整的终端点 URL
        return header + properties.getEndpoint();
    }

    /**
     * 获取 S3 客户端的终端点 URL（自定义域名）
     *
     * @return 终端点 URL
     */
    public String getDomain() {
        // 从配置中获取域名、终端点、是否使用 HTTPS 等信息
        String domain = properties.getDomain();
        String endpoint = properties.getEndpoint();
        String header = getIsHttps();

        // 如果是云服务商，直接返回域名或终端点
        if (StringUtils.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return StringUtils.isNotEmpty(domain) ? header + domain : header + endpoint;
        }

        // 如果是 MinIO，处理域名并返回
        if (StringUtils.isNotEmpty(domain)) {
            return domain.startsWith(Constants.HTTPS) || domain.startsWith(Constants.HTTP) ? domain : header + domain;
        }

        // 返回终端点
        return header + endpoint;
    }

    /**
     * 根据传入的 region 参数返回相应的 AWS 区域
     * 如果 region 参数非空，使用 Region.of 方法创建并返回对应的 AWS 区域对象
     * 如果 region 参数为空，返回一个默认的 AWS 区域（例如，us-east-1），作为广泛支持的区域
     *
     * @return 对应的 AWS 区域对象，或者默认的广泛支持的区域（us-east-1）
     */
    public Region of() {
        // AWS 区域字符串
        String region = properties.getRegion();
        // 如果 region 参数非空，使用 Region.of 方法创建对应的 AWS 区域对象，否则返回默认区域
        return StringUtils.isNotEmpty(region) ? Region.of(region) : Region.US_EAST_1;
    }

    /**
     * 获取云存储服务的URL
     *
     * @return 文件路径
     */
    public String getUrl() {
        String domain = properties.getDomain();
        String endpoint = properties.getEndpoint();
        String header = getIsHttps();
        // 云服务商直接返回
        if (StringUtils.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return header + (StringUtils.isNotEmpty(domain) ? domain : properties.getBucketName() + "." + endpoint);
        }
        // MinIO 单独处理
        if (StringUtils.isNotEmpty(domain)) {
            // 如果 domain 以 "https://" 或 "http://" 开头
            return (domain.startsWith(Constants.HTTPS) || domain.startsWith(Constants.HTTP))
                ? domain + StringUtils.SLASH + properties.getBucketName()
                : header + domain + StringUtils.SLASH + properties.getBucketName();
        }
        return header + endpoint + StringUtils.SLASH + properties.getBucketName();
    }

    public String getUrl(String bucketName) {
        String domain = properties.getDomain();
        String endpoint = properties.getEndpoint();
        String header = getIsHttps();
        // 云服务商直接返回
        if (StrUtil.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return header + (StrUtil.isNotEmpty(domain) ? domain : bucketName + "." + endpoint);
        }
        // MinIO 单独处理
        if (StrUtil.isNotEmpty(domain)) {
            // 如果 domain 以 "https://" 或 "http://" 开头
            return (domain.startsWith(Constants.HTTPS) || domain.startsWith(Constants.HTTP))
                ? domain + StrUtil.SLASH + properties.getBucketName()
                : header + domain + StrUtil.SLASH + bucketName;
        }
        return header + endpoint + StrUtil.SLASH + bucketName;
    }

    /**
     * 生成一个符合特定规则的、唯一的文件路径。通过使用日期、UUID、前缀和后缀等元素的组合，确保了文件路径的独一无二性
     *
     * @param prefix 前缀
     * @param suffix 后缀
     * @return 文件路径
     */
    public String getPath(String prefix, String suffix) {
        // 生成uuid
        String uuid = IdUtil.fastSimpleUUID();
        // 生成日期路径
        String datePath = DateUtils.datePath();
        // 拼接路径
        String path = StringUtils.isNotEmpty(prefix) ? prefix + StringUtils.SLASH + datePath + StringUtils.SLASH + uuid
            : datePath + StringUtils.SLASH + uuid;
        return path + suffix;
    }

    /**
     * 移除路径中的基础URL部分，得到相对路径
     *
     * @param path 完整的路径，包括基础URL和相对路径
     * @return 去除基础URL后的相对路径
     */
    public String removeBaseUrl(String path) {
        return path.replace(getUrl() + StringUtils.SLASH, "");
    }

    /**
     * 服务商
     */
    public String getConfigKey() {
        return configKey;
    }

    /**
     * 获取是否使用 HTTPS 的配置，并返回相应的协议头部。
     *
     * @return 协议头部，根据是否使用 HTTPS 返回 "https://" 或 "http://"
     */
    public String getIsHttps() {
        return OssConstant.IS_HTTPS.equals(properties.getIsHttps()) ? Constants.HTTPS : Constants.HTTP;
    }

    /**
     * 检查配置是否相同
     */
    public boolean checkPropertiesSame(OssProperties properties) {
        return this.properties.equals(properties);
    }

    /**
     * 获取当前桶权限类型
     *
     * @return 当前桶权限类型code
     */
    public AccessPolicyType getAccessPolicy() {
        return AccessPolicyType.getByType(properties.getAccessPolicy());
    }

    @Async()
    @NotNull
    ByteArrayOutputStream getByteArrayOutputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        int nRead;
        byte[] data = new byte[1024];
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        buffer.flush();
        return buffer;
    }

    @Async()
    public void createBucket(String bucketName) {
        try {
            // 尝试获取存储桶的信息
            client.headBucket(x -> x.bucket(bucketName).build()).join();
        } catch (Exception ex) {
            if (ex.getCause() instanceof NoSuchBucketException) {
                try {
                    // 存储桶不存在，尝试创建存储桶
                    client.createBucket(x -> x.bucket(bucketName)).join();
                    // 设置存储桶的访问策略（Bucket Policy）
                    client
                        .putBucketPolicy(
                            x -> x.bucket(bucketName).policy(getAccessPolicy().getType()))
                        .join();
                } catch (S3Exception e) {
                    // 存储桶创建或策略设置失败
                    throw new OssException("创建Bucket失败, 请核对配置信息:[" + e.getMessage() + "]");
                }
            } else {
                throw new OssException("判断Bucket是否存在失败，请核对配置信息:[" + ex.getMessage() + "]");
            }
        }
    }

    /**
     * 根据字节数组和指定的后缀、路径、存储桶名称、内容类型和MD5校验值上传文件。
     *
     * @param data        文件的字节数组。
     * @param suffix      文件的后缀（未在方法中使用，可能用于构建文件名）。
     * @param path        存储桶中文件的路径。
     * @param bucketName  存储桶的名称。
     * @param contentType 文件的内容类型（MIME类型）。
     * @param md5Digest   文件的MD5校验值。
     * @return 包含上传结果信息的 UploadResult 对象。
     * @throws IOException 如果在创建输入流或调用上传方法时发生I/O错误。
     */
    public UploadResult uploadSuffix(byte[] data, String suffix, String path, String bucketName, String contentType,
                                     String md5Digest) throws IOException {
        return upload(new ByteArrayInputStream(data), path, Long.valueOf(data.length), contentType, bucketName,
            md5Digest);
    }

    /**
     * 将文件上传到指定的存储桶中。
     *
     * @param inputStream 文件的输入流。
     * @param key         存储桶中文件的键（路径）。
     * @param length      文件的长度。
     * @param contentType 文件的内容类型（MIME类型）。
     * @param bucketName  存储桶的名称。
     * @param md5Digest   文件的MD5校验值。
     * @return 包含上传结果信息的 UploadResult 对象。
     * @throws IOException 如果在复制输入流或写入请求体时发生I/O错误。
     */
    private UploadResult upload(ByteArrayInputStream inputStream, String key, Long length, String contentType,
                                String bucketName, String md5Digest) throws IOException {
        ByteArrayOutputStream buffer = null;
        ByteArrayInputStream byteArrayInputStream = null;
        try {
            // 复制输入流以避免后续操作消耗原始流
            buffer = getByteArrayOutputStream(inputStream);
            byteArrayInputStream = new ByteArrayInputStream(buffer.toByteArray());
            // 重置流位置
            byteArrayInputStream.reset();

            createBucket(bucketName);
            // 创建异步请求体（length如果为空会报错）
            BlockingInputStreamAsyncRequestBody body = BlockingInputStreamAsyncRequestBody.builder()
                .contentLength(length).subscribeTimeout(Duration.ofSeconds(120)).build();

            // 使用 transferManager 进行上传
            Upload upload = transferManager.upload(x -> x.requestBody(body).addTransferListener(LoggingTransferListener.create())
                .putObjectRequest(y -> y.bucket(bucketName).key(key).contentType(contentType)
                    // 用于设置对象的访问控制列表（ACL）。不同云厂商对ACL的支持和实现方式有所不同，
                    // 因此根据具体的云服务提供商，你可能需要进行不同的配置（自行开启，阿里云有acl权限配置，腾讯云没有acl权限配置）
                    // .acl(getAccessPolicy().getObjectCannedACL())
                    .build())
                .build());

            // 将输入流写入请求体
            body.writeInputStream(byteArrayInputStream);
            // 等待文件上传操作完成
            CompletedUpload uploadResult = upload.completionFuture().join();
            String eTag = uploadResult.response().eTag();
            UploadResult buildResult = UploadResult.builder().url(getUrl(bucketName) + StringUtils.SLASH + key)
                .filename(key).eTag(eTag).build();
            return buildResult;
        } catch (Exception e) {
            throw new OssException("上传文件失败:[" + e.getMessage() + "]");
        } finally {
            // 关闭资源
            IoUtil.close(buffer);
            IoUtil.close(byteArrayInputStream);
        }
    }

    /**
     * 大文件分片上传
     *
     * @param filePath    本地文件路径
     * @param key         对象键
     * @param contentType 内容类型
     * @return 上传结果
     */
    public UploadResult multipartUpload(Path filePath, String key, String contentType) {
        try {
            // 获取文件大小
            long fileSize = Files.size(filePath);

            // 如果文件小于分片大小，直接上传
            if (fileSize <= PART_SIZE) {
                return upload(filePath, key, null, contentType);
            }

            // 初始化分片上传
            CreateMultipartUploadResponse initResponse = client.createMultipartUpload(
                    CreateMultipartUploadRequest.builder()
                        .bucket(properties.getBucketName())
                        .key(key)
                        .contentType(contentType)
                        .build())
                .join();

            String uploadId = initResponse.uploadId();
            List<CompletedPart> completedParts = new ArrayList<>();
            AtomicInteger partNumber = new AtomicInteger(1);

            try {
                // 读取文件并分片上传
                byte[] buffer = new byte[(int) PART_SIZE];
                try (InputStream inputStream = Files.newInputStream(filePath)) {
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        // 上传分片
                        byte[] partData = new byte[bytesRead];
                        System.arraycopy(buffer, 0, partData, 0, bytesRead);
                        UploadPartResponse uploadPartResponse = client.uploadPart(
                            UploadPartRequest.builder()
                                .bucket(properties.getBucketName())
                                .key(key)
                                .uploadId(uploadId)
                                .partNumber(partNumber.get())
                                .contentLength((long) bytesRead)
                                .build(),
                            AsyncRequestBody.fromBytes(partData)).join();

                        // 记录已上传的分片
                        completedParts.add(CompletedPart.builder()
                            .partNumber(partNumber.get())
                            .eTag(uploadPartResponse.eTag())
                            .build());

                        partNumber.incrementAndGet();
                    }
                }

                // 完成分片上传
                CompleteMultipartUploadResponse completeResponse = client.completeMultipartUpload(
                        CompleteMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .multipartUpload(CompletedMultipartUpload.builder()
                                .parts(completedParts)
                                .build())
                            .build())
                    .join();

                // 返回上传结果
                return UploadResult.builder()
                    .url(getUrl() + StringUtils.SLASH + key)
                    .filename(key)
                    .eTag(completeResponse.eTag())
                    .build();

            } catch (Exception e) {
                // 发生错误时中止分片上传
                client.abortMultipartUpload(
                        AbortMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .build())
                    .join();
                throw new OssException("分片上传失败: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new OssException("分片上传失败: " + e.getMessage());
        } finally {
            // 删除临时文件
            FileUtils.del(filePath);
        }
    }

    /**
     * 获取未完成的分片上传任务
     *
     * @return 未完成的分片上传任务列表
     */
    public List<MultipartUpload> listMultipartUploads() {
        try {
            ListMultipartUploadsResponse response = client.listMultipartUploads(
                    ListMultipartUploadsRequest.builder()
                        .bucket(properties.getBucketName())
                        .build())
                .join();
            return response.uploads();
        } catch (Exception e) {
            throw new OssException("获取未完成的分片上传任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定上传ID的分片列表
     *
     * @param key      对象键
     * @param uploadId 上传ID
     * @return 已上传的分片列表
     */
    public List<Part> listParts(String key, String uploadId) {
        try {
            ListPartsResponse response = client.listParts(
                    ListPartsRequest.builder()
                        .bucket(properties.getBucketName())
                        .key(key)
                        .uploadId(uploadId)
                        .build())
                .join();
            return response.parts();
        } catch (Exception e) {
            throw new OssException("获取分片列表失败: " + e.getMessage());
        }
    }

    /**
     * 中止分片上传
     *
     * @param key      对象键
     * @param uploadId 上传ID
     */
    public void abortMultipartUpload(String key, String uploadId) {
        try {
            client.abortMultipartUpload(
                    AbortMultipartUploadRequest.builder()
                        .bucket(properties.getBucketName())
                        .key(key)
                        .uploadId(uploadId)
                        .build())
                .join();
        } catch (Exception e) {
            throw new OssException("中止分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 大文件分片上传（带MD5校验）
     *
     * @param data        文件的字节数组
     * @param key         对象键
     * @param contentType 内容类型
     * @param md5Digest   MD5校验值
     * @return 上传结果
     */
    public UploadResult multipartUploadWithMd5(byte[] data, String key, String contentType, String md5Digest) {
        try {
            // 如果数据小于分片大小，直接上传
            if (data.length <= PART_SIZE) {
                return upload(new ByteArrayInputStream(data), key, Long.valueOf(data.length), contentType, md5Digest);
            }

            // 初始化分片上传
            CreateMultipartUploadResponse initResponse = client.createMultipartUpload(
                    CreateMultipartUploadRequest.builder()
                        .bucket(properties.getBucketName())
                        .key(key)
                        .contentType(contentType)
                        .build())
                .join();

            String uploadId = initResponse.uploadId();
            List<CompletedPart> completedParts = new ArrayList<>();
            AtomicInteger partNumber = new AtomicInteger(1);

            try {
                // 分片上传
                int offset = 0;
                while (offset < data.length) {
                    int partSize = (int) Math.min(PART_SIZE, data.length - offset);
                    byte[] partData = new byte[partSize];
                    System.arraycopy(data, offset, partData, 0, partSize);

                    // 计算分片的MD5
                    MessageDigest md = MessageDigest.getInstance("MD5");
                    md.update(partData);
                    String partMd5 = Base64.getEncoder().encodeToString(md.digest());

                    // 上传分片
                    UploadPartResponse uploadPartResponse = client.uploadPart(
                        UploadPartRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .partNumber(partNumber.get())
                            .contentLength((long) partSize)
                            .build(),
                        AsyncRequestBody.fromBytes(partData)).join();

                    // 记录已上传的分片
                    completedParts.add(CompletedPart.builder()
                        .partNumber(partNumber.get())
                        .eTag(uploadPartResponse.eTag())
                        .build());

                    partNumber.incrementAndGet();
                    offset += partSize;
                }

                // 完成分片上传
                CompleteMultipartUploadResponse completeResponse = client.completeMultipartUpload(
                        CompleteMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .multipartUpload(CompletedMultipartUpload.builder()
                                .parts(completedParts)
                                .build())
                            .build())
                    .join();

                // 返回上传结果
                return UploadResult.builder()
                    .url(getUrl() + StringUtils.SLASH + key)
                    .filename(key)
                    .eTag(completeResponse.eTag())
                    .build();

            } catch (Exception e) {
                // 发生错误时中止分片上传
                client.abortMultipartUpload(
                        AbortMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .build())
                    .join();
                throw new OssException("分片上传失败: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new OssException("分片上传失败: " + e.getMessage());
        }
    }

    /**
     * 根据字节数组和指定的后缀、路径、存储桶名称、内容类型和MD5校验值上传文件（分片上传）
     *
     * @param data        文件的字节数组
     * @param suffix      文件的后缀
     * @param path        存储桶中文件的路径
     * @param bucketName  存储桶的名称
     * @param contentType 文件的内容类型（MIME类型）
     * @param md5Digest   文件的MD5校验值
     * @return 包含上传结果信息的 UploadResult 对象
     * @throws IOException 如果在创建输入流或调用上传方法时发生I/O错误
     */
    public UploadResult uploadSuffixWithMultipart(byte[] data, String suffix, String path, String bucketName,
                                                  String contentType, String md5Digest) throws IOException {
        // 创建临时文件
        File tempFile = FileUtils.createTempFile();
        try {
            // 写入临时文件
            Files.write(tempFile.toPath(), data);
            // 使用分片上传
            return multipartUploadWithMd5(data, path, contentType, md5Digest);
        } finally {
            // 清理临时文件
            FileUtils.del(tempFile);
        }
    }

    /**
     * 使用MultipartFile进行分片上传
     *
     * @param file MultipartFile对象
     * @param key  对象键
     * @return 上传结果
     */
    public UploadResult multipartUpload(MultipartFile file, String key) {
        File tempFile = null;
        try {
            // 获取文件大小
            long fileSize = file.getSize();

            // 如果文件小于分片大小，直接上传
            if (fileSize <= PART_SIZE) {
                return upload(file.getInputStream(), key, fileSize, file.getContentType());
            }

            // 创建临时文件
            tempFile = FileUtils.createTempFile();
            // 将MultipartFile转换为临时文件
            Files.copy(file.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

            // 初始化分片上传
            CreateMultipartUploadResponse initResponse = client.createMultipartUpload(
                    CreateMultipartUploadRequest.builder()
                        .bucket(properties.getBucketName())
                        .key(key)
                        .contentType(file.getContentType())
                        .build())
                .join();

            String uploadId = initResponse.uploadId();
            List<CompletedPart> completedParts = new ArrayList<>();
            AtomicInteger partNumber = new AtomicInteger(1);

            try {
                // 读取文件并分片上传
                byte[] buffer = new byte[(int) PART_SIZE];
                try (InputStream inputStream = Files.newInputStream(tempFile.toPath())) {
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        // 上传分片
                        byte[] partData = new byte[bytesRead];
                        System.arraycopy(buffer, 0, partData, 0, bytesRead);

                        UploadPartResponse uploadPartResponse = client.uploadPart(
                            UploadPartRequest.builder()
                                .bucket(properties.getBucketName())
                                .key(key)
                                .uploadId(uploadId)
                                .partNumber(partNumber.get())
                                .contentLength((long) bytesRead)
                                .build(),
                            AsyncRequestBody.fromBytes(partData)).join();

                        // 记录已上传的分片
                        completedParts.add(CompletedPart.builder()
                            .partNumber(partNumber.get())
                            .eTag(uploadPartResponse.eTag())
                            .build());

                        partNumber.incrementAndGet();
                    }
                }

                // 完成分片上传
                CompleteMultipartUploadResponse completeResponse = client.completeMultipartUpload(
                        CompleteMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .multipartUpload(CompletedMultipartUpload.builder()
                                .parts(completedParts)
                                .build())
                            .build())
                    .join();

                // 返回上传结果
                return UploadResult.builder()
                    .url(getUrl() + StringUtils.SLASH + key)
                    .filename(key)
                    .eTag(completeResponse.eTag())
                    .build();

            } catch (Exception e) {
                // 发生错误时中止分片上传
                client.abortMultipartUpload(
                        AbortMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .uploadId(uploadId)
                            .build())
                    .join();
                throw new OssException("分片上传失败: " + e.getMessage());
            }
        } catch (Exception e) {
            throw new OssException("分片上传失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                FileUtils.del(tempFile);
            }
        }
    }

    /**
     * 使用MultipartFile进行分片上传（带MD5校验）
     *
     * @param file      MultipartFile对象
     * @param key       对象键
     * @param md5Digest MD5校验值
     * @return 上传结果
     */
    public UploadResult multipartUploadWithMd5(MultipartFile file, String key, String md5Digest) {
        try {
            // 获取文件大小
            long fileSize = file.getSize();

            // 如果文件小于分片大小，直接上传
            if (fileSize <= PART_SIZE) {
                return upload(file.getInputStream(), key, fileSize, file.getContentType(), md5Digest);
            }

            // 创建临时文件
            File tempFile = FileUtils.createTempFile();
            try {
                // 将MultipartFile转换为临时文件
                Files.copy(file.getInputStream(), tempFile.toPath(), StandardCopyOption.REPLACE_EXISTING);

                // 初始化分片上传
                CreateMultipartUploadResponse initResponse = client.createMultipartUpload(
                        CreateMultipartUploadRequest.builder()
                            .bucket(properties.getBucketName())
                            .key(key)
                            .contentType(file.getContentType())
                            .build())
                    .join();

                String uploadId = initResponse.uploadId();
                List<CompletedPart> completedParts = new ArrayList<>();
                AtomicInteger partNumber = new AtomicInteger(1);

                try {
                    // 读取文件并分片上传
                    byte[] buffer = new byte[(int) PART_SIZE];
                    try (InputStream inputStream = Files.newInputStream(tempFile.toPath())) {
                        int bytesRead;
                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                            // 上传分片
                            byte[] partData = new byte[bytesRead];
                            System.arraycopy(buffer, 0, partData, 0, bytesRead);

                            // 计算分片的MD5
                            MessageDigest md = MessageDigest.getInstance("MD5");
                            md.update(partData);
                            String partMd5 = Base64.getEncoder().encodeToString(md.digest());

                            UploadPartResponse uploadPartResponse = client.uploadPart(
                                UploadPartRequest.builder()
                                    .bucket(properties.getBucketName())
                                    .key(key)
                                    .uploadId(uploadId)
                                    .partNumber(partNumber.get())
                                    .contentLength((long) bytesRead)
                                    .build(),
                                AsyncRequestBody.fromBytes(partData)).join();

                            // 记录已上传的分片
                            completedParts.add(CompletedPart.builder()
                                .partNumber(partNumber.get())
                                .eTag(uploadPartResponse.eTag())
                                .build());

                            partNumber.incrementAndGet();
                        }
                    }

                    // 完成分片上传
                    CompleteMultipartUploadResponse completeResponse = client.completeMultipartUpload(
                            CompleteMultipartUploadRequest.builder()
                                .bucket(properties.getBucketName())
                                .key(key)
                                .uploadId(uploadId)
                                .multipartUpload(CompletedMultipartUpload.builder()
                                    .parts(completedParts)
                                    .build())
                                .build())
                        .join();

                    // 返回上传结果
                    return UploadResult.builder()
                        .url(getUrl() + StringUtils.SLASH + key)
                        .filename(key)
                        .eTag(completeResponse.eTag())
                        .build();

                } catch (Exception e) {
                    // 发生错误时中止分片上传
                    client.abortMultipartUpload(
                            AbortMultipartUploadRequest.builder()
                                .bucket(properties.getBucketName())
                                .key(key)
                                .uploadId(uploadId)
                                .build())
                        .join();
                    throw new OssException("分片上传失败: " + e.getMessage());
                }
            } finally {
                // 清理临时文件
                FileUtils.del(tempFile);
            }
        } catch (Exception e) {
            throw new OssException("分片上传失败: " + e.getMessage());
        }
    }
}
