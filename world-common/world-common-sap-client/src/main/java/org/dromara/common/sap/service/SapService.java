package org.dromara.common.sap.service;

import com.sap.conn.jco.JCoException;
import java.util.Map;

public interface SapService {

    /**
     * 调用SAP函数
     *
     * @param functionName SAP函数名称
     * @param params 输入参数
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    Map<String, Object> callFunction(String functionName, Map<String, Object> params) throws JCoException;

    /**
     * 调用SAP函数（带表格参数）
     *
     * @param functionName SAP函数名称
     * @param params 输入参数
     * @param tableParams 表格参数
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    Map<String, Object> callFunctionWithTable(String functionName, Map<String, Object> params, Map<String, Object[]> tableParams) throws JCoException;

    /**
     * 调用SAP函数（带表格参数）
     *
     * @param functionName SAP函数名称
     * @param tableParams 表格参数
     * @return 返回结果
     * @throws JCoException SAP调用异常
     */
    Map<String, Object> callFunctionWithTable(String functionName, Map<String, Object[]> tableParams) throws JCoException;
}
