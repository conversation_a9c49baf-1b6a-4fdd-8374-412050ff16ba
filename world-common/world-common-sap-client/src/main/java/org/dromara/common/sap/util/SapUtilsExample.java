package org.dromara.common.sap.util;

import com.sap.conn.jco.JCoException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.sap.vo.SapExampleVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SAP工具类使用示例
 */
@Slf4j
public class SapUtilsExample {

    /**
     * 调用无参数SAP函数示例
     */
    public void callFunctionWithoutParams() {
        try {
            // 调用无参数的SAP函数
            Map<String, Object> result = SapUtils.callFunction("BAPI_SYSTEM_INFO");

            // 获取返回结果
            String systemId = SapUtils.getStringParameter(result, "SYSTEMID");
            String systemName = SapUtils.getStringParameter(result, "SYSTEM_NAME");

            log.info("系统ID: {}, 系统名称: {}", systemId, systemName);
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带单个参数的SAP函数示例
     */
    public void callFunctionWithSingleParam() {
        try {
            // 调用带单个参数的SAP函数
            Map<String, Object> result = SapUtils.callFunction("BAPI_MATERIAL_GET_DETAIL", "IV_MATERIAL", "10000001");

            // 获取返回结果
            String materialDesc = SapUtils.getStringParameter(result, "ES_MAKTX");
            String baseUnit = SapUtils.getStringParameter(result, "ES_MARA-MEINS");

            log.info("物料描述: {}, 基本单位: {}", materialDesc, baseUnit);
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带多个参数的SAP函数示例
     */
    public void callFunctionWithMultipleParams() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IV_MATERIAL", "10000001");
            params.put("IV_PLANT", "1000");

            // 调用带多个参数的SAP函数
            Map<String, Object> result = SapUtils.callFunction("BAPI_MATERIAL_GET_DETAIL", params);

            // 获取返回结果
            String materialDesc = SapUtils.getStringParameter(result, "ES_MAKTX");
            String baseUnit = SapUtils.getStringParameter(result, "ES_MARA-MEINS");
            Integer stockQuantity = SapUtils.getIntegerParameter(result, "ES_MARD-LABST");

            log.info("物料描述: {}, 基本单位: {}, 库存数量: {}", materialDesc, baseUnit, stockQuantity);
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带表格参数的SAP函数示例
     */
    public void callFunctionWithTable() {
        try {
            // 准备输入参数
            Map<String, Object> params = new HashMap<>();
            params.put("IV_SOLD_TO", "1000001");
            params.put("IV_PURCHASE_NO", "PO-2023-001");

            // 准备表格数据
            Object[] tableData = new Object[2];

            // 使用工具方法创建表格行
            tableData[0] = SapUtils.createTableRow(
                "POSNR", "10",
                "MATNR", "10000001",
                "MENGE", 10,
                "UNIT", "EA");

            tableData[1] = SapUtils.createTableRow(
                "POSNR", "20",
                "MATNR", "10000002",
                "MENGE", 5,
                "UNIT", "EA");

            // 调用带表格参数的SAP函数
            Map<String, Object> result = SapUtils.callFunctionWithTable(
                "BAPI_SALESORDER_CREATE",
                params,
                "IT_ITEMS",
                tableData);

            // 获取返回结果
            String salesOrderNumber = SapUtils.getStringParameter(result, "EV_SALESORDER");

            // 检查返回消息
            Object[] messages = SapUtils.getTableData(result, "ET_RETURN");
            if (messages != null) {
                for (Object msgObj : messages) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> msg = (Map<String, Object>) msgObj;
                    String type = (String) msg.get("TYPE");
                    String message = (String) msg.get("MESSAGE");

                    if ("E".equals(type) || "A".equals(type)) {
                        log.error("SAP错误消息: {}", message);
                    } else {
                        log.info("SAP消息: {}", message);
                    }
                }
            }

            log.info("创建的销售订单号: {}", salesOrderNumber);
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带多个表格参数的SAP函数示例
     */
    public void callFunctionWithMultipleTables() {
        try {
            // 准备输入参数
            Map<String, Object> params = new HashMap<>();
            params.put("IV_DOCUMENT", "1000000001");

            // 准备多个表格参数
            Map<String, Object[]> tableParams = new HashMap<>();

            // 第一个表格
            Object[] items = new Object[2];
            items[0] = SapUtils.createTableRow(
                "POSNR", "10",
                "MATNR", "10000001",
                "MENGE", 10);
            items[1] = SapUtils.createTableRow(
                "POSNR", "20",
                "MATNR", "10000002",
                "MENGE", 5);
            tableParams.put("IT_ITEMS", items);

            // 第二个表格
            Object[] schedules = new Object[1];
            schedules[0] = SapUtils.createTableRow(
                "POSNR", "10",
                "DATUM", "20230415",
                "MENGE", 10);
            tableParams.put("IT_SCHEDULES", schedules);

            // 调用带多个表格参数的SAP函数
            Map<String, Object> result = SapUtils.callFunctionWithTable(
                "BAPI_SALESORDER_CREATE",
                params,
                tableParams);

            // 获取返回结果
            String salesOrderNumber = SapUtils.getStringParameter(result, "EV_SALESORDER");
            log.info("创建的销售订单号: {}", salesOrderNumber);
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 可回调方法
     *
     * @return null
     * <AUTHOR>
     * @date 16:43
     */
    public void callFunctionWithCallback() throws JCoException {
        // 完整版使用示例
        Map<String, Object> params = new HashMap<>();
        params.put("PARAM1", "value1");

        Map<String, Object> callbackParams = new HashMap<>();
        callbackParams.put("CALLBACK_PARAM1", "callback_value1");
        Object[] tableData = new Object[]{ /* 表格数据 */};
        Map<String, Object> result = SapUtils.callFunctionWithCallback(
            "ZPPM_CONF_SYNCH_MODIFY",  // 主函数名称
            params,                     // 主函数参数
            "TXI_CONF",                // 主函数表格名称
            tableData,                 // 主函数表格数据
            "ZPPM_NOTIFY_RESULT",      // 回调函数名称
            callbackParams,            // 回调函数参数
            "TYPE",                    // 成功状态参数名称
            "MESSAGE"                  // 消息参数名称
        );

        // 简化版使用示例
        Map<String, Object> result1 = SapUtils.callFunctionWithCallback(
            "ZPPM_CONF_SYNCH_MODIFY",  // 主函数名称
            "TXI_CONF",                // 主函数表格名称
            tableData,                 // 主函数表格数据
            "ZPPM_NOTIFY_RESULT",      // 回调函数名称
            "TYPE",                    // 成功状态参数名称
            "MESSAGE"                  // 消息参数名称
        );
    }

    /**
     * 调用SAP函数并转换为对象列表示例
     */
    public void exampleConvertToList() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_AUFNR", "1000000123"); // 订单号
            params.put("IM_WERKS", "1000"); // 工厂

            // 调用SAP函数并转换为对象列表
            List<SapExampleVo> voList = SapUtils.callFunctionAndConvertToList(
                "ZPPM_MAST_GET_MO", // SAP函数名
                params,
                "TXE_AFPO", // 返回表格名
                SapExampleVo.class // 目标类型
            );

            // 处理结果
            for (SapExampleVo vo : voList) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                    vo.getOrderNo(),
                    vo.getCustomerNo(),
                    vo.getOrderQty(),
                    vo.getOrderDate(),
                    vo.getStatus(),
                    vo.getRemark()
                );
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带表格参数的SAP函数并转换为对象列表示例
     */
    public void exampleConvertWithTable() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_WERKS", "1000"); // 工厂

            // 准备表格参数
            Map<String, Object[]> tableParams = new HashMap<>();
            Object[] orderRows = new Object[]{
                SapUtils.createTableRow("AUFNR", "1000000123", "KUNNR", "C001"),
                SapUtils.createTableRow("AUFNR", "1000000124", "KUNNR", "C002")
            };
            tableParams.put("IT_AUFNR", orderRows);

            // 调用SAP函数并转换为对象列表
            List<SapExampleVo> voList = SapUtils.callFunctionWithTableAndConvert(
                "ZPPM_MAST_GET_MO", // SAP函数名
                params,
                tableParams,
                "TXE_AFPO", // 返回表格名
                SapExampleVo.class // 目标类型
            );

            // 处理结果
            for (SapExampleVo vo : voList) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                    vo.getOrderNo(),
                    vo.getCustomerNo(),
                    vo.getOrderQty(),
                    vo.getOrderDate(),
                    vo.getStatus(),
                    vo.getRemark()
                );
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用SAP函数并转换为单个对象示例
     */
    public void exampleConvertToObject() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_AUFNR", "1000000123"); // 订单号

            // 调用SAP函数并转换为对象
            SapExampleVo vo = SapUtils.callFunctionAndConvert(
                "ZPPM_MAST_GET_MO_DETAIL", // SAP函数名
                params,
                SapExampleVo.class // 目标类型
            );

            // 处理结果
            if (vo != null) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                    vo.getOrderNo(),
                    vo.getCustomerNo(),
                    vo.getOrderQty(),
                    vo.getOrderDate(),
                    vo.getStatus(),
                    vo.getRemark()
                );
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }
}
