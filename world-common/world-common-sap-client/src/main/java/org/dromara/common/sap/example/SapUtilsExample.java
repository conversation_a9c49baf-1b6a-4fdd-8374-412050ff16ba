package org.dromara.common.sap.example;

import com.sap.conn.jco.JCoException;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.sap.util.SapUtils;
import org.dromara.common.sap.vo.SapExampleVo;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SapUtils使用示例
 */
@Slf4j
public class SapUtilsExample {

    /**
     * 调用SAP函数并转换为对象列表示例
     */
    public void exampleConvertToList() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_AUFNR", "1000000123"); // 订单号
            params.put("IM_WERKS", "1000"); // 工厂

            // 调用SAP函数并转换为对象列表
            List<SapExampleVo> voList = SapUtils.callFunctionAndConvertToList(
                    "ZPPM_MAST_GET_MO", // SAP函数名
                    params,
                    "TXE_AFPO", // 返回表格名
                    SapExampleVo.class // 目标类型
            );

            // 处理结果
            for (SapExampleVo vo : voList) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                        vo.getOrderNo(),
                        vo.getCustomerNo(),
                        vo.getOrderQty(),
                        vo.getOrderDate(),
                        vo.getStatus(),
                        vo.getRemark());
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用带表格参数的SAP函数并转换为对象列表示例
     */
    public void exampleConvertWithTable() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_WERKS", "1000"); // 工厂

            // 准备表格参数
            Map<String, Object[]> tableParams = new HashMap<>();
            Object[] orderRows = new Object[] {
                    SapUtils.createTableRow("AUFNR", "1000000123", "KUNNR", "C001"),
                    SapUtils.createTableRow("AUFNR", "1000000124", "KUNNR", "C002")
            };
            tableParams.put("IT_AUFNR", orderRows);

            // 调用SAP函数并转换为对象列表
            List<SapExampleVo> voList = SapUtils.callFunctionWithTableAndConvert(
                    "ZPPM_MAST_GET_MO", // SAP函数名
                    params,
                    tableParams,
                    "TXE_AFPO", // 返回表格名
                    SapExampleVo.class // 目标类型
            );

            // 处理结果
            for (SapExampleVo vo : voList) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                        vo.getOrderNo(),
                        vo.getCustomerNo(),
                        vo.getOrderQty(),
                        vo.getOrderDate(),
                        vo.getStatus(),
                        vo.getRemark());
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }

    /**
     * 调用SAP函数并转换为单个对象示例
     */
    public void exampleConvertToObject() {
        try {
            // 准备参数
            Map<String, Object> params = new HashMap<>();
            params.put("IM_AUFNR", "1000000123"); // 订单号

            // 调用SAP函数并转换为对象
            SapExampleVo vo = SapUtils.callFunctionAndConvert(
                    "ZPPM_MAST_GET_MO_DETAIL", // SAP函数名
                    params,
                    SapExampleVo.class // 目标类型
            );

            // 处理结果
            if (vo != null) {
                log.info("订单号: {}, 客户编号: {}, 订单数量: {}, 订单日期: {}, 状态: {}, 备注: {}",
                        vo.getOrderNo(),
                        vo.getCustomerNo(),
                        vo.getOrderQty(),
                        vo.getOrderDate(),
                        vo.getStatus(),
                        vo.getRemark());
            }
        } catch (JCoException e) {
            log.error("调用SAP函数失败", e);
        }
    }
}