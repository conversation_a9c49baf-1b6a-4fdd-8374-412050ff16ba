package org.dromara.common.sap.vo;

import lombok.Data;
import org.dromara.common.sap.annotation.SapFiled;
import org.dromara.common.sap.enums.FiledType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * SAP结果对象示例VO类
 */
@Data
public class SapExampleVo {
    /**
     * 订单号
     */
    @SapFiled(name = "AUFNR", type = FiledType.STRING)
    private String orderNo;

    /**
     * 客户编号
     */
    @SapFiled(name = "KUNNR", type = FiledType.STRING)
    private String customerNo;

    /**
     * 订单数量
     */
    @SapFiled(name = "PSMNG", type = FiledType.BIGDECIMAL)
    private BigDecimal orderQty;

    /**
     * 订单日期
     */
    @SapFiled(name = "ERDAT", type = FiledType.DATE)
    private Date orderDate;

    /**
     * 订单状态
     */
    @SapFiled(name = "STATUS", type = FiledType.INT)
    private Integer status;

    /**
     * 备注
     */
    @SapFiled(name = "REMARK", aliasNames = {"REMARKS", "NOTE"}, type = FiledType.STRING)
    private String remark;
}
