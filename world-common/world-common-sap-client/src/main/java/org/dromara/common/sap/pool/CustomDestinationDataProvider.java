package org.dromara.common.sap.pool;

import com.sap.conn.jco.ext.DestinationDataEventListener;
import com.sap.conn.jco.ext.DestinationDataProvider;
import com.sap.conn.jco.ext.Environment;

import java.util.HashMap;
import java.util.Properties;

public class CustomDestinationDataProvider implements DestinationDataProvider {

    private final HashMap<String, Properties> destinationProperties = new HashMap<>();

    public CustomDestinationDataProvider() {
        Environment.registerDestinationDataProvider(this);
    }

    public void addDestinationProperties(String destinationName, Properties properties) {
        destinationProperties.put(destinationName, properties);
    }

    @Override
    public Properties getDestinationProperties(String destinationName) {
        return destinationProperties.get(destinationName);
    }

    @Override
    public void setDestinationDataEventListener(DestinationDataEventListener destinationDataEventListener) {
        // 此处可以实现事件监听器，用于处理连接事件
    }

    @Override
    public boolean supportsEvents() {
        return true;
    }
}