package org.dromara.common.core.constant;

/**
 * 缓存的key 常量
 *
 * <AUTHOR> Li
 */
public interface CacheConstants {

    /**
     * 在线用户 redis key
     */
    String ONLINE_TOKEN_KEY = "online_tokens:";

    /**
     * 参数管理 cache key
     */
    String SYS_CONFIG_KEY = "sys_config:";

    /**
     * 字典管理 cache key
     */
    String SYS_DICT_KEY = "sys_dict:";

    /**
     * 登录账户密码错误次数 redis key
     */
    String PWD_ERR_CNT_KEY = "pwd_err_cnt:";

    /**
     * 工作中心同步数据锁的key
     */
    String SYNC_LOCK_KEY = "workcenter:sync:lock";
    /**
     * 工作中心同步状态锁的key
     */
    String SYNC_STATUS_KEY = "workcenter:sync:status";

    /**
     * 供应商管理同步数据锁的key
     */
    String SYNC_SUPPLIE_LOCK_KEY = "workcenter:sync:lock";
    /**
     * 供应商管理同步状态锁的key
     */
    String SYNC_SUPPLIE_STATUS_KEY = "workcenter:sync:status";

    /**
     * 供应商管理 cache key
     */
    String MES_SUPPLIER_KEY = "mes_supplier_info:";

    /**
     *核心|雪花生成|标识排序值
     */
    String SNOWFLAKE_CREATOR_KEY="core:snowflake:creatorId";
}
