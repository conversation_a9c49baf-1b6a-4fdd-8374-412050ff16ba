package org.dromara.system.strategy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import jakarta.annotation.Resource;
import org.dromara.common.core.enums.PartTypeEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.system.domain.SysAutoCodeRule;
import org.dromara.system.domain.bo.SysAutoCodePartBo;
import org.dromara.system.domain.bo.SysAutoCodeResultBo;
import org.dromara.system.domain.vo.SysAutoCodePartVo;
import org.dromara.system.domain.vo.SysAutoCodeResultVo;
import org.dromara.system.service.ISysAutoCodePartService;
import org.dromara.system.service.ISysAutoCodeResultService;
import org.dromara.system.service.ISysAutoCodeRuleService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class AutoCodeUtil {
    public static final ThreadLocal<Boolean> threadLocal = new ThreadLocal<>();

    @Resource
    private ISysAutoCodeRuleService iAutoCodeRuleService;

    @Resource
    private ISysAutoCodePartService iAutoCodePartService;

    @Resource
    private ISysAutoCodeResultService iAutoCodeResultService;

    @Resource
    private PartTypeHandler partTypeHandler;

    private String lastSerialNo;
    @Resource
    private RedissonClient redissonClient;

    public String genSerialCode(String ruleCode, String inputCharacter) {
        // 参数验证
        Assert.notBlank(ruleCode, "规则编码不能为空");

        // 使用分布式锁
        RLock lock = redissonClient.getLock("autocode:lock:" + ruleCode);
        try {
            if (lock.tryLock(5, 10, TimeUnit.SECONDS)) {
                try {
                    return generateCode(ruleCode, inputCharacter);
                } finally {
                    lock.unlock();
                }
            }
            throw new ServiceException("获取锁超时");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new ServiceException("生成编码被中断");
        }
    }

    /**
     * synchronized
     *
     * @param ruleCode
     * @param inputCharacter
     * @return java.lang.String
     * <AUTHOR>
     * @date 16:41
     */
    @Log(title = "生成业务编号", businessType = BusinessType.INSERT)
    public String generateCode(String ruleCode, String inputCharacter) {

        //查找编码规则
        SysAutoCodeRule rule = iAutoCodeRuleService.getOne(ruleCode);
        Assert.notNull(rule, "未获取到指定类型:[{}]的业务编码生成规则", ruleCode);

        //查找规则组成
        SysAutoCodePartBo partParam = new SysAutoCodePartBo();
        partParam.setRuleId(rule.getRuleId());
        List<SysAutoCodePartVo> parts = iAutoCodePartService.queryList(partParam);
        List<SysAutoCodePartVo> collect = parts.stream().filter(part -> PartTypeEnum.PART_TYPE_SERIALNO.getCode().equals(part.getPartType())).collect(Collectors.toList());

        Assert.isTrue(collect.size() < 2, "编码规则[{}]流水号方式的组成只能存在一个", ruleCode);

        StringBuilder buff = new StringBuilder();
        parts.forEach(codePart -> {
            codePart.setInputCharacter(inputCharacter);
            //根据当前组成部分，获取当前组成部分的结果
            String partStr = partTypeHandler.choiceExecute(codePart);

            //如果是流水号部分，则进行记录
            if (StringUtils.equals(codePart.getPartType(), PartTypeEnum.PART_TYPE_SERIALNO.getCode())) {
                lastSerialNo = partStr;
            }
            //将获取到的部分组装进整体编码中
            buff.append(partStr);
        });

        Assert.notBlank(buff.toString(), "规则：[{}]生成的编码为空！", ruleCode);

        String autoCode = paddingStr(rule, buff);

        //将生成结果保存到数据库
        saveAutoCodeResult(rule, autoCode, inputCharacter);
        return autoCode;
    }

    /**
     * 根据编码规则的配置进行补齐操作
     *
     * @param rule
     * @param sb
     * @return
     */
    private String paddingStr(SysAutoCodeRule rule, StringBuilder sb) {
        String isPadding = rule.getIsPadded();
        if ("Y".equals(isPadding)) {
            int maxLength = rule.getMaxLength();
            String paddingChar = rule.getPaddedChar();
            StringBuilder resultStr = new StringBuilder();
            long length = maxLength - sb.length();
            Assert.isTrue(maxLength > sb.length(), "生成的编码[{}]已经超出规则中配置的最大长度：[{}]", sb.toString(), maxLength);

            if ("L".equals(rule.getPaddedMethod())) {
                //左补齐
                //使用指定字符补齐左侧后,再将生成的编码添加到右侧
                for (; length > 0; length--) {
                    resultStr.append(paddingChar);
                }
                resultStr.append(sb);
            } else {
                //右补齐
                //将生成的编码添加到左侧后,再使用指定字符补齐右侧
                resultStr.append(sb);
                for (; length > 0; length--) {
                    resultStr.append(paddingChar);
                }
            }
            return resultStr.toString();
        }
        //如果不需要补齐，则直接返回
        return sb.toString();
    }

    private void saveAutoCodeResult(SysAutoCodeRule rule, String autoCode, String inputChar) {
        //针对当前线程的判断 flag = true则数据库中没有当前规则的生成记录
        Boolean flag = threadLocal.get();
        if (flag != null && flag) {
            SysAutoCodeResultBo rs = new SysAutoCodeResultBo();
            rs.setRuleId(rule.getRuleId());
            rs.setGenDate(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
            rs.setLastResult(autoCode);
            rs.setGenIndex(1);
            rs.setLastSerialNo(Integer.parseInt(lastSerialNo));
            rs.setLastInputChar(inputChar);
            iAutoCodeResultService.insertByBo(rs);
        } else {
            //直接更新对应的记录（我们默认非流水号模式下一个RULE_CODE只有一种方式）
            SysAutoCodeResultBo bo = new SysAutoCodeResultBo();
            bo.setRuleId(rule.getRuleId());
            List<SysAutoCodeResultVo> results = iAutoCodeResultService.queryList(bo);
            Assert.notEmpty(results, "未查询到规则{[]}对应的结果记录", rule.getRuleCode());
            SysAutoCodeResultVo rs = results.get(0);
            rs.setLastResult(autoCode);
            rs.setGenDate(DateUtil.format(LocalDateTime.now(), "yyyyMMddHHmmss"));
            rs.setGenIndex(rs.getGenIndex() + 1);
            rs.setLastSerialNo(Integer.parseInt(lastSerialNo));
            rs.setLastInputChar(inputChar);
            BeanUtil.copyProperties(rs, bo);
            //更新流水号
            iAutoCodeResultService.updateByBo(bo);
        }

    }
}
