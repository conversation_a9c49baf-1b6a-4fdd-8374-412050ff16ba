package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.SyncTimestampBo;
import org.dromara.system.domain.vo.SyncTimestampVo;

import java.util.Collection;
import java.util.List;

/**
 * 任务增量同步日期记录Service接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface ISyncTimestampService {

    /**
     * 查询任务增量同步日期记录
     *
     * @param id 主键
     * @return 任务增量同步日期记录
     */
    SyncTimestampVo queryById(Long id);

    /**
     * 分页查询任务增量同步日期记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 任务增量同步日期记录分页列表
     */
    TableDataInfo<SyncTimestampVo> queryPageList(SyncTimestampBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的任务增量同步日期记录列表
     *
     * @param bo 查询条件
     * @return 任务增量同步日期记录列表
     */
    List<SyncTimestampVo> queryList(SyncTimestampBo bo);

    /**
     * 新增任务增量同步日期记录
     *
     * @param bo 任务增量同步日期记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SyncTimestampBo bo);

    /**
     * 修改任务增量同步日期记录
     *
     * @param bo 任务增量同步日期记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SyncTimestampBo bo);

    /**
     * 校验并批量删除任务增量同步日期记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
