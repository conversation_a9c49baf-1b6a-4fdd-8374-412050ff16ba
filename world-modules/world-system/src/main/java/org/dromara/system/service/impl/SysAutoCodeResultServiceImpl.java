package org.dromara.system.service.impl;

import com.google.common.collect.Maps;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.system.domain.SysAutoCodePart;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.SysAutoCodeResultBo;
import org.dromara.system.domain.vo.SysAutoCodeResultVo;
import org.dromara.system.domain.SysAutoCodeResult;
import org.dromara.system.mapper.SysAutoCodeResultMapper;
import org.dromara.system.service.ISysAutoCodeResultService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 编码生成记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class SysAutoCodeResultServiceImpl implements ISysAutoCodeResultService {

    private final SysAutoCodeResultMapper baseMapper;

    /**
     * 查询编码生成记录
     *
     * @param codeId 主键
     * @return 编码生成记录
     */
    @Override
    public SysAutoCodeResultVo queryById(Long codeId){
        return baseMapper.selectVoById(codeId);
    }

    /**
     * 分页查询编码生成记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成记录分页列表
     */
    @Override
    public TableDataInfo<SysAutoCodeResultVo> queryPageList(SysAutoCodeResultBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysAutoCodeResult> lqw = buildQueryWrapper(bo);
        Page<SysAutoCodeResultVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的编码生成记录列表
     *
     * @param bo 查询条件
     * @return 编码生成记录列表
     */
    @Override
    public List<SysAutoCodeResultVo> queryList(SysAutoCodeResultBo bo) {
        LambdaQueryWrapper<SysAutoCodeResult> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    private LambdaQueryWrapper<SysAutoCodeResult> buildQueryWrapper(SysAutoCodeResultBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysAutoCodeResult> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysAutoCodeResult::getCodeId);
        lqw.eq(bo.getRuleId() != null, SysAutoCodeResult::getRuleId, bo.getRuleId());
        lqw.eq(StringUtils.isNotBlank(bo.getGenDate()), SysAutoCodeResult::getGenDate, bo.getGenDate());
        lqw.eq(bo.getGenIndex() != null, SysAutoCodeResult::getGenIndex, bo.getGenIndex());
        lqw.eq(StringUtils.isNotBlank(bo.getLastResult()), SysAutoCodeResult::getLastResult, bo.getLastResult());
        lqw.eq(bo.getLastSerialNo() != null, SysAutoCodeResult::getLastSerialNo, bo.getLastSerialNo());
        lqw.eq(StringUtils.isNotBlank(bo.getLastInputChar()), SysAutoCodeResult::getLastInputChar, bo.getLastInputChar());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr1()), SysAutoCodeResult::getAttr1, bo.getAttr1());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr2()), SysAutoCodeResult::getAttr2, bo.getAttr2());
        lqw.eq(bo.getAttr3() != null, SysAutoCodeResult::getAttr3, bo.getAttr3());
        lqw.eq(bo.getAttr4() != null, SysAutoCodeResult::getAttr4, bo.getAttr4());
        return lqw;
    }

    /**
     * 新增编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysAutoCodeResultBo bo) {
        SysAutoCodeResult add = MapstructUtils.convert(bo, SysAutoCodeResult.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCodeId(add.getCodeId());
        }
        return flag;
    }

    /**
     * 修改编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysAutoCodeResultBo bo) {
        SysAutoCodeResult update = MapstructUtils.convert(bo, SysAutoCodeResult.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysAutoCodeResult entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除编码生成记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public String partHandle(SysAutoCodePart sysAutoCodePart) {
        return sysAutoCodePart.getFixCharacter();
    }
}
