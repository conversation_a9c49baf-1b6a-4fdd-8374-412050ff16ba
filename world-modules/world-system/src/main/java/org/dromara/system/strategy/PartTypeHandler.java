package org.dromara.system.strategy;

import jakarta.annotation.Resource;
import org.dromara.common.core.enums.PartTypeEnum;
import org.dromara.system.domain.SysAutoCodePart;
import org.dromara.system.domain.vo.SysAutoCodePartVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class PartTypeHandler {

    @Resource
    List<PartTypeTemplate> partTypeTemplates;

    public String choiceExecute(SysAutoCodePartVo sysAutoCodePart){
        String partType = sysAutoCodePart.getPartType();
        return partTypeTemplates.get(PartTypeEnum.getByCode(partType).getBeanIndex()).partHandle(sysAutoCodePart);
    }

}
