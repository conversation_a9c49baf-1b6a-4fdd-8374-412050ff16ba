package org.dromara.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.SysAutoCodeRule;
import org.dromara.system.domain.bo.SysAutoCodeRuleBo;
import org.dromara.system.domain.vo.SysAutoCodeRuleVo;
import org.dromara.system.mapper.SysAutoCodeRuleMapper;
import org.dromara.system.service.ISysAutoCodeRuleService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 编码生成规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class SysAutoCodeRuleServiceImpl implements ISysAutoCodeRuleService {

    private final SysAutoCodeRuleMapper baseMapper;

    /**
     * 查询编码生成规则
     *
     * @param ruleId 主键
     * @return 编码生成规则
     */
    @Override
    public SysAutoCodeRuleVo queryById(Long ruleId) {
        return baseMapper.selectVoById(ruleId);
    }

    /**
     * 分页查询编码生成规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则分页列表
     */
    @Override
    public TableDataInfo<SysAutoCodeRuleVo> queryPageList(SysAutoCodeRuleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysAutoCodeRule> lqw = buildQueryWrapper(bo);
        Page<SysAutoCodeRuleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的编码生成规则列表
     *
     * @param bo 查询条件
     * @return 编码生成规则列表
     */
    @Override
    public List<SysAutoCodeRuleVo> queryList(SysAutoCodeRuleBo bo) {
        LambdaQueryWrapper<SysAutoCodeRule> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysAutoCodeRule> buildQueryWrapper(SysAutoCodeRuleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysAutoCodeRule> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysAutoCodeRule::getRuleId);
        lqw.like(StringUtils.isNotBlank(bo.getRuleCode()), SysAutoCodeRule::getRuleCode, bo.getRuleCode());
        lqw.like(StringUtils.isNotBlank(bo.getRuleName()), SysAutoCodeRule::getRuleName, bo.getRuleName());
        lqw.eq(StringUtils.isNotBlank(bo.getEnableFlag()), SysAutoCodeRule::getEnableFlag, bo.getEnableFlag());
        lqw.eq(bo.getAttr4() != null, SysAutoCodeRule::getAttr4, bo.getAttr4());
        return lqw;
    }

    /**
     * 新增编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysAutoCodeRuleBo bo) {
        SysAutoCodeRule add = MapstructUtils.convert(bo, SysAutoCodeRule.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRuleId(add.getRuleId());
        }
        return flag;
    }

    /**
     * 修改编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysAutoCodeRuleBo bo) {
        SysAutoCodeRule update = MapstructUtils.convert(bo, SysAutoCodeRule.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysAutoCodeRule entity) {
        if (!checkRuleCodeUnique(entity)) {
            throw new ServiceException("自动编码规则的编号'" + entity.getRuleCode() + "'重复!");
        }

        if (!checkRuleNameUnique(entity)) {
            throw new ServiceException("自动编码规则的名称'" + entity.getRuleName() + "'重复!");
        }
        if ("N".equals(entity.getIsPadded())) {
            entity.setPaddedChar(null);
            entity.setPaddedMethod(null);
        }
    }

    /**
     * 校验并批量删除编码生成规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    public boolean checkRuleCodeUnique(SysAutoCodeRule sysAutoCodeRule) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysAutoCodeRule>()
            .eq(SysAutoCodeRule::getRuleCode, sysAutoCodeRule.getRuleCode())
            .ne(ObjectUtil.isNotNull(sysAutoCodeRule.getRuleId()), SysAutoCodeRule::getRuleId, sysAutoCodeRule.getRuleId()));
        return !exist;
    }

    @Override
    public boolean checkRuleNameUnique(SysAutoCodeRule sysAutoCodeRule) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysAutoCodeRule>()
            .eq(SysAutoCodeRule::getRuleName, sysAutoCodeRule.getRuleName())
            .ne(ObjectUtil.isNotNull(sysAutoCodeRule.getRuleId()), SysAutoCodeRule::getRuleId, sysAutoCodeRule.getRuleId()));
        return !exist;
    }

    @Override
    public SysAutoCodeRule getOne(String ruleCode) {
        SysAutoCodeRule param = new SysAutoCodeRule();
        param.setRuleCode(ruleCode);
        List<SysAutoCodeRule> rules = baseMapper.selectSysAutoCodeResultList(param);
        if(CollectionUtil.isNotEmpty(rules)){
            return rules.get(0);
        }
        return null;
    }
}
