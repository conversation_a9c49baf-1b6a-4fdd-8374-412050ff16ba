package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * OA员工数据对象 employee_info
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("employee_info")
public class EmployeeInfo extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 员工ID
     */
    private Long ID;

    /**
     * 工号
     */
    private String gh;

    /**
     * 姓名
     */
    private String xm;

    /**
     * 指纹号/工号
     */
    private String zwh;

    /**
     * 卡号
     */
    private String kh;

    /**
     * 性别
     */
    private String xb;

    /**
     * 部门编码
     */
    private String bmbm;

    /**
     * 部门全称
     */
    private String sjbmbm;

    /**
     * 中心
     */
    private String bmqc;

    /**
     * 部门
     */
    private String bm;

    /**
     * 组别
     */
    private String zb;

    /**
     * 部门简称
     */
    private String bmjc;

    /**
     * 所属小组
     */
    private String ssxz;

    /**
     * 一级审批者
     */
    private String yjspz;

    /**
     * 二级审批者
     */
    private String ejspz;

    /**
     * 三级审批者
     */
    private String sjspz;

    /**
     * 岗位编码
     */
    private String gwbm;

    /**
     * 岗位
     */
    private String gw;

    /**
     * 兼任部门岗位
     */
    private String jrbmgw;

    /**
     * 职级代码编码
     */
    private String zjdmbm;

    /**
     * 职级
     */
    private String zj;

    /**
     * 职称
     */
    private String zc;

    /**
     * 员工状态
     */
    private String ygzt;

    /**
     * 聘用日期
     */
    private Date pyrq;

    /**
     * 离职日期
     */
    private Date lzrq;

    /**
     * 试用期开始日期
     */
    private Date syq;

    /**
     * 转正日期
     */
    private Date zzrq;

    /**
     * 是否转正
     */
    private Long sfzz;

    /**
     * 工龄（可能需要根据实际需求选择更合适的类型，如YEAR等）
     */
    private Long gl;

    /**
     * 最高学历
     */
    private String zgxl;

    /**
     * 学习专业
     */
    private String xxzy;

    /**
     * 毕业学校
     */
    private String byxx;

    /**
     * 英语水平（如CET-4, CET-6, TOEFL等）
     */
    private String yysp;

    /**
     * 办公电话
     */
    private String bgdh;

    /**
     * 手机
     */
    private String sj;

    /**
     * 公司邮件
     */
    private String gsyj;

    /**
     * 虚拟组织代号
     */
    private String xnzzdh;

    /**
     * 虚拟组织名称
     */
    private String xnzzmc;


}
