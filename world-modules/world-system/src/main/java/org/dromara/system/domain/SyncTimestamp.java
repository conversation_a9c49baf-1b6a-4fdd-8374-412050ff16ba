package org.dromara.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.util.Date;

/**
 * 任务增量同步日期记录对象 sync_timestamp
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sync_timestamp")
public class SyncTimestamp extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     *
     */
    private Date lastSyncTime;


}
