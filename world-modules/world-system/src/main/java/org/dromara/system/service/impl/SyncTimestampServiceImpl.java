package org.dromara.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.SyncTimestamp;
import org.dromara.system.domain.bo.SyncTimestampBo;
import org.dromara.system.domain.vo.SyncTimestampVo;
import org.dromara.system.mapper.SyncTimestampMapper;
import org.dromara.system.service.ISyncTimestampService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 任务增量同步日期记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@RequiredArgsConstructor
@Service
public class SyncTimestampServiceImpl implements ISyncTimestampService {

    private final SyncTimestampMapper baseMapper;

    /**
     * 查询任务增量同步日期记录
     *
     * @param id 主键
     * @return 任务增量同步日期记录
     */
    @Override
    public SyncTimestampVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询任务增量同步日期记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 任务增量同步日期记录分页列表
     */
    @Override
    public TableDataInfo<SyncTimestampVo> queryPageList(SyncTimestampBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SyncTimestamp> lqw = buildQueryWrapper(bo);
        Page<SyncTimestampVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的任务增量同步日期记录列表
     *
     * @param bo 查询条件
     * @return 任务增量同步日期记录列表
     */
    @Override
    public List<SyncTimestampVo> queryList(SyncTimestampBo bo) {
        LambdaQueryWrapper<SyncTimestamp> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SyncTimestamp> buildQueryWrapper(SyncTimestampBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SyncTimestamp> lqw = Wrappers.lambdaQuery();
        lqw.between(params.get("beginLastSyncTime") != null && params.get("endLastSyncTime") != null,
            SyncTimestamp::getLastSyncTime ,params.get("beginLastSyncTime"), params.get("endLastSyncTime"));
        return lqw;
    }

    /**
     * 新增任务增量同步日期记录
     *
     * @param bo 任务增量同步日期记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SyncTimestampBo bo) {
        SyncTimestamp add = MapstructUtils.convert(bo, SyncTimestamp.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改任务增量同步日期记录
     *
     * @param bo 任务增量同步日期记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SyncTimestampBo bo) {
        SyncTimestamp update = MapstructUtils.convert(bo, SyncTimestamp.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SyncTimestamp entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除任务增量同步日期记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
