package org.dromara.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.EmployeeInfo;
import org.dromara.system.domain.bo.EmployeeInfoBo;
import org.dromara.system.domain.vo.EmployeeInfoVo;
import org.dromara.system.mapper.EmployeeInfoMapper;
import org.dromara.system.service.IEmployeeInfoService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * OA员工数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@RequiredArgsConstructor
@Service
public class EmployeeInfoServiceImpl implements IEmployeeInfoService {

    private final EmployeeInfoMapper baseMapper;

    /**
     * 查询OA员工数据
     *
     * @param ID 主键
     * @return OA员工数据
     */
    @Override
    public EmployeeInfoVo queryById(Long ID) {
        return baseMapper.selectVoById(ID);
    }

    /**
     * 分页查询OA员工数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return OA员工数据分页列表
     */
    @Override
    public TableDataInfo<EmployeeInfoVo> queryPageList(EmployeeInfoBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<EmployeeInfo> lqw = buildQueryWrapper(bo);
        Page<EmployeeInfoVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的OA员工数据列表
     *
     * @param bo 查询条件
     * @return OA员工数据列表
     */
    @Override
    public List<EmployeeInfoVo> queryList(EmployeeInfoBo bo) {
        LambdaQueryWrapper<EmployeeInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<EmployeeInfo> buildQueryWrapper(EmployeeInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<EmployeeInfo> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getGh()), EmployeeInfo::getGh, bo.getGh());
        lqw.like(StringUtils.isNotBlank(bo.getXm()), EmployeeInfo::getXm, bo.getXm());
        return lqw;
    }

    /**
     * 新增OA员工数据
     *
     * @param bo OA员工数据
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(EmployeeInfoBo bo) {
        EmployeeInfo add = MapstructUtils.convert(bo, EmployeeInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setID(add.getID());
        }
        return flag;
    }

    /**
     * 修改OA员工数据
     *
     * @param bo OA员工数据
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(EmployeeInfoBo bo) {
        EmployeeInfo update = MapstructUtils.convert(bo, EmployeeInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(EmployeeInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除OA员工数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

}
