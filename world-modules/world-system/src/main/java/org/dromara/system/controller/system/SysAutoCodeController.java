package org.dromara.system.controller.system;

import org.dromara.common.core.domain.R;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.system.strategy.AutoCodeUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 自动编码控制器
 *
 * <AUTHOR>
 * @date 2025/4/11 08:14
 */
@RestController
@RequestMapping("/autocode")
public class SysAutoCodeController {

    private final AutoCodeUtil autoCodeUtil;

    public SysAutoCodeController(AutoCodeUtil autoCodeUtil) {
        this.autoCodeUtil = autoCodeUtil;
    }

    /**
     * 获取一个自动生成的编码
     *
     * @param ruleCode       规则编码
     * @param inputCharacter 输入字符
     * @return java.lang.String
     * <AUTHOR>
     * @date 2025/4/11 08:13
     */
    @GetMapping(value = {"/get/{ruleCode}/{inputCharacter}", "/get/{ruleCode}"})
    public R<String> getAutoCode(@PathVariable String ruleCode, @PathVariable(required = false) String inputCharacter) {
        String code = autoCodeUtil.genSerialCode(ruleCode, inputCharacter);
        return R.ok("操作成功", code);
    }

}
