package org.dromara.system.mapper;

import org.dromara.system.domain.SysAutoCodeRule;
import org.dromara.system.domain.vo.SysAutoCodeRuleVo;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
 * 编码生成规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface SysAutoCodeRuleMapper extends BaseMapperPlus<SysAutoCodeRule, SysAutoCodeRuleVo> {


    List<SysAutoCodeRule> selectSysAutoCodeResultList(SysAutoCodeRule param);
}
