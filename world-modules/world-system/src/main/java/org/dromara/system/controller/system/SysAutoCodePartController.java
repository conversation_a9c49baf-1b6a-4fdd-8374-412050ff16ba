package org.dromara.system.controller.system;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.SysAutoCodePartVo;
import org.dromara.system.domain.bo.SysAutoCodePartBo;
import org.dromara.system.service.ISysAutoCodePartService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 编码生成规则组成
 * 前端访问路由地址为:/system/autoCodePart
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/autocode/part")
public class SysAutoCodePartController extends BaseController {

    private final ISysAutoCodePartService sysAutoCodePartService;

    /**
     * 查询编码生成规则组成列表
     */
    @SaCheckPermission("system:autocode:part:list")
    @GetMapping("/list")
    public TableDataInfo<SysAutoCodePartVo> list(SysAutoCodePartBo bo, PageQuery pageQuery) {
        return sysAutoCodePartService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成规则组成列表
     */
    @SaCheckPermission("system:autocode:part:export")
    @Log(title = "编码生成规则组成", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysAutoCodePartBo bo, HttpServletResponse response) {
        List<SysAutoCodePartVo> list = sysAutoCodePartService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成规则组成", SysAutoCodePartVo.class, response);
    }

    /**
     * 获取编码生成规则组成详细信息
     *
     * @param partId 主键
     */
    @SaCheckPermission("system:autocode:part:query")
    @GetMapping("/{partId}")
    public R<SysAutoCodePartVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("partId") Long partId) {
        return R.ok(sysAutoCodePartService.queryById(partId));
    }

    /**
     * 新增编码生成规则组成
     */
    @SaCheckPermission("system:autocode:part:add")
    @Log(title = "编码生成规则组成", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysAutoCodePartBo bo) {
        return toAjax(sysAutoCodePartService.insertByBo(bo));
    }

    /**
     * 修改编码生成规则组成
     */
    @SaCheckPermission("system:autocode:part:edit")
    @Log(title = "编码生成规则组成", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysAutoCodePartBo bo) {
        return toAjax(sysAutoCodePartService.updateByBo(bo));
    }

    /**
     * 删除编码生成规则组成
     *
     * @param partIds 主键串
     */
    @SaCheckPermission("system:autocode:part:remove")
    @Log(title = "编码生成规则组成", businessType = BusinessType.DELETE)
    @DeleteMapping("/{partIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("partIds") Long[] partIds) {
        return toAjax(sysAutoCodePartService.deleteWithValidByIds(List.of(partIds), true));
    }
}
