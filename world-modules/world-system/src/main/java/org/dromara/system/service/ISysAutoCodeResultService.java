package org.dromara.system.service;

import org.dromara.system.domain.SysAutoCodePart;
import org.dromara.system.domain.SysAutoCodeResult;
import org.dromara.system.domain.vo.SysAutoCodeResultVo;
import org.dromara.system.domain.bo.SysAutoCodeResultBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 编码生成记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface ISysAutoCodeResultService {

    /**
     * 查询编码生成记录
     *
     * @param codeId 主键
     * @return 编码生成记录
     */
    SysAutoCodeResultVo queryById(Long codeId);

    /**
     * 分页查询编码生成记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成记录分页列表
     */
    TableDataInfo<SysAutoCodeResultVo> queryPageList(SysAutoCodeResultBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的编码生成记录列表
     *
     * @param bo 查询条件
     * @return 编码生成记录列表
     */
    List<SysAutoCodeResultVo> queryList(SysAutoCodeResultBo bo);


    /**
     * 新增编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否新增成功
     */
    Boolean insertByBo(SysAutoCodeResultBo bo);

    /**
     * 修改编码生成记录
     *
     * @param bo 编码生成记录
     * @return 是否修改成功
     */
    Boolean updateByBo(SysAutoCodeResultBo bo);

    /**
     * 校验并批量删除编码生成记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 分段的处理规则
     * @param sysAutoCodePart
     * @return
     */
    String partHandle(SysAutoCodePart sysAutoCodePart);

}
