package org.dromara.system.service;

import org.dromara.system.domain.SysAutoCodeRule;
import org.dromara.system.domain.bo.SysDictTypeBo;
import org.dromara.system.domain.vo.SysAutoCodeRuleVo;
import org.dromara.system.domain.bo.SysAutoCodeRuleBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 编码生成规则Service接口
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
public interface ISysAutoCodeRuleService {

    /**
     * 查询编码生成规则
     *
     * @param ruleId 主键
     * @return 编码生成规则
     */
    SysAutoCodeRuleVo queryById(Long ruleId);

    /**
     * 分页查询编码生成规则列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则分页列表
     */
    TableDataInfo<SysAutoCodeRuleVo> queryPageList(SysAutoCodeRuleBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的编码生成规则列表
     *
     * @param bo 查询条件
     * @return 编码生成规则列表
     */
    List<SysAutoCodeRuleVo> queryList(SysAutoCodeRuleBo bo);

    /**
     * 新增编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否新增成功
     */
    Boolean insertByBo(SysAutoCodeRuleBo bo);

    /**
     * 修改编码生成规则
     *
     * @param bo 编码生成规则
     * @return 是否修改成功
     */
    Boolean updateByBo(SysAutoCodeRuleBo bo);

    /**
     * 校验并批量删除编码生成规则信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 校验编码是否唯一
     *
     * @param sysAutoCodeRule 编码与名称
     * @return 结果
     */
    boolean checkRuleCodeUnique(SysAutoCodeRule sysAutoCodeRule);

    /**
     * 校验名称是否唯一
     *
     * @param sysAutoCodeRule 名称
     * @return 结果
     */
    boolean checkRuleNameUnique(SysAutoCodeRule sysAutoCodeRule);

    /**
     * 根据编码规则编码查询编码生成规则
     *
     * @param ruleCode 编码规则编码
     * @return 编码生成规则
     */
    SysAutoCodeRule getOne(String ruleCode);
}
