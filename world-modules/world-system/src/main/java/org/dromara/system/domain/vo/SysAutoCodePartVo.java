package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SysAutoCodePart;

import java.io.Serial;
import java.io.Serializable;


/**
 * 编码生成规则组成视图对象 sys_auto_code_part
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysAutoCodePart.class)
public class SysAutoCodePartVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 分段ID
     */
    @ExcelProperty(value = "分段ID")
    private Long partId;

    /**
     * 规则ID
     */
    @ExcelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 分段序号
     */
    @ExcelProperty(value = "分段序号")
    private Long partIndex;

    /**
     * 分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号
     */
    @ExcelProperty(value = "分段类型，INPUTCHAR：输入字符，NOWDATE：当前日期时间，FIXCHAR：固定字符，SERIALNO：流水号")
    private String partType;

    /**
     * 分段编号
     */
    @ExcelProperty(value = "分段编号")
    private String partCode;

    /**
     * 分段名称
     */
    @ExcelProperty(value = "分段名称")
    private String partName;

    /**
     * 分段长度
     */
    @ExcelProperty(value = "分段长度")
    private Long partLength;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String dateFormat;

    /**
     * 输入字符
     */
    @ExcelProperty(value = "输入字符")
    private String inputCharacter;

    /**
     * 固定字符
     */
    @ExcelProperty(value = "固定字符")
    private String fixCharacter;

    /**
     * 流水号起始值
     */
    @ExcelProperty(value = "流水号起始值")
    private Long seriaStartNo;

    /**
     * 流水号步长
     */
    @ExcelProperty(value = "流水号步长")
    private Long seriaStep;

    /**
     * 流水号当前值
     */
    @ExcelProperty(value = "流水号当前值")
    private Long seriaNowNo;

    /**
     * 流水号是否循环
     */
    @ExcelProperty(value = "流水号是否循环")
    private String cycleFlag;

    /**
     * 循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变
     */
    @ExcelProperty(value = "循环方式，YEAR：按年，MONTH：按月，DAY：按天，HOUR：按小时，MINITE：按分钟，OTHER：按传入字符变")
    private String cycleMethod;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 预留字段1
     */
    @ExcelProperty(value = "预留字段1")
    private String attr1;

    /**
     * 预留字段2
     */
    @ExcelProperty(value = "预留字段2")
    private String attr2;

    /**
     * 预留字段3
     */
    @ExcelProperty(value = "预留字段3")
    private Long attr3;

    /**
     * 预留字段4
     */
    @ExcelProperty(value = "预留字段4")
    private Long attr4;


}
