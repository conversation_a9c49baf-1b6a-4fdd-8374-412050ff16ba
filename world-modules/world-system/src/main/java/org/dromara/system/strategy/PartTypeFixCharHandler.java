package org.dromara.system.strategy;

import org.dromara.system.domain.vo.SysAutoCodePartVo;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(2)
public class PartTypeFixCharHandler implements PartTypeTemplate {
    @Override
    public String partHandle(SysAutoCodePartVo sysAutoCodePart) {
        return sysAutoCodePart.getFixCharacter();
    }


}
