package org.dromara.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import org.apache.ibatis.annotations.Select;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.api.domain.vo.RemoteEmployeeVo;
import org.dromara.system.domain.EmployeeInfo;
import org.dromara.system.domain.vo.EmployeeInfoVo;

import java.time.LocalDateTime;
import java.util.List;

/**
 * OA员工数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface EmployeeInfoMapper extends BaseMapperPlus<EmployeeInfo, EmployeeInfoVo> {

    @DS("user_slave")
    @Select("SELECT ID,GH,XM,XB,BMBM,GSYJ,SJ,YGZT,PYRQ,LZRQ FROM employee_info WHERE GH <'400000' and PYRQ >= #{lastSyncTime} and LZRQ IS null")
    List<RemoteEmployeeVo> findAddAll(LocalDateTime lastSyncTime);

    @DS("user_slave")
    @Select("SELECT ID,GH,XM,XB,BMBM,GSYJ,SJ,YGZT,PYRQ,LZRQ FROM employee_info WHERE GH <'400000' and LZRQ >= #{lastSyncTime} and LZRQ IS not null")
    List<RemoteEmployeeVo> findUpdateAll(LocalDateTime lastSyncTime);

}
