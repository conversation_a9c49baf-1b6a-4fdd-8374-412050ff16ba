package org.dromara.system.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.system.domain.bo.SysAutoCodePartBo;
import org.dromara.system.domain.vo.SysAutoCodePartVo;
import org.dromara.system.domain.SysAutoCodePart;
import org.dromara.system.mapper.SysAutoCodePartMapper;
import org.dromara.system.service.ISysAutoCodePartService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 编码生成规则组成Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@RequiredArgsConstructor
@Service
public class SysAutoCodePartServiceImpl implements ISysAutoCodePartService {

    private final SysAutoCodePartMapper baseMapper;

    /**
     * 查询编码生成规则组成
     *
     * @param partId 主键
     * @return 编码生成规则组成
     */
    @Override
    public SysAutoCodePartVo queryById(Long partId){
        return baseMapper.selectVoById(partId);
    }

    /**
     * 分页查询编码生成规则组成列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 编码生成规则组成分页列表
     */
    @Override
    public TableDataInfo<SysAutoCodePartVo> queryPageList(SysAutoCodePartBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysAutoCodePart> lqw = buildQueryWrapper(bo);
        Page<SysAutoCodePartVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的编码生成规则组成列表
     *
     * @param bo 查询条件
     * @return 编码生成规则组成列表
     */
    @Override
    public List<SysAutoCodePartVo> queryList(SysAutoCodePartBo bo) {
        LambdaQueryWrapper<SysAutoCodePart> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysAutoCodePart> buildQueryWrapper(SysAutoCodePartBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysAutoCodePart> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(SysAutoCodePart::getPartId);
        lqw.eq(bo.getRuleId() != null, SysAutoCodePart::getRuleId, bo.getRuleId());
        lqw.eq(bo.getPartIndex() != null, SysAutoCodePart::getPartIndex, bo.getPartIndex());
        lqw.eq(StringUtils.isNotBlank(bo.getPartType()), SysAutoCodePart::getPartType, bo.getPartType());
        lqw.eq(StringUtils.isNotBlank(bo.getPartCode()), SysAutoCodePart::getPartCode, bo.getPartCode());
        lqw.like(StringUtils.isNotBlank(bo.getPartName()), SysAutoCodePart::getPartName, bo.getPartName());
        lqw.eq(bo.getPartLength() != null, SysAutoCodePart::getPartLength, bo.getPartLength());
        lqw.eq(StringUtils.isNotBlank(bo.getDateFormat()), SysAutoCodePart::getDateFormat, bo.getDateFormat());
        lqw.eq(StringUtils.isNotBlank(bo.getInputCharacter()), SysAutoCodePart::getInputCharacter, bo.getInputCharacter());
        lqw.eq(StringUtils.isNotBlank(bo.getFixCharacter()), SysAutoCodePart::getFixCharacter, bo.getFixCharacter());
        lqw.eq(bo.getSeriaStartNo() != null, SysAutoCodePart::getSeriaStartNo, bo.getSeriaStartNo());
        lqw.eq(bo.getSeriaStep() != null, SysAutoCodePart::getSeriaStep, bo.getSeriaStep());
        lqw.eq(bo.getSeriaNowNo() != null, SysAutoCodePart::getSeriaNowNo, bo.getSeriaNowNo());
        lqw.eq(StringUtils.isNotBlank(bo.getCycleFlag()), SysAutoCodePart::getCycleFlag, bo.getCycleFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getCycleMethod()), SysAutoCodePart::getCycleMethod, bo.getCycleMethod());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr1()), SysAutoCodePart::getAttr1, bo.getAttr1());
        lqw.eq(StringUtils.isNotBlank(bo.getAttr2()), SysAutoCodePart::getAttr2, bo.getAttr2());
        lqw.eq(bo.getAttr3() != null, SysAutoCodePart::getAttr3, bo.getAttr3());
        lqw.eq(bo.getAttr4() != null, SysAutoCodePart::getAttr4, bo.getAttr4());
        return lqw;
    }

    /**
     * 新增编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(SysAutoCodePartBo bo) {
        SysAutoCodePart add = MapstructUtils.convert(bo, SysAutoCodePart.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPartId(add.getPartId());
        }
        return flag;
    }

    /**
     * 修改编码生成规则组成
     *
     * @param bo 编码生成规则组成
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(SysAutoCodePartBo bo) {
        SysAutoCodePart update = MapstructUtils.convert(bo, SysAutoCodePart.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysAutoCodePart entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除编码生成规则组成信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
