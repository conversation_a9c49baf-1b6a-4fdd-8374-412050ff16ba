package org.dromara.system.controller.system;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.SysAutoCodeResultVo;
import org.dromara.system.domain.bo.SysAutoCodeResultBo;
import org.dromara.system.service.ISysAutoCodeResultService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 编码生成记录
 * 前端访问路由地址为:/system/autoCodeResult
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/autoCodeResult")
public class SysAutoCodeResultController extends BaseController {

    private final ISysAutoCodeResultService sysAutoCodeResultService;

    /**
     * 查询编码生成记录列表
     */
    @SaCheckPermission("system:autoCodeResult:list")
    @GetMapping("/list")
    public TableDataInfo<SysAutoCodeResultVo> list(SysAutoCodeResultBo bo, PageQuery pageQuery) {
        return sysAutoCodeResultService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成记录列表
     */
    @SaCheckPermission("system:autoCodeResult:export")
    @Log(title = "编码生成记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysAutoCodeResultBo bo, HttpServletResponse response) {
        List<SysAutoCodeResultVo> list = sysAutoCodeResultService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成记录", SysAutoCodeResultVo.class, response);
    }

    /**
     * 获取编码生成记录详细信息
     *
     * @param codeId 主键
     */
    @SaCheckPermission("system:autoCodeResult:query")
    @GetMapping("/{codeId}")
    public R<SysAutoCodeResultVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("codeId") Long codeId) {
        return R.ok(sysAutoCodeResultService.queryById(codeId));
    }

    /**
     * 新增编码生成记录
     */
    @SaCheckPermission("system:autoCodeResult:add")
    @Log(title = "编码生成记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysAutoCodeResultBo bo) {
        return toAjax(sysAutoCodeResultService.insertByBo(bo));
    }

    /**
     * 修改编码生成记录
     */
    @SaCheckPermission("system:autoCodeResult:edit")
    @Log(title = "编码生成记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysAutoCodeResultBo bo) {
        return toAjax(sysAutoCodeResultService.updateByBo(bo));
    }

    /**
     * 删除编码生成记录
     *
     * @param codeIds 主键串
     */
    @SaCheckPermission("system:autoCodeResult:remove")
    @Log(title = "编码生成记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{codeIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("codeIds") Long[] codeIds) {
        return toAjax(sysAutoCodeResultService.deleteWithValidByIds(List.of(codeIds), true));
    }
}
