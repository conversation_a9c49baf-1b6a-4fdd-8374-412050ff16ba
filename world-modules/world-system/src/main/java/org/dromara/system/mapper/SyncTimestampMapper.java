package org.dromara.system.mapper;

import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;
import org.dromara.system.domain.SyncTimestamp;
import org.dromara.system.domain.vo.SyncTimestampVo;

import java.time.LocalDateTime;

/**
 * 任务增量同步日期记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface SyncTimestampMapper extends BaseMapperPlus<SyncTimestamp, SyncTimestampVo> {


    @Select("SELECT last_sync_time FROM sync_timestamp WHERE id = #{id}")
    LocalDateTime getLastSyncTime(Long id);

    @Update("UPDATE sync_timestamp SET last_sync_time = #{time} WHERE id = #{id}")
    void updateLastSyncTime(LocalDateTime time, Long id);
}
