package org.dromara.system.controller.system;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.system.domain.vo.SysAutoCodeRuleVo;
import org.dromara.system.domain.bo.SysAutoCodeRuleBo;
import org.dromara.system.service.ISysAutoCodeRuleService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 编码生成规则
 * 前端访问路由地址为:/system/autocode
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/autocode/rule")
public class SysAutoCodeRuleController extends BaseController {

    private final ISysAutoCodeRuleService sysAutoCodeRuleService;

    /**
     * 查询编码生成规则列表
     */
    @SaCheckPermission("system:autocode:rule:list")
    @GetMapping("/list")
    public TableDataInfo<SysAutoCodeRuleVo> list(SysAutoCodeRuleBo bo, PageQuery pageQuery) {
        return sysAutoCodeRuleService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出编码生成规则列表
     */
    @SaCheckPermission("system:autocode:rule:export")
    @Log(title = "编码生成规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysAutoCodeRuleBo bo, HttpServletResponse response) {
        List<SysAutoCodeRuleVo> list = sysAutoCodeRuleService.queryList(bo);
        ExcelUtil.exportExcel(list, "编码生成规则", SysAutoCodeRuleVo.class, response);
    }

    /**
     * 获取编码生成规则详细信息
     *
     * @param ruleId 主键
     */
    @SaCheckPermission("system:autocode:rule:query")
    @GetMapping("/{ruleId}")
    public R<SysAutoCodeRuleVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable("ruleId") Long ruleId) {
        return R.ok(sysAutoCodeRuleService.queryById(ruleId));
    }

    /**
     * 新增编码生成规则
     */
    @SaCheckPermission("system:autocode:rule:add")
    @Log(title = "编码生成规则", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysAutoCodeRuleBo bo) {
        return toAjax(sysAutoCodeRuleService.insertByBo(bo));
    }

    /**
     * 修改编码生成规则
     */
    @SaCheckPermission("system:autocode:rule:edit")
    @Log(title = "编码生成规则", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysAutoCodeRuleBo bo) {
        return toAjax(sysAutoCodeRuleService.updateByBo(bo));
    }

    /**
     * 删除编码生成规则
     *
     * @param ruleIds 主键串
     */
    @SaCheckPermission("system:autocode:rule:remove")
    @Log(title = "编码生成规则", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable("ruleIds") Long[] ruleIds) {
        return toAjax(sysAutoCodeRuleService.deleteWithValidByIds(List.of(ruleIds), true));
    }
}
