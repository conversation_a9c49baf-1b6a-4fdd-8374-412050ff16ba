package org.dromara.system.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.domain.SysAutoCodeResult;

import java.io.Serial;
import java.io.Serializable;


/**
 * 编码生成记录视图对象 sys_auto_code_result
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = SysAutoCodeResult.class)
public class SysAutoCodeResultVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @ExcelProperty(value = "记录ID")
    private Long codeId;

    /**
     * 规则ID
     */
    @ExcelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 生成日期时间
     */
    @ExcelProperty(value = "生成日期时间")
    private String genDate;

    /**
     * 最后产生的序号
     */
    @ExcelProperty(value = "最后产生的序号")
    private Long genIndex;

    /**
     * 最后产生的值
     */
    @ExcelProperty(value = "最后产生的值")
    private String lastResult;

    /**
     * 最后产生的流水号
     */
    @ExcelProperty(value = "最后产生的流水号")
    private Integer lastSerialNo;

    /**
     * 最后传入的参数
     */
    @ExcelProperty(value = "最后传入的参数")
    private String lastInputChar;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 预留字段1
     */
    @ExcelProperty(value = "预留字段1")
    private String attr1;

    /**
     * 预留字段2
     */
    @ExcelProperty(value = "预留字段2")
    private String attr2;

    /**
     * 预留字段3
     */
    @ExcelProperty(value = "预留字段3")
    private Long attr3;

    /**
     * 预留字段4
     */
    @ExcelProperty(value = "预留字段4")
    private Long attr4;


}
