package org.dromara.system.domain.vo;

import java.io.Serial;
import java.io.Serializable;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 用户对象导入VO
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
public class SysUserUpdateVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工号
     */
    @ExcelProperty(value = "工号")
    private String userName;

    /**
     * 岗位名称
     */
    @ExcelProperty(value = "岗位名称")
    private String postName;

}
