package org.dromara.system.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.system.domain.bo.EmployeeInfoBo;
import org.dromara.system.domain.vo.EmployeeInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * OA员工数据Service接口
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
public interface IEmployeeInfoService {

    /**
     * 查询OA员工数据
     *
     * @param ID 主键
     * @return OA员工数据
     */
    EmployeeInfoVo queryById(Long ID);

    /**
     * 分页查询OA员工数据列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return OA员工数据分页列表
     */
    TableDataInfo<EmployeeInfoVo> queryPageList(EmployeeInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的OA员工数据列表
     *
     * @param bo 查询条件
     * @return OA员工数据列表
     */
    List<EmployeeInfoVo> queryList(EmployeeInfoBo bo);

    /**
     * 新增OA员工数据
     *
     * @param bo OA员工数据
     * @return 是否新增成功
     */
    Boolean insertByBo(EmployeeInfoBo bo);

    /**
     * 修改OA员工数据
     *
     * @param bo OA员工数据
     * @return 是否修改成功
     */
    Boolean updateByBo(EmployeeInfoBo bo);

    /**
     * 校验并批量删除OA员工数据信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


}
