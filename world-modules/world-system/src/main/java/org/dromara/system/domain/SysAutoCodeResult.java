package org.dromara.system.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 编码生成记录对象 sys_auto_code_result
 *
 * <AUTHOR>
 * @date 2025-04-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_auto_code_result")
public class SysAutoCodeResult extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "code_id")
    private Long codeId;

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 生成日期时间
     */
    private String genDate;

    /**
     * 最后产生的序号
     */
    private Integer genIndex;

    /**
     * 最后产生的值
     */
    private String lastResult;

    /**
     * 最后产生的流水号
     */
    private Integer lastSerialNo;

    /**
     * 最后传入的参数
     */
    private String lastInputChar;

    /**
     * 备注
     */
    private String remark;

    /**
     * 预留字段1
     */
    private String attr1;

    /**
     * 预留字段2
     */
    private String attr2;

    /**
     * 预留字段3
     */
    private Long attr3;

    /**
     * 预留字段4
     */
    private Long attr4;


}
